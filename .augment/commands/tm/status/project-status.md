Enhanced status command with comprehensive project insights.

Arguments: $ARGUMENTS

## Intelligent Status Overview

### 1. **Executive Summary**
Quick dashboard view:
- 🏃 Active work (in-progress tasks)
- 📊 Progress metrics (% complete, velocity)
- 🚧 Blockers and risks
- ⏱️ Time analysis (estimated vs actual)
- 🎯 Sprint/milestone progress

### 2. **Contextual Analysis**

Based on $ARGUMENTS, focus on:
- "sprint" → Current sprint progress and burndown
- "blocked" → Dependency chains and resolution paths
- "team" → Task distribution and workload
- "timeline" → Schedule adherence and projections
- "risk" → High complexity or overdue items

### 3. **Smart Insights**

**Workflow Health:**
- Idle tasks (in-progress > 24h without updates)
- Bottlenecks (multiple tasks waiting on same dependency)
- Quick wins (low complexity, high impact)

**Predictive Analytics:**
- Completion projections based on velocity
- Risk of missing deadlines
- Recommended task order for optimal flow

### 4. **Visual Intelligence**

Dynamic visualization based on data:
```
Sprint Progress: ████████░░ 80% (16/20 tasks)
Velocity Trend: ↗️ +15% this week
Blocked Tasks:  🔴 3 critical path items

Priority Distribution:
High:   ████████ 8 tasks (2 blocked)
Medium: ████░░░░ 4 tasks
Low:    ██░░░░░░ 2 tasks
```

### 5. **Actionable Recommendations**

Based on analysis:
1. **Immediate actions** (unblock critical path)
2. **Today's focus** (optimal task sequence)
3. **Process improvements** (recurring patterns)
4. **Resource needs** (skills, time, dependencies)

### 6. **Historical Context**

Compare to previous periods:
- Velocity changes
- Pattern recognition
- Improvement areas
- Success patterns to repeat