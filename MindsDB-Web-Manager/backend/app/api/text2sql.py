"""
Text2SQL API - 任务#118
实现多表Text2SQL技能创建功能
与增强版技能管理API集成，支持技能生命周期管理
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import logging
import time
import json
import asyncio
from datetime import datetime

# 导入核心模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.mindsdb_client import get_mindsdb_client
    from core.api_response import APIResponseBuilder
    from core.exception_handlers import BusinessException
    from utils.unicode_handler import unicode_handler
    from api.enhanced_skills import create_skill, SkillCreateRequest
except ImportError as e:
    logging.warning(f"导入模块失败: {e}")
    
    # 提供模拟实现
    def get_mindsdb_client():
        return None
    
    class MockAPIResponseBuilder:
        @staticmethod
        def success(data=None, message=""):
            return {"success": True, "data": data, "message": message}
        
        @staticmethod
        def error(message="", error_code="", details=None):
            return {"success": False, "message": message, "error_code": error_code, "details": details}
    
    class MockBusinessException(Exception):
        pass
    
    class MockUnicodeHandler:
        def decode_response_data(self, data):
            return data
    
    APIResponseBuilder = MockAPIResponseBuilder
    BusinessException = MockBusinessException
    unicode_handler = MockUnicodeHandler()
    
    # 模拟技能创建函数
    async def create_skill(skill_data):
        return {"success": True, "data": {"name": skill_data.get("name", "test_skill")}}
    
    class SkillCreateRequest:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/text2sql", tags=["text2sql"])

# 功能开关检查
def check_text2sql_features_enabled():
    """检查Text2SQL功能是否启用"""
    agent_skills_real = os.getenv("FEATURE_AGENT_SKILLS_REAL", "false").lower() == "true"
    intelligent_text2sql = os.getenv("FEATURE_INTELLIGENT_TEXT2SQL", "false").lower() == "true"
    
    if not (agent_skills_real and intelligent_text2sql):
        raise HTTPException(
            status_code=503,
            detail="Text2SQL功能未启用，请联系管理员启用相关功能开关"
        )

# 请求模型
class MultiTableSkillRequest(BaseModel):
    """多表Text2SQL技能创建请求模型"""
    tables: List[str] = Field(..., description="表列表", min_items=1)
    database: str = Field(..., description="数据库名称")
    skill_name: Optional[str] = Field(None, description="技能名称（可选，如果不提供会自动生成）")
    description: Optional[str] = Field(None, description="技能描述（可选）")
    validate_tables: bool = Field(default=True, description="是否验证表存在性")

class SingleTableSkillRequest(BaseModel):
    """单表Text2SQL技能创建请求模型"""
    table: str = Field(..., description="表名称")
    database: str = Field(..., description="数据库名称")
    skill_name: Optional[str] = Field(None, description="技能名称（可选）")
    description: Optional[str] = Field(None, description="技能描述（可选）")

# 核心函数实现 - 任务#118的关键功能
async def create_multi_table_skill(tables: List[str], database: str, skill_name: Optional[str] = None, description: Optional[str] = None) -> Dict[str, Any]:
    """
    创建多表Text2SQL技能 - 任务#118核心函数
    
    Args:
        tables: 表列表
        database: 数据库名称
        skill_name: 技能名称（可选）
        description: 技能描述（可选）
        
    Returns:
        Dict包含创建结果和技能信息
        
    Raises:
        BusinessException: 当创建失败时
    """
    try:
        logger.info(f"创建多表Text2SQL技能: {database}, 表: {tables}")
        
        if not tables:
            raise BusinessException("表列表不能为空")
        
        if not database:
            raise BusinessException("数据库名称不能为空")
        
        start_time = time.time()
        
        # 1. 获取MindsDB客户端
        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")
        
        # 2. 验证数据库是否存在
        try:
            databases_result = client.execute_query("SHOW DATABASES")
            available_databases = []
            
            if databases_result and 'data' in databases_result:
                for row in databases_result['data']:
                    decoded_row = unicode_handler.decode_response_data(row)
                    if isinstance(decoded_row, list) and len(decoded_row) > 0:
                        available_databases.append(str(decoded_row[0]))
            
            if database not in available_databases:
                raise BusinessException(f"数据库 '{database}' 不存在")
                
        except Exception as e:
            logger.warning(f"验证数据库失败: {e}")
        
        # 3. 验证表是否存在
        invalid_tables = []
        try:
            tables_result = client.execute_query(f"SHOW TABLES FROM {database}")
            available_tables = []
            
            if tables_result and 'data' in tables_result:
                for row in tables_result['data']:
                    decoded_row = unicode_handler.decode_response_data(row)
                    if isinstance(decoded_row, list) and len(decoded_row) > 0:
                        available_tables.append(str(decoded_row[0]))
            
            # 检查每个表是否存在
            for table in tables:
                if table not in available_tables:
                    invalid_tables.append(table)
            
            if invalid_tables:
                raise BusinessException(f"以下表不存在: {', '.join(invalid_tables)}")
                
        except BusinessException:
            raise
        except Exception as e:
            logger.warning(f"验证表失败: {e}")
        
        # 4. 生成技能名称（如果未提供）
        if not skill_name:
            if len(tables) == 1:
                skill_name = f"text2sql_{database}_{tables[0]}_skill"
            else:
                skill_name = f"text2sql_{database}_multi_table_skill"
        
        # 5. 生成技能描述（如果未提供）
        if not description:
            if len(tables) == 1:
                description = f"Text2SQL技能，用于查询{database}数据库的{tables[0]}表"
            else:
                description = f"Multi-table data analysis - Text2SQL技能，用于查询{database}数据库的{len(tables)}个表"
        
        # 6. 格式化表列表为MindsDB所需的格式
        # 确保tables参数正确格式化为字符串列表
        tables_json = json.dumps(tables, ensure_ascii=False)
        
        # 7. 构建CREATE SKILL SQL语句 - 任务#118要求的格式
        create_skill_sql = f"""CREATE SKILL {skill_name} 
USING type = 'text2sql', 
      database = '{database}', 
      tables = '{tables_json}', 
      description = '{description.replace("'", "''")}'"""
        
        logger.info(f"执行CREATE SKILL SQL: {create_skill_sql}")
        
        # 8. 执行CREATE SKILL语句
        result = client.execute_query(create_skill_sql)
        
        if not result:
            raise BusinessException("CREATE SKILL语句执行失败，未返回结果")
        
        execution_time = time.time() - start_time
        
        # 9. 构建返回结果
        skill_info = {
            'skill_name': skill_name,
            'database': database,
            'tables': tables,
            'table_count': len(tables),
            'description': description,
            'skill_type': 'text2sql',
            'created_at': datetime.now().isoformat(),
            'execution_time': execution_time,
            'sql_statement': create_skill_sql,
            'mindsdb_result': result,
            'parameters': {
                'type': 'text2sql',
                'database': database,
                'tables': tables,
                'multi_table': len(tables) > 1,
                'validated_tables': len(tables) - len(invalid_tables),
                'invalid_tables': invalid_tables
            }
        }
        
        logger.info(f"多表Text2SQL技能创建成功: {skill_name}, 耗时: {execution_time:.3f}秒")
        
        return {
            'success': True,
            'data': skill_info,
            'message': f"多表Text2SQL技能 '{skill_name}' 创建成功"
        }
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"创建多表Text2SQL技能失败: {str(e)}")
        raise BusinessException(f"创建多表Text2SQL技能失败: {str(e)}")

# 与增强版技能管理API集成的函数
async def create_multi_table_skill_with_lifecycle(tables: List[str], database: str, skill_name: Optional[str] = None, description: Optional[str] = None) -> Dict[str, Any]:
    """
    创建多表Text2SQL技能并集成技能生命周期管理
    与任务#114的增强版技能管理API集成
    
    Args:
        tables: 表列表
        database: 数据库名称
        skill_name: 技能名称（可选）
        description: 技能描述（可选）
        
    Returns:
        Dict包含创建结果和技能管理信息
    """
    try:
        logger.info(f"创建多表Text2SQL技能（集成生命周期管理）: {database}, 表: {tables}")
        
        # 1. 生成技能名称（如果未提供）
        if not skill_name:
            if len(tables) == 1:
                skill_name = f"text2sql_{database}_{tables[0]}_skill"
            else:
                skill_name = f"text2sql_{database}_multi_table_skill"
        
        # 2. 生成技能描述（如果未提供）
        if not description:
            if len(tables) == 1:
                description = f"Text2SQL技能，用于查询{database}数据库的{tables[0]}表"
            else:
                description = f"Multi-table data analysis - Text2SQL技能，用于查询{database}数据库的{len(tables)}个表"
        
        # 3. 使用增强版技能管理API创建技能
        skill_create_request = SkillCreateRequest(
            name=skill_name,
            skill_type='text2sql',
            database=database,
            tables=tables,
            description=description,
            parameters={
                'multi_table': len(tables) > 1,
                'table_count': len(tables),
                'schema_analysis': True,
                'query_optimization': True,
                'chinese_support': True
            }
        )
        
        # 4. 调用增强版技能创建API
        enhanced_result = await create_skill(skill_create_request)
        
        # 5. 构建集成结果
        integrated_result = {
            'success': True,
            'data': {
                'skill_name': skill_name,
                'database': database,
                'tables': tables,
                'table_count': len(tables),
                'description': description,
                'skill_type': 'text2sql',
                'created_at': datetime.now().isoformat(),
                'lifecycle_managed': True,
                'enhanced_api_result': enhanced_result,
                'integration_method': 'enhanced_skills_api',
                'parameters': skill_create_request.parameters if hasattr(skill_create_request, 'parameters') else {}
            },
            'message': f"多表Text2SQL技能 '{skill_name}' 创建成功（集成生命周期管理）"
        }
        
        logger.info(f"多表Text2SQL技能创建成功（集成生命周期管理）: {skill_name}")
        
        return integrated_result
        
    except Exception as e:
        logger.error(f"创建多表Text2SQL技能（集成生命周期管理）失败: {str(e)}")
        raise BusinessException(f"创建多表Text2SQL技能（集成生命周期管理）失败: {str(e)}")

# API端点实现
@router.post("/create-multi-table-skill")
async def create_multi_table_skill_endpoint(
    request: MultiTableSkillRequest,
    _: None = Depends(check_text2sql_features_enabled)
):
    """
    创建多表Text2SQL技能 - 任务#118主要端点
    """
    try:
        logger.info(f"收到多表Text2SQL技能创建请求: {request.database}, 表: {request.tables}")
        
        # 验证请求参数
        if not request.tables:
            raise HTTPException(status_code=400, detail="表列表不能为空")
        
        if not request.database:
            raise HTTPException(status_code=400, detail="数据库名称不能为空")
        
        # 调用核心创建函数
        result = await create_multi_table_skill(
            tables=request.tables,
            database=request.database,
            skill_name=request.skill_name,
            description=request.description
        )
        
        return APIResponseBuilder.success(
            data=result['data'],
            message=result['message']
        ).dict()
        
    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"多表Text2SQL技能创建业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"多表Text2SQL技能创建错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/create-multi-table-skill-managed")
async def create_multi_table_skill_managed_endpoint(
    request: MultiTableSkillRequest,
    _: None = Depends(check_text2sql_features_enabled)
):
    """
    创建多表Text2SQL技能（集成生命周期管理）- 任务#118扩展端点
    """
    try:
        logger.info(f"收到多表Text2SQL技能创建请求（生命周期管理）: {request.database}, 表: {request.tables}")
        
        # 验证请求参数
        if not request.tables:
            raise HTTPException(status_code=400, detail="表列表不能为空")
        
        if not request.database:
            raise HTTPException(status_code=400, detail="数据库名称不能为空")
        
        # 调用集成生命周期管理的创建函数
        result = await create_multi_table_skill_with_lifecycle(
            tables=request.tables,
            database=request.database,
            skill_name=request.skill_name,
            description=request.description
        )
        
        return APIResponseBuilder.success(
            data=result['data'],
            message=result['message']
        ).dict()
        
    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"多表Text2SQL技能创建（生命周期管理）业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"多表Text2SQL技能创建（生命周期管理）错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/create-single-table-skill")
async def create_single_table_skill_endpoint(
    request: SingleTableSkillRequest,
    _: None = Depends(check_text2sql_features_enabled)
):
    """
    创建单表Text2SQL技能 - 便捷端点
    """
    try:
        logger.info(f"收到单表Text2SQL技能创建请求: {request.database}.{request.table}")

        # 转换为多表请求格式
        multi_table_request = MultiTableSkillRequest(
            tables=[request.table],
            database=request.database,
            skill_name=request.skill_name,
            description=request.description
        )

        # 调用多表创建函数
        result = await create_multi_table_skill(
            tables=multi_table_request.tables,
            database=multi_table_request.database,
            skill_name=multi_table_request.skill_name,
            description=multi_table_request.description
        )

        return APIResponseBuilder.success(
            data=result['data'],
            message=result['message']
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"单表Text2SQL技能创建业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"单表Text2SQL技能创建错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/validate-tables/{database}")
async def validate_tables_endpoint(
    database: str,
    tables: str = Field(..., description="逗号分隔的表名列表"),
    _: None = Depends(check_text2sql_features_enabled)
):
    """
    验证表是否存在 - 辅助端点
    """
    try:
        logger.info(f"验证表存在性: {database}, 表: {tables}")

        # 解析表列表
        table_list = [table.strip() for table in tables.split(',') if table.strip()]

        if not table_list:
            raise HTTPException(status_code=400, detail="表列表不能为空")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 验证数据库是否存在
        databases_result = client.execute_query("SHOW DATABASES")
        available_databases = []

        if databases_result and 'data' in databases_result:
            for row in databases_result['data']:
                decoded_row = unicode_handler.decode_response_data(row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    available_databases.append(str(decoded_row[0]))

        database_exists = database in available_databases

        # 验证表是否存在
        valid_tables = []
        invalid_tables = []

        if database_exists:
            try:
                tables_result = client.execute_query(f"SHOW TABLES FROM {database}")
                available_tables = []

                if tables_result and 'data' in tables_result:
                    for row in tables_result['data']:
                        decoded_row = unicode_handler.decode_response_data(row)
                        if isinstance(decoded_row, list) and len(decoded_row) > 0:
                            available_tables.append(str(decoded_row[0]))

                # 分类表
                for table in table_list:
                    if table in available_tables:
                        valid_tables.append(table)
                    else:
                        invalid_tables.append(table)

            except Exception as e:
                logger.warning(f"获取表列表失败: {e}")
                invalid_tables = table_list
        else:
            invalid_tables = table_list

        # 构建验证结果
        validation_result = {
            'database': database,
            'database_exists': database_exists,
            'requested_tables': table_list,
            'valid_tables': valid_tables,
            'invalid_tables': invalid_tables,
            'total_requested': len(table_list),
            'total_valid': len(valid_tables),
            'total_invalid': len(invalid_tables),
            'validation_success': len(invalid_tables) == 0,
            'validated_at': datetime.now().isoformat()
        }

        return APIResponseBuilder.success(
            data=validation_result,
            message=f"表验证完成，有效: {len(valid_tables)}, 无效: {len(invalid_tables)}"
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"表验证业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"表验证错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/list-skills/{database}")
async def list_database_skills_endpoint(
    database: str,
    skill_type: str = "text2sql",
    _: None = Depends(check_text2sql_features_enabled)
):
    """
    列出数据库相关的Text2SQL技能 - 查询端点
    """
    try:
        logger.info(f"列出数据库技能: {database}, 类型: {skill_type}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 获取所有技能
        skills_result = client.execute_query("SHOW SKILLS")

        if not skills_result or 'data' not in skills_result:
            return APIResponseBuilder.success(
                data={
                    'database': database,
                    'skill_type': skill_type,
                    'skills': [],
                    'total_count': 0
                },
                message="未找到任何技能"
            ).dict()

        # 解析技能列表
        database_skills = []
        column_names = skills_result.get('column_names', [])

        for skill_row in skills_result['data']:
            decoded_row = unicode_handler.decode_response_data(skill_row)

            skill_dict = {}
            for i, value in enumerate(decoded_row):
                if i < len(column_names):
                    key = column_names[i].lower()
                    skill_dict[key] = value

            # 过滤数据库相关的技能
            skill_database = skill_dict.get('database', '')
            skill_skill_type = skill_dict.get('type', '')

            if skill_database == database and skill_skill_type == skill_type:
                # 格式化技能信息
                formatted_skill = {
                    'name': skill_dict.get('name', ''),
                    'type': skill_dict.get('type', ''),
                    'database': skill_dict.get('database', ''),
                    'description': skill_dict.get('description', ''),
                    'tables': skill_dict.get('tables', []),
                    'created_at': skill_dict.get('created_at'),
                    'updated_at': skill_dict.get('updated_at'),
                    'parameters': {}
                }

                # 解析参数
                if skill_dict.get('parameters'):
                    try:
                        if isinstance(skill_dict['parameters'], str):
                            formatted_skill['parameters'] = json.loads(skill_dict['parameters'])
                        else:
                            formatted_skill['parameters'] = skill_dict['parameters']
                    except (json.JSONDecodeError, TypeError):
                        formatted_skill['parameters'] = {}

                # 解析表列表
                if skill_dict.get('tables'):
                    try:
                        if isinstance(skill_dict['tables'], str):
                            formatted_skill['tables'] = json.loads(skill_dict['tables'])
                        else:
                            formatted_skill['tables'] = skill_dict['tables']
                    except (json.JSONDecodeError, TypeError):
                        formatted_skill['tables'] = []

                database_skills.append(formatted_skill)

        # 构建结果
        result_data = {
            'database': database,
            'skill_type': skill_type,
            'skills': database_skills,
            'total_count': len(database_skills),
            'retrieved_at': datetime.now().isoformat()
        }

        return APIResponseBuilder.success(
            data=result_data,
            message=f"成功获取数据库 {database} 的 {len(database_skills)} 个 {skill_type} 技能"
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"列出数据库技能业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"列出数据库技能错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.delete("/delete-skill/{skill_name}")
async def delete_skill_endpoint(
    skill_name: str,
    _: None = Depends(check_text2sql_features_enabled)
):
    """
    删除Text2SQL技能 - 管理端点
    """
    try:
        logger.info(f"删除Text2SQL技能: {skill_name}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 检查技能是否存在
        skills_result = client.execute_query("SHOW SKILLS")
        skill_exists = False

        if skills_result and 'data' in skills_result:
            for skill_row in skills_result['data']:
                decoded_row = unicode_handler.decode_response_data(skill_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    if str(decoded_row[0]) == skill_name:
                        skill_exists = True
                        break

        if not skill_exists:
            raise HTTPException(status_code=404, detail=f"技能 '{skill_name}' 不存在")

        # 执行删除
        delete_sql = f"DROP SKILL {skill_name}"
        result = client.execute_query(delete_sql)

        delete_info = {
            'skill_name': skill_name,
            'deleted_at': datetime.now().isoformat(),
            'sql_statement': delete_sql,
            'mindsdb_result': result
        }

        return APIResponseBuilder.success(
            data=delete_info,
            message=f"技能 '{skill_name}' 删除成功"
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"删除技能业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"删除技能错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
