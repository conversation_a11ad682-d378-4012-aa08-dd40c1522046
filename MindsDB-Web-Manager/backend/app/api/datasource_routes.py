"""
数据源管理API路由
提供数据源的CRUD操作和连接测试功能
"""

import logging
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field, validator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/datasources", tags=["DataSources"])

class DataSourceConfig(BaseModel):
    """数据源配置基类"""
    pass

class PostgreSQLConfig(DataSourceConfig):
    """PostgreSQL配置"""
    host: str = Field(..., description="主机地址")
    port: int = Field(5432, ge=1, le=65535, description="端口号")
    database: str = Field(..., description="数据库名")
    username: str = Field(..., description="用户名")
    password: Optional[str] = Field(None, description="密码")
    ssl: bool = Field(False, description="是否启用SSL")
    connection_timeout: int = Field(30, ge=5, le=300, description="连接超时（秒）")

class SSHConfig(DataSourceConfig):
    """SSH配置"""
    host: str = Field(..., description="SSH主机地址")
    port: int = Field(22, ge=1, le=65535, description="SSH端口")
    username: str = Field(..., description="用户名")
    auth_method: str = Field("password", regex="^(password|key)$", description="认证方式")
    password: Optional[str] = Field(None, description="密码")
    key_path: Optional[str] = Field(None, description="私钥路径")
    passphrase: Optional[str] = Field(None, description="私钥密码")
    connection_timeout: int = Field(30, ge=5, le=300, description="连接超时（秒）")
    
    @validator('key_path')
    def validate_key_path(cls, v, values):
        if values.get('auth_method') == 'key' and not v:
            raise ValueError('使用密钥认证时必须提供私钥路径')
        return v

class DataSourceCreate(BaseModel):
    """创建数据源请求"""
    name: str = Field(..., min_length=1, max_length=100, description="数据源名称")
    type: str = Field(..., regex="^(postgresql|ssh)$", description="数据源类型")
    description: Optional[str] = Field(None, max_length=500, description="描述")
    config: Dict[str, Any] = Field(default_factory=dict, description="配置参数")

class DataSourceUpdate(BaseModel):
    """更新数据源请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="数据源名称")
    description: Optional[str] = Field(None, max_length=500, description="描述")
    config: Optional[Dict[str, Any]] = Field(None, description="配置参数")

class DataSourceResponse(BaseModel):
    """数据源响应"""
    id: int
    name: str
    type: str
    description: Optional[str]
    config: Dict[str, Any]
    status: str
    last_tested: Optional[datetime]
    created_at: datetime
    updated_at: datetime

class ConnectionTestResult(BaseModel):
    """连接测试结果"""
    success: bool
    message: str
    details: Optional[Dict[str, Any]] = None
    tested_at: datetime

# 模拟数据存储
datasources_db = {}
next_id = 1

def get_next_id():
    """获取下一个ID"""
    global next_id
    current_id = next_id
    next_id += 1
    return current_id

@router.get("/", response_model=List[DataSourceResponse])
async def list_datasources():
    """获取所有数据源"""
    try:
        datasources = []
        for ds_id, ds_data in datasources_db.items():
            # 隐藏敏感信息
            config = ds_data['config'].copy()
            if 'password' in config:
                config['password'] = '***'
            if 'passphrase' in config:
                config['passphrase'] = '***'
            
            datasources.append(DataSourceResponse(
                id=ds_id,
                name=ds_data['name'],
                type=ds_data['type'],
                description=ds_data['description'],
                config=config,
                status=ds_data['status'],
                last_tested=ds_data['last_tested'],
                created_at=ds_data['created_at'],
                updated_at=ds_data['updated_at']
            ))
        
        return datasources
        
    except Exception as e:
        logger.error(f"获取数据源列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据源列表失败")

@router.get("/{datasource_id}", response_model=DataSourceResponse)
async def get_datasource(datasource_id: int):
    """获取指定数据源"""
    if datasource_id not in datasources_db:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    try:
        ds_data = datasources_db[datasource_id]
        
        # 隐藏敏感信息
        config = ds_data['config'].copy()
        if 'password' in config:
            config['password'] = '***'
        if 'passphrase' in config:
            config['passphrase'] = '***'
        
        return DataSourceResponse(
            id=datasource_id,
            name=ds_data['name'],
            type=ds_data['type'],
            description=ds_data['description'],
            config=config,
            status=ds_data['status'],
            last_tested=ds_data['last_tested'],
            created_at=ds_data['created_at'],
            updated_at=ds_data['updated_at']
        )
        
    except Exception as e:
        logger.error(f"获取数据源失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据源失败")

@router.post("/", response_model=DataSourceResponse)
async def create_datasource(datasource: DataSourceCreate):
    """创建新数据源"""
    try:
        # 验证名称唯一性
        for ds_data in datasources_db.values():
            if ds_data['name'] == datasource.name:
                raise HTTPException(status_code=400, detail="数据源名称已存在")
        
        # 验证配置
        if datasource.type == 'postgresql':
            try:
                PostgreSQLConfig(**datasource.config)
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"PostgreSQL配置无效: {str(e)}")
        elif datasource.type == 'ssh':
            try:
                SSHConfig(**datasource.config)
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"SSH配置无效: {str(e)}")
        
        # 创建数据源
        ds_id = get_next_id()
        now = datetime.now()
        
        ds_data = {
            'name': datasource.name,
            'type': datasource.type,
            'description': datasource.description,
            'config': datasource.config,
            'status': 'disconnected',
            'last_tested': None,
            'created_at': now,
            'updated_at': now
        }
        
        datasources_db[ds_id] = ds_data
        
        # 隐藏敏感信息
        config = ds_data['config'].copy()
        if 'password' in config:
            config['password'] = '***'
        if 'passphrase' in config:
            config['passphrase'] = '***'
        
        return DataSourceResponse(
            id=ds_id,
            name=ds_data['name'],
            type=ds_data['type'],
            description=ds_data['description'],
            config=config,
            status=ds_data['status'],
            last_tested=ds_data['last_tested'],
            created_at=ds_data['created_at'],
            updated_at=ds_data['updated_at']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建数据源失败: {e}")
        raise HTTPException(status_code=500, detail="创建数据源失败")

@router.put("/{datasource_id}", response_model=DataSourceResponse)
async def update_datasource(datasource_id: int, datasource: DataSourceUpdate):
    """更新数据源"""
    if datasource_id not in datasources_db:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    try:
        ds_data = datasources_db[datasource_id]
        
        # 验证名称唯一性
        if datasource.name and datasource.name != ds_data['name']:
            for other_id, other_data in datasources_db.items():
                if other_id != datasource_id and other_data['name'] == datasource.name:
                    raise HTTPException(status_code=400, detail="数据源名称已存在")
        
        # 更新字段
        if datasource.name is not None:
            ds_data['name'] = datasource.name
        if datasource.description is not None:
            ds_data['description'] = datasource.description
        if datasource.config is not None:
            # 验证配置
            if ds_data['type'] == 'postgresql':
                try:
                    PostgreSQLConfig(**datasource.config)
                except Exception as e:
                    raise HTTPException(status_code=400, detail=f"PostgreSQL配置无效: {str(e)}")
            elif ds_data['type'] == 'ssh':
                try:
                    SSHConfig(**datasource.config)
                except Exception as e:
                    raise HTTPException(status_code=400, detail=f"SSH配置无效: {str(e)}")
            
            ds_data['config'] = datasource.config
        
        ds_data['updated_at'] = datetime.now()
        
        # 隐藏敏感信息
        config = ds_data['config'].copy()
        if 'password' in config:
            config['password'] = '***'
        if 'passphrase' in config:
            config['passphrase'] = '***'
        
        return DataSourceResponse(
            id=datasource_id,
            name=ds_data['name'],
            type=ds_data['type'],
            description=ds_data['description'],
            config=config,
            status=ds_data['status'],
            last_tested=ds_data['last_tested'],
            created_at=ds_data['created_at'],
            updated_at=ds_data['updated_at']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新数据源失败: {e}")
        raise HTTPException(status_code=500, detail="更新数据源失败")

@router.delete("/{datasource_id}")
async def delete_datasource(datasource_id: int):
    """删除数据源"""
    if datasource_id not in datasources_db:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    try:
        ds_data = datasources_db[datasource_id]
        del datasources_db[datasource_id]
        
        return {
            "success": True,
            "message": f"数据源 '{ds_data['name']}' 删除成功",
            "deleted_id": datasource_id
        }
        
    except Exception as e:
        logger.error(f"删除数据源失败: {e}")
        raise HTTPException(status_code=500, detail="删除数据源失败")

@router.post("/{datasource_id}/test", response_model=ConnectionTestResult)
async def test_datasource_connection(datasource_id: int, background_tasks: BackgroundTasks):
    """测试数据源连接"""
    if datasource_id not in datasources_db:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    try:
        ds_data = datasources_db[datasource_id]
        
        # 更新状态为测试中
        ds_data['status'] = 'testing'
        
        # 模拟连接测试
        if ds_data['type'] == 'postgresql':
            result = await test_postgresql_connection(ds_data['config'])
        elif ds_data['type'] == 'ssh':
            result = await test_ssh_connection(ds_data['config'])
        else:
            raise HTTPException(status_code=400, detail="不支持的数据源类型")
        
        # 更新状态和测试时间
        ds_data['status'] = 'connected' if result.success else 'disconnected'
        ds_data['last_tested'] = result.tested_at
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试连接失败: {e}")
        # 更新状态为断开
        if datasource_id in datasources_db:
            datasources_db[datasource_id]['status'] = 'disconnected'
        raise HTTPException(status_code=500, detail="测试连接失败")

async def test_postgresql_connection(config: Dict[str, Any]) -> ConnectionTestResult:
    """测试PostgreSQL连接"""
    try:
        # 模拟连接测试
        await asyncio.sleep(1)  # 模拟连接时间
        
        # 简单的配置验证
        required_fields = ['host', 'port', 'database', 'username']
        for field in required_fields:
            if field not in config or not config[field]:
                return ConnectionTestResult(
                    success=False,
                    message=f"缺少必需的配置字段: {field}",
                    tested_at=datetime.now()
                )
        
        # 模拟连接成功/失败
        import random
        success = random.random() > 0.2  # 80%成功率
        
        if success:
            return ConnectionTestResult(
                success=True,
                message="PostgreSQL连接测试成功",
                details={
                    "host": config['host'],
                    "port": config['port'],
                    "database": config['database'],
                    "ssl": config.get('ssl', False)
                },
                tested_at=datetime.now()
            )
        else:
            return ConnectionTestResult(
                success=False,
                message="连接超时或认证失败",
                details={"error_code": "CONNECTION_FAILED"},
                tested_at=datetime.now()
            )
            
    except Exception as e:
        return ConnectionTestResult(
            success=False,
            message=f"连接测试异常: {str(e)}",
            tested_at=datetime.now()
        )

async def test_ssh_connection(config: Dict[str, Any]) -> ConnectionTestResult:
    """测试SSH连接"""
    try:
        # 模拟连接测试
        await asyncio.sleep(1.5)  # 模拟连接时间
        
        # 简单的配置验证
        required_fields = ['host', 'port', 'username']
        for field in required_fields:
            if field not in config or not config[field]:
                return ConnectionTestResult(
                    success=False,
                    message=f"缺少必需的配置字段: {field}",
                    tested_at=datetime.now()
                )
        
        # 验证认证方式
        auth_method = config.get('auth_method', 'password')
        if auth_method == 'password' and not config.get('password'):
            return ConnectionTestResult(
                success=False,
                message="密码认证方式需要提供密码",
                tested_at=datetime.now()
            )
        elif auth_method == 'key' and not config.get('key_path'):
            return ConnectionTestResult(
                success=False,
                message="密钥认证方式需要提供私钥路径",
                tested_at=datetime.now()
            )
        
        # 模拟连接成功/失败
        import random
        success = random.random() > 0.3  # 70%成功率
        
        if success:
            return ConnectionTestResult(
                success=True,
                message="SSH连接测试成功",
                details={
                    "host": config['host'],
                    "port": config['port'],
                    "username": config['username'],
                    "auth_method": auth_method
                },
                tested_at=datetime.now()
            )
        else:
            return ConnectionTestResult(
                success=False,
                message="SSH连接失败或认证失败",
                details={"error_code": "SSH_CONNECTION_FAILED"},
                tested_at=datetime.now()
            )
            
    except Exception as e:
        return ConnectionTestResult(
            success=False,
            message=f"SSH连接测试异常: {str(e)}",
            tested_at=datetime.now()
        )

@router.get("/{datasource_id}/status")
async def get_datasource_status(datasource_id: int):
    """获取数据源状态"""
    if datasource_id not in datasources_db:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    ds_data = datasources_db[datasource_id]
    
    return {
        "id": datasource_id,
        "name": ds_data['name'],
        "status": ds_data['status'],
        "last_tested": ds_data['last_tested']
    }

@router.get("/types/supported")
async def get_supported_types():
    """获取支持的数据源类型"""
    return {
        "types": [
            {
                "type": "postgresql",
                "name": "PostgreSQL",
                "description": "PostgreSQL数据库连接",
                "icon": "🐘"
            },
            {
                "type": "ssh",
                "name": "SSH隧道",
                "description": "SSH隧道连接",
                "icon": "🔐"
            }
        ]
    }

# 初始化一些示例数据
def init_sample_data():
    """初始化示例数据"""
    global next_id
    
    sample_datasources = [
        {
            "name": "生产数据库",
            "type": "postgresql",
            "description": "主要的生产环境PostgreSQL数据库",
            "config": {
                "host": "prod-db.example.com",
                "port": 5432,
                "database": "production",
                "username": "app_user",
                "password": "secure_password",
                "ssl": True,
                "connection_timeout": 30
            }
        },
        {
            "name": "开发环境SSH",
            "type": "ssh",
            "description": "开发环境的SSH隧道连接",
            "config": {
                "host": "dev-server.example.com",
                "port": 22,
                "username": "developer",
                "auth_method": "key",
                "key_path": "/path/to/private/key",
                "connection_timeout": 30
            }
        }
    ]
    
    for ds in sample_datasources:
        ds_id = get_next_id()
        now = datetime.now()
        
        datasources_db[ds_id] = {
            **ds,
            'status': 'disconnected',
            'last_tested': None,
            'created_at': now,
            'updated_at': now
        }

# 表结构相关模型
class TableInfo(BaseModel):
    """表信息"""
    name: str
    schema: Optional[str] = None
    type: str = "table"  # table, view, materialized_view
    comment: Optional[str] = None
    row_count: Optional[int] = None

class ColumnInfo(BaseModel):
    """列信息"""
    name: str
    data_type: str
    is_nullable: bool = True
    default_value: Optional[str] = None
    max_length: Optional[int] = None
    precision: Optional[int] = None
    scale: Optional[int] = None
    comment: Optional[str] = None
    is_primary_key: bool = False
    is_foreign_key: bool = False

class TableSchema(BaseModel):
    """表结构"""
    table: TableInfo
    columns: List[ColumnInfo]
    indexes: List[Dict[str, Any]] = []
    foreign_keys: List[Dict[str, Any]] = []

class DatabaseSchema(BaseModel):
    """数据库结构"""
    database_name: str
    tables: List[TableInfo]
    total_tables: int
    schemas: List[str] = []

@router.get("/{datasource_id}/schema", response_model=DatabaseSchema)
async def get_database_schema(datasource_id: int):
    """获取数据库结构"""
    if datasource_id not in datasources_db:
        raise HTTPException(status_code=404, detail="数据源不存在")

    try:
        ds_data = datasources_db[datasource_id]

        if ds_data['type'] != 'postgresql':
            raise HTTPException(status_code=400, detail="只支持PostgreSQL数据源的结构预览")

        if ds_data['status'] != 'connected':
            raise HTTPException(status_code=400, detail="数据源未连接，请先测试连接")

        # 模拟获取数据库结构
        schema = await get_postgresql_schema(ds_data['config'])
        return schema

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据库结构失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据库结构失败")

@router.get("/{datasource_id}/tables/{table_name}/schema", response_model=TableSchema)
async def get_table_schema(datasource_id: int, table_name: str, schema_name: Optional[str] = None):
    """获取表结构详情"""
    if datasource_id not in datasources_db:
        raise HTTPException(status_code=404, detail="数据源不存在")

    try:
        ds_data = datasources_db[datasource_id]

        if ds_data['type'] != 'postgresql':
            raise HTTPException(status_code=400, detail="只支持PostgreSQL数据源的表结构预览")

        if ds_data['status'] != 'connected':
            raise HTTPException(status_code=400, detail="数据源未连接，请先测试连接")

        # 模拟获取表结构
        table_schema = await get_postgresql_table_schema(ds_data['config'], table_name, schema_name)
        return table_schema

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        raise HTTPException(status_code=500, detail="获取表结构失败")

@router.get("/{datasource_id}/tables/{table_name}/preview")
async def preview_table_data(
    datasource_id: int,
    table_name: str,
    schema_name: Optional[str] = None,
    limit: int = 50
):
    """预览表数据"""
    if datasource_id not in datasources_db:
        raise HTTPException(status_code=404, detail="数据源不存在")

    try:
        ds_data = datasources_db[datasource_id]

        if ds_data['type'] != 'postgresql':
            raise HTTPException(status_code=400, detail="只支持PostgreSQL数据源的数据预览")

        if ds_data['status'] != 'connected':
            raise HTTPException(status_code=400, detail="数据源未连接，请先测试连接")

        # 限制预览数据量
        if limit > 1000:
            limit = 1000

        # 模拟获取表数据
        preview_data = await get_postgresql_table_preview(ds_data['config'], table_name, schema_name, limit)
        return preview_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览表数据失败: {e}")
        raise HTTPException(status_code=500, detail="预览表数据失败")

async def get_postgresql_schema(config: Dict[str, Any]) -> DatabaseSchema:
    """获取PostgreSQL数据库结构"""
    try:
        # 模拟数据库查询
        await asyncio.sleep(0.5)

        # 模拟表列表
        tables = [
            TableInfo(
                name="users",
                schema="public",
                type="table",
                comment="用户表",
                row_count=1250
            ),
            TableInfo(
                name="orders",
                schema="public",
                type="table",
                comment="订单表",
                row_count=5680
            ),
            TableInfo(
                name="products",
                schema="public",
                type="table",
                comment="产品表",
                row_count=890
            ),
            TableInfo(
                name="user_profiles",
                schema="public",
                type="table",
                comment="用户资料表",
                row_count=1200
            ),
            TableInfo(
                name="order_items",
                schema="public",
                type="table",
                comment="订单项表",
                row_count=12450
            ),
            TableInfo(
                name="categories",
                schema="public",
                type="table",
                comment="分类表",
                row_count=45
            ),
            TableInfo(
                name="user_activity_log",
                schema="logs",
                type="table",
                comment="用户活动日志",
                row_count=89000
            ),
            TableInfo(
                name="sales_summary",
                schema="reports",
                type="view",
                comment="销售汇总视图",
                row_count=None
            )
        ]

        return DatabaseSchema(
            database_name=config.get('database', 'unknown'),
            tables=tables,
            total_tables=len(tables),
            schemas=["public", "logs", "reports"]
        )

    except Exception as e:
        logger.error(f"获取PostgreSQL结构失败: {e}")
        raise

async def get_postgresql_table_schema(config: Dict[str, Any], table_name: str, schema_name: Optional[str] = None) -> TableSchema:
    """获取PostgreSQL表结构详情"""
    try:
        # 模拟数据库查询
        await asyncio.sleep(0.3)

        # 模拟不同表的结构
        if table_name == "users":
            table_info = TableInfo(
                name="users",
                schema=schema_name or "public",
                type="table",
                comment="用户表",
                row_count=1250
            )

            columns = [
                ColumnInfo(
                    name="id",
                    data_type="integer",
                    is_nullable=False,
                    is_primary_key=True,
                    comment="用户ID"
                ),
                ColumnInfo(
                    name="username",
                    data_type="varchar",
                    max_length=50,
                    is_nullable=False,
                    comment="用户名"
                ),
                ColumnInfo(
                    name="email",
                    data_type="varchar",
                    max_length=100,
                    is_nullable=False,
                    comment="邮箱地址"
                ),
                ColumnInfo(
                    name="password_hash",
                    data_type="varchar",
                    max_length=255,
                    is_nullable=False,
                    comment="密码哈希"
                ),
                ColumnInfo(
                    name="created_at",
                    data_type="timestamp",
                    is_nullable=False,
                    default_value="CURRENT_TIMESTAMP",
                    comment="创建时间"
                ),
                ColumnInfo(
                    name="updated_at",
                    data_type="timestamp",
                    is_nullable=True,
                    comment="更新时间"
                ),
                ColumnInfo(
                    name="is_active",
                    data_type="boolean",
                    is_nullable=False,
                    default_value="true",
                    comment="是否激活"
                )
            ]

        elif table_name == "orders":
            table_info = TableInfo(
                name="orders",
                schema=schema_name or "public",
                type="table",
                comment="订单表",
                row_count=5680
            )

            columns = [
                ColumnInfo(
                    name="id",
                    data_type="integer",
                    is_nullable=False,
                    is_primary_key=True,
                    comment="订单ID"
                ),
                ColumnInfo(
                    name="user_id",
                    data_type="integer",
                    is_nullable=False,
                    is_foreign_key=True,
                    comment="用户ID"
                ),
                ColumnInfo(
                    name="order_number",
                    data_type="varchar",
                    max_length=20,
                    is_nullable=False,
                    comment="订单号"
                ),
                ColumnInfo(
                    name="total_amount",
                    data_type="decimal",
                    precision=10,
                    scale=2,
                    is_nullable=False,
                    comment="总金额"
                ),
                ColumnInfo(
                    name="status",
                    data_type="varchar",
                    max_length=20,
                    is_nullable=False,
                    default_value="'pending'",
                    comment="订单状态"
                ),
                ColumnInfo(
                    name="created_at",
                    data_type="timestamp",
                    is_nullable=False,
                    default_value="CURRENT_TIMESTAMP",
                    comment="创建时间"
                )
            ]

        else:
            # 通用表结构
            table_info = TableInfo(
                name=table_name,
                schema=schema_name or "public",
                type="table",
                comment=f"{table_name}表",
                row_count=100
            )

            columns = [
                ColumnInfo(
                    name="id",
                    data_type="integer",
                    is_nullable=False,
                    is_primary_key=True,
                    comment="主键ID"
                ),
                ColumnInfo(
                    name="name",
                    data_type="varchar",
                    max_length=100,
                    is_nullable=False,
                    comment="名称"
                ),
                ColumnInfo(
                    name="created_at",
                    data_type="timestamp",
                    is_nullable=False,
                    default_value="CURRENT_TIMESTAMP",
                    comment="创建时间"
                )
            ]

        return TableSchema(
            table=table_info,
            columns=columns,
            indexes=[
                {"name": f"{table_name}_pkey", "type": "PRIMARY KEY", "columns": ["id"]},
                {"name": f"idx_{table_name}_created_at", "type": "INDEX", "columns": ["created_at"]}
            ],
            foreign_keys=[]
        )

    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        raise

async def get_postgresql_table_preview(config: Dict[str, Any], table_name: str, schema_name: Optional[str] = None, limit: int = 50) -> Dict[str, Any]:
    """获取PostgreSQL表数据预览"""
    try:
        # 模拟数据库查询
        await asyncio.sleep(0.4)

        # 模拟不同表的数据
        if table_name == "users":
            columns = ["id", "username", "email", "created_at", "is_active"]
            rows = [
                [1, "admin", "<EMAIL>", "2024-01-01 10:00:00", True],
                [2, "john_doe", "<EMAIL>", "2024-01-02 11:30:00", True],
                [3, "jane_smith", "<EMAIL>", "2024-01-03 09:15:00", True],
                [4, "bob_wilson", "<EMAIL>", "2024-01-04 14:20:00", False],
                [5, "alice_brown", "<EMAIL>", "2024-01-05 16:45:00", True]
            ]

        elif table_name == "orders":
            columns = ["id", "user_id", "order_number", "total_amount", "status", "created_at"]
            rows = [
                [1, 2, "ORD-2024-001", 299.99, "completed", "2024-01-10 10:30:00"],
                [2, 3, "ORD-2024-002", 149.50, "pending", "2024-01-11 14:15:00"],
                [3, 2, "ORD-2024-003", 89.99, "shipped", "2024-01-12 09:45:00"],
                [4, 5, "ORD-2024-004", 199.99, "processing", "2024-01-13 11:20:00"],
                [5, 3, "ORD-2024-005", 349.99, "completed", "2024-01-14 16:30:00"]
            ]

        else:
            # 通用数据
            columns = ["id", "name", "created_at"]
            rows = [
                [1, f"{table_name}_item_1", "2024-01-01 10:00:00"],
                [2, f"{table_name}_item_2", "2024-01-02 11:00:00"],
                [3, f"{table_name}_item_3", "2024-01-03 12:00:00"]
            ]

        # 限制返回的行数
        if len(rows) > limit:
            rows = rows[:limit]

        return {
            "table_name": table_name,
            "schema_name": schema_name or "public",
            "columns": columns,
            "rows": rows,
            "total_rows": len(rows),
            "limit": limit,
            "has_more": len(rows) == limit
        }

    except Exception as e:
        logger.error(f"获取表数据预览失败: {e}")
        raise

# 启动时初始化示例数据
init_sample_data()
