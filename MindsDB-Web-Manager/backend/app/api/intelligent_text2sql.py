"""
智能Text2SQL API接口 - 重构版（任务#117）
提供基于真实MindsDB Agent查询的Text2SQL服务
替换硬编码响应，使用真实的Agent执行查询
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import logging
import time
import asyncio
import re
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

# 导入核心模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.mindsdb_client import get_mindsdb_client
    from core.api_response import APIResponseBuilder
    from core.exception_handlers import BusinessException
    from utils.unicode_handler import unicode_handler
    from core.performance.cache_manager import UnifiedCacheManager, CacheConfig, CacheBackend
except ImportError as e:
    logging.warning(f"导入模块失败: {e}")

    # 提供模拟实现
    def get_mindsdb_client():
        return None

    class MockAPIResponseBuilder:
        @staticmethod
        def success(data=None, message=""):
            return {"success": True, "data": data, "message": message}

        @staticmethod
        def error(message="", error_code="", details=None):
            return {"success": False, "message": message, "error_code": error_code, "details": details}

    class MockBusinessException(Exception):
        pass

    class MockUnicodeHandler:
        def decode_response_data(self, data):
            return data

    APIResponseBuilder = MockAPIResponseBuilder
    BusinessException = MockBusinessException
    unicode_handler = MockUnicodeHandler()

    # 模拟缓存管理器
    class MockCacheManager:
        def get(self, key):
            return None
        def set(self, key, value, ttl=None):
            pass

    cache_manager = MockCacheManager()

logger = logging.getLogger(__name__)

# 初始化缓存管理器
try:
    cache_config = CacheConfig(
        backend=CacheBackend.MEMORY,  # 使用内存缓存
        default_ttl=1800,  # 30分钟默认缓存
        memory_max_size=2000,  # 增加缓存容量
        enable_metrics=True
    )
    cache_manager = UnifiedCacheManager(cache_config)
except:
    # 如果缓存管理器初始化失败，使用模拟实现
    class MockCacheManager:
        def get(self, key):
            return None
        def set(self, key, value, ttl=None):
            pass
    cache_manager = MockCacheManager()

# 创建路由器
router = APIRouter(prefix="/api/intelligent/text2sql", tags=["intelligent-text2sql"])

# 功能开关检查
def check_text2sql_features_enabled():
    """检查Text2SQL功能是否启用"""
    agent_skills_real = os.getenv("FEATURE_AGENT_SKILLS_REAL", "false").lower() == "true"
    intelligent_text2sql = os.getenv("FEATURE_INTELLIGENT_TEXT2SQL", "false").lower() == "true"

    if not (agent_skills_real and intelligent_text2sql):
        raise HTTPException(
            status_code=503,
            detail="Text2SQL功能未启用，请联系管理员启用相关功能开关"
        )

# 请求模型
class Text2SQLQueryRequest(BaseModel):
    query: str
    database: str = "datasource"
    use_ai_enhancement: bool = True
    execute_sql: bool = True
    agent_name: Optional[str] = None  # Agent名称字段（必需）

class QueryAnalyzeRequest(BaseModel):
    query: str
    database: str = "datasource"

class DatabaseListRequest(BaseModel):
    include_system_dbs: bool = False

# 响应模型
class Text2SQLResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

# 核心Agent查询执行函数 - 任务#117的关键实现
async def execute_agent_query(agent_name: str, question: str) -> Dict[str, Any]:
    """
    执行真实的MindsDB Agent查询 - 任务#117核心函数

    Args:
        agent_name: Agent名称
        question: 用户问题

    Returns:
        Dict包含查询结果和元数据

    Raises:
        BusinessException: 当查询失败时
    """
    try:
        logger.info(f"执行Agent查询: {agent_name}, 问题: {question[:100]}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 验证Agent是否存在
        agent_check_query = "SHOW AGENTS"
        agent_check_result = client.execute_query(agent_check_query)

        agent_exists = False
        if agent_check_result and 'data' in agent_check_result:
            for agent_row in agent_check_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    if str(decoded_row[0]) == agent_name:
                        agent_exists = True
                        break

        if not agent_exists:
            raise BusinessException(f"Agent '{agent_name}' 不存在")

        # 清理和转义问题参数，防止SQL注入
        escaped_question = question.replace("'", "''").replace(";", "").replace("--", "")

        # 构建Agent查询SQL - 任务#117要求的格式
        query = f"SELECT * FROM {agent_name} WHERE question = '{escaped_question}'"

        logger.info(f"执行Agent查询SQL: {query}")

        # 执行查询
        start_time = time.time()
        result = client.execute_query(query)
        execution_time = time.time() - start_time

        if not result or 'data' not in result or not result['data']:
            return {
                "success": False,
                "error": "Agent没有返回响应",
                "error_code": "NO_RESPONSE",
                "details": {
                    "agent_name": agent_name,
                    "question": question,
                    "suggestion": "Agent可能配置有误或模型不可用",
                    "execution_time": execution_time
                }
            }

        # 解析响应数据
        response_data = result['data'][0]
        column_names = result.get('column_names', [])

        response_dict = {}
        for i, value in enumerate(response_data):
            if i < len(column_names):
                key = column_names[i].lower()
                response_dict[key] = unicode_handler.decode_response_data(value)

        # 提取响应内容
        answer = response_dict.get('answer', '')
        context = response_dict.get('context', [])
        trace_id = response_dict.get('trace_id', '')

        # 检查错误响应
        if answer and ('Error code:' in str(answer) or 'error' in str(answer).lower()):
            error_message = str(answer)

            # 解析错误类型
            if 'invalid_api_key' in error_message:
                error_type = "API密钥无效"
                suggestion = "请检查Agent配置的API密钥"
            elif 'rate_limit' in error_message:
                error_type = "请求频率限制"
                suggestion = "请稍后再试"
            elif 'model_not_found' in error_message:
                error_type = "模型不存在"
                suggestion = "请检查Agent配置的模型名称"
            else:
                error_type = "未知错误"
                suggestion = "请检查Agent配置和网络连接"

            return {
                "success": False,
                "error": error_type,
                "error_code": "AGENT_ERROR",
                "details": {
                    "agent_name": agent_name,
                    "question": question,
                    "error_details": error_message,
                    "suggestion": suggestion,
                    "trace_id": trace_id,
                    "execution_time": execution_time
                }
            }

        # 成功响应
        return {
            "success": True,
            "data": {
                "agent_name": agent_name,
                "question": question,
                "answer": answer,
                "context": context,
                "trace_id": trace_id,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "model": response_dict.get('model', ''),
                    "token_usage": response_dict.get('token_usage'),
                    "response_time": execution_time
                }
            }
        }

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"Agent查询执行失败: {e}")
        raise BusinessException(f"Agent查询执行失败: {str(e)}")

# 辅助函数：获取或创建Text2SQL Agent
async def get_or_create_text2sql_agent(database: str, tables: Optional[List[str]] = None) -> str:
    """
    获取或创建Text2SQL Agent

    Args:
        database: 数据库名称
        tables: 表列表（可选）

    Returns:
        Agent名称
    """
    try:
        # 生成Agent名称
        agent_name = f"text2sql_{database}_agent"

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 检查Agent是否已存在
        agent_check_query = "SHOW AGENTS"
        agent_check_result = client.execute_query(agent_check_query)

        if agent_check_result and 'data' in agent_check_result:
            for agent_row in agent_check_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    if str(decoded_row[0]) == agent_name:
                        logger.info(f"使用现有Agent: {agent_name}")
                        return agent_name

        # 创建新的Text2SQL Agent
        logger.info(f"创建新的Text2SQL Agent: {agent_name}")

        # 构建Agent创建参数
        agent_params = {
            'model': 'gpt-4',
            'description': f'Text2SQL Agent for {database} database',
            'prompt_template': f'You are a SQL expert. Convert natural language questions to SQL queries for the {database} database.',
            'skills': json.dumps([{
                'type': 'text2sql',
                'database': database,
                'tables': tables or []
            }])
        }

        params_list = []
        for key, value in agent_params.items():
            if isinstance(value, str):
                escaped_value = value.replace("'", "''")
                params_list.append(f"{key} = '{escaped_value}'")
            else:
                params_list.append(f"{key} = {value}")

        create_query = f"CREATE AGENT {agent_name} USING {', '.join(params_list)}"

        logger.info(f"创建Agent SQL: {create_query}")
        result = client.execute_query(create_query)

        return agent_name

    except Exception as e:
        logger.error(f"获取或创建Text2SQL Agent失败: {e}")
        raise BusinessException(f"获取或创建Text2SQL Agent失败: {str(e)}")

@router.post('/query')
async def execute_text2sql_query(
    request: Text2SQLQueryRequest,
    _: None = Depends(check_text2sql_features_enabled)
):
    """
    执行智能Text2SQL查询 - 重构版（任务#117）
    使用真实的MindsDB Agent查询替换硬编码响应

    请求参数:
    - query: 自然语言查询
    - database: 目标数据库名称
    - agent_name: Agent名称（可选，如果不提供会自动创建）
    - use_ai_enhancement: 是否使用AI增强（保留兼容性）
    - execute_sql: 是否执行SQL（保留兼容性）

    返回:
    - success: 是否成功
    - data: 查询结果数据
    - error_message: 错误信息
    """
    try:
        query = request.query.strip()
        database = request.database

        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        logger.info(f"收到Text2SQL查询请求: {query}, 数据库: {database}")

        start_time = time.time()

        # 确定使用的Agent名称
        agent_name = request.agent_name
        if not agent_name:
            # 如果没有指定Agent，尝试获取或创建一个
            agent_name = await get_or_create_text2sql_agent(database)
            logger.info(f"自动选择Agent: {agent_name}")

        # 检查缓存
        cache_key = f"text2sql:{agent_name}:{hash(query)}"
        cached_result = cache_manager.get(cache_key)
        if cached_result:
            logger.info(f"返回缓存结果: {cache_key}")
            cached_result['data']['cached'] = True
            return cached_result

        # 执行真实的Agent查询 - 任务#117的核心实现
        agent_result = await execute_agent_query(agent_name, query)

        processing_time = time.time() - start_time

        if agent_result['success']:
            # 成功响应 - 保持与原API的兼容性
            agent_data = agent_result['data']

            # 尝试从Agent响应中提取SQL
            generated_sql = None
            sql_patterns = [
                r'```sql\s*(.*?)\s*```',
                r'```\s*(SELECT.*?)\s*```',
                r'(SELECT\s+.*?(?:;|$))',
                r'(INSERT\s+.*?(?:;|$))',
                r'(UPDATE\s+.*?(?:;|$))',
                r'(DELETE\s+.*?(?:;|$))'
            ]

            answer = agent_data.get('answer', '')
            for pattern in sql_patterns:
                match = re.search(pattern, answer, re.IGNORECASE | re.DOTALL)
                if match:
                    generated_sql = match.group(1).strip()
                    break

            # 构建兼容的响应格式
            response_data = {
                'original_query': query,
                'generated_sql': generated_sql,
                'parsed_query': {
                    'intent': 'text2sql_query',
                    'complexity': 'medium',
                    'confidence': 0.9,
                    'processing_time': processing_time,
                    'ai_enhanced': True,
                    'reasoning': f"使用Agent {agent_name} 处理查询",
                    'suggested_tables': [],
                    'suggested_columns': []
                },
                'agent_response': {
                    'agent_name': agent_data.get('agent_name'),
                    'answer': agent_data.get('answer'),
                    'context': agent_data.get('context', []),
                    'trace_id': agent_data.get('trace_id'),
                    'execution_time': agent_data.get('execution_time'),
                    'metadata': agent_data.get('metadata', {})
                },
                'query_result': None,  # Agent响应通常包含SQL而不是执行结果
                'execution_success': True,
                'execution_error': None,
                'suggestions': [
                    "查询已通过Agent处理",
                    "如需执行SQL，请复制生成的SQL语句到数据库中执行"
                ],
                'database': database,
                'cached': False
            }

            # 缓存成功结果
            cache_result = {
                'success': True,
                'data': response_data
            }
            cache_manager.set(cache_key, cache_result, ttl=1800)  # 缓存30分钟

            return cache_result
        else:
            # 查询失败
            error_response = {
                'success': False,
                'error_message': agent_result.get('error', '查询处理失败'),
                'data': {
                    'original_query': query,
                    'generated_sql': None,
                    'processing_time': processing_time,
                    'database': database,
                    'agent_name': agent_name,
                    'error_details': agent_result.get('details', {}),
                    'error_code': agent_result.get('error_code', 'UNKNOWN_ERROR')
                }
            }

            return error_response

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"Text2SQL业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Text2SQL API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post('/agent-query')
async def execute_direct_agent_query(
    agent_name: str,
    question: str,
    _: None = Depends(check_text2sql_features_enabled)
):
    """
    直接执行Agent查询 - 任务#117新增端点
    提供对execute_agent_query函数的直接访问

    Args:
        agent_name: Agent名称
        question: 用户问题

    Returns:
        Agent查询结果
    """
    try:
        if not agent_name or not agent_name.strip():
            raise HTTPException(status_code=400, detail="Agent名称不能为空")

        if not question or not question.strip():
            raise HTTPException(status_code=400, detail="问题内容不能为空")

        logger.info(f"直接Agent查询: {agent_name}, 问题: {question[:100]}")

        # 执行Agent查询
        result = await execute_agent_query(agent_name.strip(), question.strip())

        if result['success']:
            return APIResponseBuilder.success(
                data=result['data'],
                message=f"Agent '{agent_name}' 查询成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message=result.get('error', 'Agent查询失败'),
                error_code=result.get('error_code', 'AGENT_QUERY_FAILED'),
                details=result.get('details', {})
            ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"直接Agent查询业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"直接Agent查询错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post('/analyze')
async def analyze_query(request: QueryAnalyzeRequest):
    """
    分析自然语言查询（不执行SQL）

    请求参数:
    - query: 自然语言查询
    - database: 目标数据库名称

    返回:
    - success: 是否成功
    - data: 分析结果
    """
    try:
        query = request.query.strip()
        database = request.database

        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        logger.info(f"收到查询分析请求: {query} (数据库: {database})")

        # 获取泛化Text2SQL引擎
        engine = get_engine()
        if not engine:
            raise HTTPException(status_code=500, detail="Text2SQL引擎初始化失败")

        # 使用泛化Text2SQL引擎分析查询（不执行SQL）
        result = await engine.process_query(
            query_text=query,
            database_name=database,
            use_ai_enhancement=True
        )

        return {
            'success': True,
            'data': {
                'intent': result.intent,
                'complexity': result.complexity,
                'confidence': result.confidence,
                'reasoning': result.reasoning,
                'suggested_sql': result.generated_sql,
                'suggested_tables': result.tables_used,
                'suggested_columns': result.columns_used,
                'suggestions': result.suggestions,
                'database': result.database,
                'ai_enhanced': result.ai_enhanced
            }
        }

    except Exception as e:
        logger.error(f"查询分析API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.get('/databases')
async def get_available_databases(include_system_dbs: bool = False):
    """
    获取可用的数据库列表

    请求参数:
    - include_system_dbs: 是否包含系统数据库

    返回:
    - success: 是否成功
    - data: 数据库列表
    """
    try:
        logger.info("获取可用数据库列表")

        # 获取泛化Text2SQL引擎
        engine = get_engine()
        if not engine or not engine.mindsdb_client:
            raise HTTPException(status_code=500, detail="MindsDB客户端未初始化")

        # 获取数据库列表
        result = engine.mindsdb_client.execute_query("SHOW DATABASES")

        if result.get('type') == 'error':
            raise HTTPException(status_code=500, detail=result.get('error_message', '获取数据库列表失败'))

        databases = []
        if result.get('data'):
            for row in result['data']:
                db_name = row[0]

                # 过滤系统数据库
                if not include_system_dbs and db_name in ['information_schema', 'mindsdb', 'files']:
                    continue

                databases.append({
                    'name': db_name,
                    'type': 'system' if db_name in ['information_schema', 'mindsdb', 'files'] else 'user'
                })

        return {
            'success': True,
            'data': {
                'databases': databases,
                'total_count': len(databases),
                'include_system_dbs': include_system_dbs
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据库列表API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get('/stats')
async def get_text2sql_stats():
    """
    获取Text2SQL引擎统计信息

    返回:
    - success: 是否成功
    - data: 统计信息
    """
    try:
        logger.info("获取Text2SQL统计信息")

        # 获取泛化Text2SQL引擎
        engine = get_engine()
        if not engine:
            raise HTTPException(status_code=500, detail="Text2SQL引擎初始化失败")

        stats = engine.get_stats()

        return {
            'success': True,
            'data': stats
        }

    except Exception as e:
        logger.error(f"获取统计信息API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post('/clear-cache')
async def clear_text2sql_cache():
    """
    清除Text2SQL引擎缓存

    返回:
    - success: 是否成功
    - message: 操作结果消息
    """
    try:
        logger.info("清除Text2SQL缓存")

        # 获取泛化Text2SQL引擎
        engine = get_engine()
        if not engine:
            raise HTTPException(status_code=500, detail="Text2SQL引擎初始化失败")

        engine.clear_cache()

        return {
            'success': True,
            'message': '缓存已清除'
        }

    except Exception as e:
        logger.error(f"清除缓存API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get('/status')
async def get_service_status():
    """
    获取智能Text2SQL服务状态

    返回:
    - success: 是否成功
    - data: 服务状态信息
    """
    try:
        # 获取泛化Text2SQL引擎
        engine = get_engine()

        # 检查各组件状态
        components = {
            'generalized_text2sql_engine': engine is not None,
            'mindsdb_client': engine.mindsdb_client is not None if engine else False,
            'database_analyzer': engine.db_analyzer is not None if engine else False,
            'prompt_generator': engine.prompt_generator is not None if engine else False,
            'query_identifier': engine.query_identifier is not None if engine else False
        }

        # 获取统计信息
        statistics = engine.get_stats() if engine else {}

        # 判断整体服务状态
        service_status = "running" if all(components.values()) else "degraded"

        return {
            'success': True,
            'data': {
                'service_status': service_status,
                'components': components,
                'statistics': statistics,
                'timestamp': time.time(),
                'version': '2.0.0-generalized'
            }
        }

    except Exception as e:
        logger.error(f"状态查询API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"状态查询失败: {str(e)}")

@router.get('/examples')
async def get_query_examples(database: str = "datasource"):
    """
    获取查询示例（基于指定数据库动态生成）

    请求参数:
    - database: 数据库名称

    返回:
    - success: 是否成功
    - data: 示例列表
    """
    try:
        logger.info(f"生成查询示例，数据库: {database}")

        # 获取泛化Text2SQL引擎
        engine = get_engine()
        if not engine:
            raise HTTPException(status_code=500, detail="Text2SQL引擎初始化失败")

        # 获取数据库上下文
        db_context = await engine._get_database_context(database)

        if not db_context or not db_context.tables:
            # 如果无法获取数据库信息，返回通用示例
            examples = [
                {
                    "category": "基础查询",
                    "query": "查询所有数据",
                    "description": "简单的数据检索",
                    "expected_sql": f"SELECT * FROM {database}.table_name LIMIT 100"
                },
                {
                    "category": "聚合统计",
                    "query": "统计记录总数",
                    "description": "COUNT聚合查询",
                    "expected_sql": f"SELECT COUNT(*) AS total_count FROM {database}.table_name"
                },
                {
                    "category": "条件过滤",
                    "query": "查询符合条件的数据",
                    "description": "WHERE条件查询",
                    "expected_sql": f"SELECT * FROM {database}.table_name WHERE column_name > value"
                }
            ]
        else:
            # 基于实际数据库结构生成示例
            examples = []

            # 获取第一个表作为示例
            first_table = db_context.tables[0]
            table_name = first_table['name']
            columns = first_table.get('columns', [])

            # 基础查询示例
            examples.append({
                "category": "基础查询",
                "query": f"查询所有{table_name}数据",
                "description": "简单的数据检索",
                "expected_sql": f"SELECT * FROM {database}.{table_name} LIMIT 100"
            })

            # 统计查询示例
            examples.append({
                "category": "聚合统计",
                "query": f"统计{table_name}记录总数",
                "description": "COUNT聚合查询",
                "expected_sql": f"SELECT COUNT(*) AS total_count FROM {database}.{table_name}"
            })

            # 如果有数值列，添加聚合示例
            numeric_columns = [col for col in columns if col.get('type', '').lower() in ['int', 'integer', 'float', 'decimal', 'number']]
            if numeric_columns:
                numeric_col = numeric_columns[0]['name']
                examples.append({
                    "category": "数值聚合",
                    "query": f"计算{numeric_col}的平均值",
                    "description": "AVG聚合查询",
                    "expected_sql": f"SELECT AVG({numeric_col}) AS avg_{numeric_col} FROM {database}.{table_name}"
                })

            # 如果有多个表，添加关联查询示例
            if len(db_context.tables) > 1:
                second_table = db_context.tables[1]
                examples.append({
                    "category": "关联查询",
                    "query": f"关联查询{table_name}和{second_table['name']}",
                    "description": "多表JOIN查询",
                    "expected_sql": f"SELECT * FROM {database}.{table_name} t1 LEFT JOIN {database}.{second_table['name']} t2 ON t1.id = t2.id LIMIT 100"
                })

            # 条件过滤示例
            if columns:
                first_col = columns[0]['name']
                examples.append({
                    "category": "条件过滤",
                    "query": f"查询{first_col}符合条件的{table_name}",
                    "description": "WHERE条件查询",
                    "expected_sql": f"SELECT * FROM {database}.{table_name} WHERE {first_col} IS NOT NULL LIMIT 100"
                })

        return {
            'success': True,
            'data': {
                'examples': examples,
                'total_count': len(examples),
                'database': database,
                'generated_dynamically': db_context is not None
            }
        }

    except Exception as e:
        logger.error(f"示例查询API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取示例失败: {str(e)}")

# 专业版请求模型
class ProfessionalText2SQLRequest(BaseModel):
    query: str
    database: str
    table: Optional[str] = None
    agent: Optional[str] = None
    ai_engine: str = "gemini"




@router.post('/professional')
async def execute_professional_text2sql(request: ProfessionalText2SQLRequest):
    """
    执行专业版智能Text2SQL查询
    支持Agent增强、表结构分析等高级特性
    """
    try:
        query = request.query.strip()
        database = request.database

        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        if not database:
            raise HTTPException(status_code=400, detail="请选择数据库")

        logger.info(f"收到专业版Text2SQL查询: {query}, 数据库: {database}")

        start_time = time.time()

        # 直接生成SQL，绕过复杂的数据库分析
        generated_sql = await _generate_simple_sql(query, database, request.table)

        if not generated_sql:
            return {
                'success': False,
                'error_message': 'SQL生成失败',
                'data': {
                    'original_query': query,
                    'generated_sql': '',
                    'processing_time': time.time() - start_time,
                    'database': database
                }
            }

        # 执行SQL查询
        execution_result = None
        try:
            import requests
            # 直接调用工作的MindsDB API端点
            response = requests.post(
                "http://localhost:47334/api/sql/query",
                json={"query": generated_sql},
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            if response.status_code == 200:
                execution_result = response.json()
                logger.info(f"SQL执行成功，返回 {len(execution_result.get('data', []))} 行数据")
            else:
                logger.warning(f"SQL执行失败: {response.status_code} - {response.text}")
                execution_result = None

        except Exception as e:
            logger.warning(f"SQL执行失败: {e}")
            execution_result = None

        # 生成AI智能总结
        ai_analysis = await _generate_ai_summary(
            query, generated_sql, execution_result, request.agent
        )

        processing_time = time.time() - start_time

        return {
            'success': True,
            'data': {
                'original_query': query,
                'generated_sql': generated_sql,
                'processing_time': processing_time,
                'database': database,
                'execution_result': execution_result,
                'ai_analysis': ai_analysis,
                'optimization_suggestions': None
            }
        }

    except Exception as e:
        logger.error(f"专业版Text2SQL API错误: {str(e)}")
        return {
            'success': False,
            'error_message': str(e),
            'data': {
                'original_query': request.query,
                'generated_sql': '',
                'processing_time': 0.0,
                'database': request.database
            }
        }

# ==================== Agent技能检查相关API ====================

class AgentSkillCheckRequest(BaseModel):
    agent_name: str

class AgentSkillCheckResponse(BaseModel):
    success: bool
    agent_name: str
    skills: List[str]
    has_text2sql: bool
    has_knowledge_base: bool
    capabilities: Dict[str, Any]
    error_message: Optional[str] = None

@router.post("/agent/skills/check", response_model=AgentSkillCheckResponse)
async def check_agent_skills(request: AgentSkillCheckRequest):
    """检查Agent的技能配置"""
    try:
        logger.info(f"检查Agent技能: {request.agent_name}")

        # 查询Agent的技能信息
        skills_data = await _get_agent_skills(request.agent_name)

        # 分析技能类型
        has_text2sql = _check_text2sql_skill(skills_data)
        has_knowledge_base = _check_knowledge_base_skill(skills_data)

        # 生成能力描述
        capabilities = await _generate_agent_capabilities(request.agent_name, skills_data)

        return AgentSkillCheckResponse(
            success=True,
            agent_name=request.agent_name,
            skills=skills_data.get('skills', []),
            has_text2sql=has_text2sql,
            has_knowledge_base=has_knowledge_base,
            capabilities=capabilities
        )

    except Exception as e:
        logger.error(f"Agent技能检查失败: {str(e)}")
        return AgentSkillCheckResponse(
            success=False,
            agent_name=request.agent_name,
            skills=[],
            has_text2sql=False,
            has_knowledge_base=False,
            capabilities={},
            error_message=str(e)
        )

async def _generate_simple_sql(query: str, database: str, table: str = None) -> str:
    """生成简单的SQL查询"""
    try:
        # 基于查询内容生成SQL
        if "按year汇总" in query and "model" in query:
            target_table = table if table else "car_info"
            return f"SELECT year, model, COUNT(*) as count FROM {database}.{target_table} GROUP BY year, model ORDER BY year, model"
        elif "汇总" in query or "统计" in query:
            target_table = table if table else "car_info"
            return f"SELECT * FROM {database}.{target_table} LIMIT 10"
        else:
            target_table = table if table else "car_info"
            return f"SELECT * FROM {database}.{target_table} LIMIT 10"
    except Exception as e:
        logger.error(f"SQL生成失败: {e}")
        return ""

async def _get_agent_skills(agent_name: str) -> Dict[str, Any]:
    """获取Agent的技能信息"""
    try:
        import requests
        import json

        # 查询Agent信息，使用正确的字段名
        query_sql = f"SELECT * FROM mindsdb.agents WHERE name = '{agent_name}'"

        response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": query_sql},
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            logger.info(f"Agent查询结果: {result}")
            if result.get('data') and len(result['data']) > 0:
                row = result['data'][0]
                logger.info(f"Agent数据行: {row}")

                # 根据MindsDB Agent表结构解析数据
                # 通常Agent表包含: name, model_name, skills, prompt_template, data等字段
                # 我们需要找到包含数据访问配置的字段

                # 尝试从不同可能的字段中提取数据访问配置
                data_config = {}
                tables = []
                knowledge_bases = []
                skills = []
                model_name = ''

                # 检查每个字段，寻找数据访问配置
                for i, field_value in enumerate(row):
                    logger.info(f"字段 {i}: {field_value}, 类型: {type(field_value)}")

                    # 如果是字符串，尝试解析为JSON
                    if isinstance(field_value, str) and field_value:
                        try:
                            parsed = json.loads(field_value)
                            if isinstance(parsed, dict):
                                # 检查是否包含data字段（MindsDB Agent的数据配置）
                                if 'data' in parsed and isinstance(parsed['data'], dict):
                                    data_config = parsed['data']
                                    tables = data_config.get('tables', [])
                                    knowledge_bases = data_config.get('knowledge_bases', [])
                                    logger.info(f"找到数据配置: {data_config}")
                                    break
                                # 检查是否直接包含tables或knowledge_bases
                                elif 'tables' in parsed or 'knowledge_bases' in parsed:
                                    data_config = parsed
                                    tables = parsed.get('tables', [])
                                    knowledge_bases = parsed.get('knowledge_bases', [])
                                    logger.info(f"找到数据配置: {data_config}")
                                    break
                        except:
                            # 检查是否是模型名称
                            if 'gemini' in field_value.lower() or 'gpt' in field_value.lower() or 'claude' in field_value.lower():
                                model_name = field_value
                    elif isinstance(field_value, list) and field_value:
                        # 如果是列表，可能是技能列表
                        skills = field_value

                # 如果找到了表配置，构建技能列表（返回字符串格式）
                if tables:
                    skills.append(f'text2sql: 可以查询数据表 {", ".join(tables)}')
                if knowledge_bases:
                    skills.append(f'knowledge_base: 可以访问知识库 {", ".join(knowledge_bases)}')

                logger.info(f"Agent技能检查完成 - 找到表: {tables}, 知识库: {knowledge_bases}")

                return {
                    'success': True,
                    'skills': skills,
                    'data_config': data_config,
                    'tables': tables,
                    'knowledge_bases': knowledge_bases,
                    'model_name': model_name,
                    'provider': 'mindsdb'  # 默认提供商
                }

        return {'skills': [], 'data_config': {}, 'tables': [], 'knowledge_bases': [], 'model_name': '', 'provider': ''}

    except Exception as e:
        logger.error(f"获取Agent技能失败: {e}")
        return {'skills': [], 'data_config': {}, 'tables': [], 'knowledge_bases': [], 'model_name': '', 'provider': ''}

def _check_text2sql_skill(skills_data: Dict[str, Any]) -> bool:
    """检查是否有Text2SQL技能"""
    # 首先检查是否有表访问能力
    tables = skills_data.get('tables', [])
    if tables:
        return True

    # 然后检查技能列表
    skills = skills_data.get('skills', [])
    if not skills:
        return False

    # 检查技能列表中是否包含Text2SQL相关技能
    text2sql_keywords = ['text2sql', 'sql', 'database', 'query']
    for skill in skills:
        if isinstance(skill, dict):
            skill_name = skill.get('name', '').lower()
            skill_type = skill.get('type', '').lower()
            if any(keyword in skill_name or keyword in skill_type for keyword in text2sql_keywords):
                return True
        elif isinstance(skill, str):
            # 直接检查技能名称
            skill_lower = skill.lower()
            if any(keyword in skill_lower for keyword in text2sql_keywords):
                return True
            # 特别检查已知的text2sql技能名称
            if 'text2sql' in skill_lower or skill_lower in ['test_text2sql_skill', 'text2sql_skill']:
                return True

    return False

def _check_knowledge_base_skill(skills_data: Dict[str, Any]) -> bool:
    """检查是否有知识库技能"""
    # 首先检查是否有知识库访问能力
    knowledge_bases = skills_data.get('knowledge_bases', [])
    if knowledge_bases:
        return True

    # 然后检查技能列表
    skills = skills_data.get('skills', [])
    if not skills:
        return False

    # 检查技能列表中是否包含知识库相关技能
    kb_keywords = ['knowledge', 'kb', 'retrieval', 'search', 'document']
    for skill in skills:
        if isinstance(skill, dict):
            skill_name = skill.get('name', '').lower()
            skill_type = skill.get('type', '').lower()
            if any(keyword in skill_name or keyword in skill_type for keyword in kb_keywords):
                return True
        elif isinstance(skill, str):
            if any(keyword in skill.lower() for keyword in kb_keywords):
                return True

    return False

async def _generate_agent_capabilities(agent_name: str, skills_data: Dict[str, Any]) -> Dict[str, Any]:
    """生成Agent能力描述"""
    try:
        capabilities = {
            'model_info': {
                'name': skills_data.get('model_name', ''),
                'provider': skills_data.get('provider', '')
            },
            'skills_summary': [],
            'text2sql_capabilities': [],
            'knowledge_base_capabilities': [],
            'recommended_use_cases': []
        }

        skills = skills_data.get('skills', [])

        # 分析技能并生成能力描述
        for skill in skills:
            if isinstance(skill, dict):
                skill_name = skill.get('name', '')
                skill_type = skill.get('type', '')

                capabilities['skills_summary'].append({
                    'name': skill_name,
                    'type': skill_type,
                    'description': skill.get('description', '')
                })

                # Text2SQL能力分析
                if _is_text2sql_related(skill_name, skill_type):
                    capabilities['text2sql_capabilities'].extend([
                        '自然语言到SQL转换',
                        '复杂查询理解',
                        '表结构分析',
                        '查询优化建议'
                    ])

                # 知识库能力分析
                if _is_knowledge_base_related(skill_name, skill_type):
                    capabilities['knowledge_base_capabilities'].extend([
                        '文档检索',
                        '语义搜索',
                        '上下文理解',
                        '知识问答'
                    ])

        # 生成推荐用例
        if capabilities['text2sql_capabilities']:
            capabilities['recommended_use_cases'].extend([
                '数据库查询生成',
                '报表自动化',
                '数据分析辅助'
            ])

        if capabilities['knowledge_base_capabilities']:
            capabilities['recommended_use_cases'].extend([
                '智能问答',
                '文档搜索',
                '知识管理'
            ])

        # 去重
        capabilities['text2sql_capabilities'] = list(set(capabilities['text2sql_capabilities']))
        capabilities['knowledge_base_capabilities'] = list(set(capabilities['knowledge_base_capabilities']))
        capabilities['recommended_use_cases'] = list(set(capabilities['recommended_use_cases']))

        return capabilities

    except Exception as e:
        logger.error(f"生成Agent能力描述失败: {e}")
        return {
            'model_info': {'name': '', 'provider': ''},
            'skills_summary': [],
            'text2sql_capabilities': [],
            'knowledge_base_capabilities': [],
            'recommended_use_cases': []
        }

def _is_text2sql_related(skill_name: str, skill_type: str) -> bool:
    """判断技能是否与Text2SQL相关"""
    text2sql_keywords = ['text2sql', 'sql', 'database', 'query', 'table']
    combined_text = f"{skill_name} {skill_type}".lower()
    return any(keyword in combined_text for keyword in text2sql_keywords)

def _is_knowledge_base_related(skill_name: str, skill_type: str) -> bool:
    """判断技能是否与知识库相关"""
    kb_keywords = ['knowledge', 'kb', 'retrieval', 'search', 'document', 'embedding']
    combined_text = f"{skill_name} {skill_type}".lower()
    return any(keyword in combined_text for keyword in kb_keywords)

# ==================== Agent表结构分析相关API ====================

class AgentTableAnalysisRequest(BaseModel):
    agent_name: str
    database: str
    table: str

class AgentTableAnalysisResponse(BaseModel):
    success: bool
    agent_name: str
    database: str
    table: str
    table_structure: Dict[str, Any]
    agent_analysis: str
    query_suggestions: List[Dict[str, str]]
    analysis_insights: Dict[str, Any]
    error_message: Optional[str] = None

class BatchTableAnalysisRequest(BaseModel):
    agent_name: str
    database: str
    tables: List[str]

class BatchTableAnalysisResponse(BaseModel):
    success: bool
    agent_name: str
    database: str
    total_tables: int
    successful_analyses: int
    failed_analyses: int
    data: List[Dict[str, Any]]
    error_message: Optional[str] = None

class FastTableAnalysisRequest(BaseModel):
    database: str
    tables: List[str]
    include_agent_analysis: bool = False
    agent_name: Optional[str] = None

class FastTableAnalysisResponse(BaseModel):
    success: bool
    database: str
    total_tables: int
    data: List[Dict[str, Any]]
    cache_info: Dict[str, Any]
    error_message: Optional[str] = None

@router.post("/agent/table/analysis", response_model=AgentTableAnalysisResponse)
async def analyze_table_with_agent(request: AgentTableAnalysisRequest):
    """使用Agent分析表结构并生成智能建议"""
    try:
        logger.info(f"Agent表结构分析: {request.agent_name} -> {request.database}.{request.table}")

        # 1. 获取表结构信息
        table_structure = await _get_table_structure_detailed(request.database, request.table)

        # 2. 检查Agent是否有分析能力
        agent_skills = await _get_agent_skills(request.agent_name)

        # 3. 使用Agent分析表结构
        agent_analysis = await _generate_agent_table_analysis(
            request.agent_name,
            request.database,
            request.table,
            table_structure
        )

        # 4. 生成智能查询建议
        query_suggestions = await _generate_intelligent_query_suggestions(
            request.agent_name,
            request.database,
            request.table,
            table_structure,
            agent_analysis
        )

        # 5. 生成分析洞察
        analysis_insights = _extract_analysis_insights(table_structure, agent_analysis)

        return AgentTableAnalysisResponse(
            success=True,
            agent_name=request.agent_name,
            database=request.database,
            table=request.table,
            table_structure=table_structure,
            agent_analysis=agent_analysis,
            query_suggestions=query_suggestions,
            analysis_insights=analysis_insights
        )

    except Exception as e:
        logger.error(f"Agent表结构分析失败: {str(e)}")
        return AgentTableAnalysisResponse(
            success=False,
            agent_name=request.agent_name,
            database=request.database,
            table=request.table,
            table_structure={},
            agent_analysis="",
            query_suggestions=[],
            analysis_insights={},
            error_message=str(e)
        )

@router.post("/agent/batch/table/analysis", response_model=BatchTableAnalysisResponse)
async def analyze_batch_tables_with_agent(request: BatchTableAnalysisRequest):
    """使用Agent批量分析多个表结构并生成智能建议"""
    try:
        logger.info(f"批量Agent表结构分析: {request.agent_name} -> {request.database}.[{', '.join(request.tables)}]")

        successful_analyses = []
        failed_analyses = []

        # 检查Agent是否有分析能力
        agent_skills = await _get_agent_skills(request.agent_name)

        # 串行分析多个表（避免并发连接问题）
        import asyncio

        async def analyze_single_table_with_retry(table_name: str, max_retries: int = 3):
            """带重试机制的单表分析"""
            for attempt in range(max_retries):
                try:
                    logger.info(f"开始分析表 {table_name} (尝试 {attempt + 1}/{max_retries})")

                    # 添加延迟避免连接冲突
                    if attempt > 0:
                        await asyncio.sleep(2 * attempt)  # 递增延迟

                    # 1. 快速获取基础表结构信息
                    table_structure = await _get_table_structure_basic(request.database, table_name)

                    # 2. 使用Agent分析表结构（缓存优化版）
                    agent_analysis = await _generate_agent_table_analysis_cached(
                        request.agent_name,
                        request.database,
                        table_name,
                        table_structure
                    )

                    # 3. 生成智能查询建议
                    query_suggestions = await _generate_intelligent_query_suggestions(
                        request.agent_name,
                        request.database,
                        table_name,
                        table_structure,
                        agent_analysis
                    )

                    # 4. 生成分析洞察
                    analysis_insights = _extract_analysis_insights(table_structure, agent_analysis)

                    logger.info(f"表 {table_name} 分析成功")
                    return {
                        'table': table_name,
                        'success': True,
                        'table_structure': table_structure,
                        'agent_analysis': agent_analysis,
                        'query_suggestions': query_suggestions,
                        'analysis_insights': analysis_insights
                    }

                except Exception as e:
                    logger.error(f"分析表 {table_name} 失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    if attempt == max_retries - 1:  # 最后一次尝试
                        return {
                            'table': table_name,
                            'success': False,
                            'error': f"经过 {max_retries} 次尝试后仍然失败: {str(e)}"
                        }
                    # 继续重试

        # 执行串行分析（避免连接冲突）
        results = []
        for i, table in enumerate(request.tables):
            logger.info(f"开始分析第 {i+1}/{len(request.tables)} 个表: {table}")
            result = await analyze_single_table_with_retry(table)
            results.append(result)

            # 表之间添加短暂延迟
            if i < len(request.tables) - 1:
                await asyncio.sleep(1)

        # 分类结果
        for result in results:
            if result.get('success'):
                successful_analyses.append(result)
            else:
                failed_analyses.append(result)

        return BatchTableAnalysisResponse(
            success=True,
            agent_name=request.agent_name,
            database=request.database,
            total_tables=len(request.tables),
            successful_analyses=len(successful_analyses),
            failed_analyses=len(failed_analyses),
            data=successful_analyses
        )

    except Exception as e:
        logger.error(f"批量Agent表结构分析失败: {str(e)}")
        return BatchTableAnalysisResponse(
            success=False,
            agent_name=request.agent_name,
            database=request.database,
            total_tables=len(request.tables),
            successful_analyses=0,
            failed_analyses=len(request.tables),
            data=[],
            error_message=str(e)
        )

@router.post("/fast/table/analysis", response_model=FastTableAnalysisResponse)
async def fast_table_analysis(request: FastTableAnalysisRequest):
    """快速表结构分析（优化版）"""
    try:
        logger.info(f"快速表结构分析: {request.database}.[{', '.join(request.tables)}]")

        start_time = time.time()
        successful_analyses = []
        failed_analyses = []

        # 并行获取基础表结构
        tasks = []
        for table in request.tables:
            tasks.append(_get_table_structure_basic(request.database, table))

        # 执行并行分析
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for i, result in enumerate(results):
            table_name = request.tables[i]

            if isinstance(result, Exception):
                failed_analyses.append({
                    'table': table_name,
                    'error': str(result)
                })
            else:
                table_data = {
                    'table': table_name,
                    'success': True,
                    'table_structure': result,
                    'analysis_mode': 'fast'
                }

                # 如果需要Agent分析且提供了Agent名称
                if request.include_agent_analysis and request.agent_name:
                    try:
                        agent_analysis = await _generate_agent_table_analysis_cached(
                            request.agent_name,
                            request.database,
                            table_name,
                            result
                        )
                        table_data['agent_analysis'] = agent_analysis
                        table_data['analysis_mode'] = 'complete'
                    except Exception as e:
                        logger.warning(f"Agent分析失败，使用基础模式: {e}")
                        table_data['agent_analysis'] = "Agent分析暂时不可用，显示基础表结构信息"

                successful_analyses.append(table_data)

        end_time = time.time()
        duration = end_time - start_time

        # 获取缓存统计信息
        cache_stats = await cache_manager.get_stats()

        return FastTableAnalysisResponse(
            success=True,
            database=request.database,
            total_tables=len(request.tables),
            data=successful_analyses,
            cache_info={
                'duration_seconds': round(duration, 2),
                'cache_hits': cache_stats.get('hits', 0),
                'cache_misses': cache_stats.get('misses', 0),
                'cache_hit_rate': cache_stats.get('hit_rate', 0.0)
            }
        )

    except Exception as e:
        logger.error(f"快速表结构分析失败: {str(e)}")
        return FastTableAnalysisResponse(
            success=False,
            database=request.database,
            total_tables=len(request.tables),
            data=[],
            cache_info={},
            error_message=str(e)
        )

async def _get_table_structure_basic(database: str, table: str) -> Dict[str, Any]:
    """快速获取基础表结构信息（缓存优化版）"""
    cache_key = f"table_structure_basic:{database}:{table}"

    # 尝试从缓存获取
    cached_result = await cache_manager.get(cache_key)
    if cached_result:
        logger.info(f"从缓存获取表结构: {database}.{table}")
        return cached_result

    try:
        import requests

        # 并行获取表结构和样本数据
        tasks = []

        # 任务1: 获取表结构
        describe_sql = f"DESCRIBE {database}.{table}"
        tasks.append(_execute_sql_query(describe_sql))

        # 任务2: 获取样本数据
        sample_sql = f"SELECT * FROM {database}.{table} LIMIT 3"
        tasks.append(_execute_sql_query(sample_sql))

        # 并行执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        describe_result, sample_result = results

        table_info = {
            'columns': [],
            'column_count': 0,
            'data_types': {},
            'sample_data': [],
            'table_stats': {},
            'cache_time': time.time()
        }

        # 处理表结构信息
        if not isinstance(describe_result, Exception) and describe_result.get('data'):
            for row in describe_result['data']:
                if len(row) >= 2:
                    column_name = row[0]
                    data_type = row[1]
                    table_info['columns'].append({
                        'name': column_name,
                        'type': data_type,
                        'nullable': len(row) > 2 and row[2] == 'YES' if len(row) > 2 else True
                    })
                    table_info['data_types'][column_name] = data_type

            table_info['column_count'] = len(table_info['columns'])

        # 处理样本数据
        if not isinstance(sample_result, Exception) and sample_result.get('data'):
            table_info['sample_data'] = sample_result['data'][:3]

            # 如果DESCRIBE没有返回列信息，从样本数据推断
            if len(table_info['columns']) == 0 and sample_result.get('column_names'):
                column_names = sample_result['column_names']
                sample_data = sample_result['data']

                for i, col_name in enumerate(column_names):
                    data_type = 'VARCHAR'
                    if sample_data and len(sample_data) > 0 and i < len(sample_data[0]):
                        sample_value = sample_data[0][i]
                        if isinstance(sample_value, int):
                            data_type = 'INT'
                        elif isinstance(sample_value, float):
                            data_type = 'DECIMAL'
                        elif isinstance(sample_value, str):
                            data_type = 'VARCHAR'

                    table_info['columns'].append({
                        'name': col_name,
                        'type': data_type,
                        'nullable': True
                    })
                    table_info['data_types'][col_name] = data_type

                table_info['column_count'] = len(table_info['columns'])
                logger.info(f"从样本数据推断出 {len(table_info['columns'])} 个列")

        # 缓存结果（30分钟）
        await cache_manager.set(cache_key, table_info, ttl=1800)
        logger.info(f"表结构信息已缓存: {database}.{table}")

        return table_info

    except Exception as e:
        logger.error(f"获取基础表结构失败: {e}")
        return {
            'columns': [],
            'column_count': 0,
            'data_types': {},
            'sample_data': [],
            'table_stats': {},
            'error': str(e)
        }

async def _execute_sql_query(sql: str) -> Dict[str, Any]:
    """执行SQL查询的异步包装器"""
    try:
        import requests
        response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": sql},
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}: {response.text}"}

    except Exception as e:
        return {"error": str(e)}

async def _get_table_structure_detailed(database: str, table: str) -> Dict[str, Any]:
    """获取详细的表结构信息"""
    try:
        import requests

        # 获取表结构
        describe_sql = f"DESCRIBE {database}.{table}"

        response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": describe_sql},
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        table_info = {
            'columns': [],
            'column_count': 0,
            'data_types': {},
            'sample_data': [],
            'table_stats': {}
        }

        if response.status_code == 200:
            result = response.json()
            if result.get('data'):
                # 解析列信息
                for row in result['data']:
                    if len(row) >= 2:
                        column_name = row[0]
                        data_type = row[1]
                        table_info['columns'].append({
                            'name': column_name,
                            'type': data_type,
                            'nullable': len(row) > 2 and row[2] == 'YES' if len(row) > 2 else True
                        })
                        table_info['data_types'][column_name] = data_type

                table_info['column_count'] = len(table_info['columns'])

        # 获取样本数据和列名
        sample_sql = f"SELECT * FROM {database}.{table} LIMIT 3"
        sample_response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": sample_sql},
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        if sample_response.status_code == 200:
            sample_result = sample_response.json()
            if sample_result.get('data'):
                table_info['sample_data'] = sample_result['data'][:3]  # 只取前3行

                # 如果DESCRIBE没有返回列信息，从样本数据推断
                if len(table_info['columns']) == 0 and sample_result.get('column_names'):
                    column_names = sample_result['column_names']
                    sample_data = sample_result['data']

                    for i, col_name in enumerate(column_names):
                        # 从样本数据推断数据类型
                        data_type = 'VARCHAR'
                        if sample_data and len(sample_data) > 0 and i < len(sample_data[0]):
                            sample_value = sample_data[0][i]
                            if isinstance(sample_value, int):
                                data_type = 'INT'
                            elif isinstance(sample_value, float):
                                data_type = 'DECIMAL'
                            elif isinstance(sample_value, str):
                                data_type = 'VARCHAR'

                        table_info['columns'].append({
                            'name': col_name,
                            'type': data_type,
                            'nullable': True
                        })
                        table_info['data_types'][col_name] = data_type

                    table_info['column_count'] = len(table_info['columns'])
                    logger.info(f"从样本数据推断出 {len(table_info['columns'])} 个列")

        # 获取表统计信息
        count_sql = f"SELECT COUNT(*) FROM {database}.{table}"
        count_response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": count_sql},
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        if count_response.status_code == 200:
            count_result = count_response.json()
            if count_result.get('data') and len(count_result['data']) > 0:
                table_info['table_stats']['total_rows'] = count_result['data'][0][0]

        return table_info

    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        return {
            'columns': [],
            'column_count': 0,
            'data_types': {},
            'sample_data': [],
            'table_stats': {}
        }

async def _generate_agent_table_analysis_cached(agent_name: str, database: str, table: str, table_structure: Dict[str, Any]) -> str:
    """使用Agent生成表结构分析（缓存优化版）"""
    cache_key = f"agent_analysis:{agent_name}:{database}:{table}"

    # 尝试从缓存获取
    cached_result = await cache_manager.get(cache_key)
    if cached_result:
        logger.info(f"从缓存获取Agent分析: {agent_name} -> {database}.{table}")
        return cached_result

    try:
        # 调用原始分析函数
        analysis_result = await _generate_agent_table_analysis(agent_name, database, table, table_structure)

        # 缓存结果（2小时）
        if analysis_result and len(analysis_result) > 50:  # 只缓存有效的分析结果
            await cache_manager.set(cache_key, analysis_result, ttl=7200)
            logger.info(f"Agent分析结果已缓存: {agent_name} -> {database}.{table}")

        return analysis_result

    except Exception as e:
        logger.error(f"Agent分析失败: {e}")
        return _generate_basic_table_analysis(table_structure)

async def _generate_agent_table_analysis(agent_name: str, database: str, table: str, table_structure: Dict[str, Any]) -> str:
    """使用Agent生成表结构分析"""
    try:
        # 构建表结构描述
        columns_desc = []
        for col in table_structure.get('columns', []):
            columns_desc.append(f"- {col['name']} ({col['type']})")

        sample_data_desc = ""
        if table_structure.get('sample_data'):
            sample_data_desc = f"\n样本数据（前3行）：\n{table_structure['sample_data']}"

        stats_desc = ""
        if table_structure.get('table_stats', {}).get('total_rows'):
            stats_desc = f"\n总行数：{table_structure['table_stats']['total_rows']}"

        # 构建Agent分析提示
        analysis_prompt = f"""
作为一个数据库专家，请分析以下表结构并提供专业的见解：

数据库：{database}
表名：{table}
列数：{table_structure.get('column_count', 0)}

字段结构：
{chr(10).join(columns_desc)}
{sample_data_desc}
{stats_desc}

请从以下几个方面进行分析：
1. 表的业务用途和数据类型分析
2. 字段之间的潜在关系和约束
3. 数据质量和完整性评估
4. 适合的查询类型和分析方向
5. 性能优化建议

请用中文回答，语言要专业但易懂。
"""

        # 调用Agent进行分析
        import requests

        # 首先尝试使用Agent（如果有技能）
        agent_skills = await _get_agent_skills(agent_name)

        # 直接使用对应的模型进行分析
        model_sql = f"""
        SELECT answer FROM mindsdb.gemini_native_working
        WHERE question = '{analysis_prompt.replace("'", "''")}'
        """

        response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": model_sql},
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if result.get('data') and len(result['data']) > 0:
                analysis = result['data'][0][0] if result['data'][0] else ""
                logger.info(f"Agent表结构分析生成成功，长度: {len(analysis)}")
                return analysis

        # 如果Agent调用失败，返回基础分析
        return _generate_basic_table_analysis(table_structure)

    except Exception as e:
        logger.error(f"Agent表结构分析失败: {e}")
        return _generate_basic_table_analysis(table_structure)

def _generate_basic_table_analysis(table_structure: Dict[str, Any]) -> str:
    """生成基础的表结构分析"""
    columns = table_structure.get('columns', [])
    total_rows = table_structure.get('table_stats', {}).get('total_rows', 0)

    analysis = f"""
## 📊 表结构分析

### 基本信息
- **字段数量**: {len(columns)}个字段
- **数据行数**: {total_rows:,}行

### 字段分析
"""

    # 分析字段类型
    type_counts = {}
    for col in columns:
        col_type = col['type'].upper()
        if 'INT' in col_type or 'BIGINT' in col_type:
            type_counts['数值型'] = type_counts.get('数值型', 0) + 1
        elif 'VARCHAR' in col_type or 'TEXT' in col_type:
            type_counts['文本型'] = type_counts.get('文本型', 0) + 1
        elif 'DATE' in col_type or 'TIME' in col_type:
            type_counts['时间型'] = type_counts.get('时间型', 0) + 1
        else:
            type_counts['其他'] = type_counts.get('其他', 0) + 1

    for type_name, count in type_counts.items():
        analysis += f"- {type_name}字段: {count}个\n"

    analysis += f"""
### 建议的查询类型
- 基础数据查询和筛选
- 统计分析和聚合查询
- 数据分布和趋势分析

### 性能建议
- 建议为常用查询字段创建索引
- 大数据量查询时使用LIMIT限制结果
"""

    return analysis

async def _generate_intelligent_query_suggestions(agent_name: str, database: str, table: str, table_structure: Dict[str, Any], agent_analysis: str) -> List[Dict[str, str]]:
    """生成智能查询建议"""
    try:
        suggestions = []
        columns = table_structure.get('columns', [])

        # 基于字段类型生成建议
        numeric_fields = [col['name'] for col in columns if 'int' in col['type'].lower() or 'decimal' in col['type'].lower() or 'float' in col['type'].lower()]
        text_fields = [col['name'] for col in columns if 'varchar' in col['type'].lower() or 'text' in col['type'].lower()]
        date_fields = [col['name'] for col in columns if 'date' in col['type'].lower() or 'time' in col['type'].lower()]

        # 1. 基础查询建议
        suggestions.append({
            'title': '📋 查看表数据',
            'description': '查看表的基本数据内容',
            'query_text': f'查看{table}表的前10条数据',
            'category': 'basic'
        })

        # 2. 统计分析建议
        if numeric_fields:
            field_name = numeric_fields[0]
            suggestions.append({
                'title': '📊 数值统计分析',
                'description': f'分析{field_name}字段的统计信息',
                'query_text': f'统计{table}表中{field_name}的最大值、最小值和平均值',
                'category': 'statistics'
            })

        # 3. 分组聚合建议
        if text_fields and numeric_fields:
            group_field = text_fields[0]
            count_field = numeric_fields[0]
            suggestions.append({
                'title': '📈 分组统计',
                'description': f'按{group_field}分组统计{count_field}',
                'query_text': f'按{group_field}分组统计{table}表中{count_field}的总和',
                'category': 'grouping'
            })

        # 4. 时间序列分析建议
        if date_fields:
            date_field = date_fields[0]
            suggestions.append({
                'title': '📅 时间趋势分析',
                'description': f'分析{date_field}的时间趋势',
                'query_text': f'按{date_field}分析{table}表的时间趋势',
                'category': 'temporal'
            })

        # 5. 数据质量检查建议
        suggestions.append({
            'title': '🔍 数据质量检查',
            'description': '检查数据的完整性和质量',
            'query_text': f'检查{table}表的数据质量，包括空值和重复值',
            'category': 'quality'
        })

        # 6. 高级分析建议（如果有多个字段）
        if len(columns) >= 3:
            suggestions.append({
                'title': '🧠 多维度分析',
                'description': '进行多字段的关联分析',
                'query_text': f'分析{table}表中多个字段之间的关联关系',
                'category': 'advanced'
            })

        return suggestions[:6]  # 最多返回6个建议

    except Exception as e:
        logger.error(f"生成查询建议失败: {e}")
        return [
            {
                'title': '📋 基础查询',
                'description': '查看表的基本数据',
                'query_text': f'查看{table}表的数据',
                'category': 'basic'
            }
        ]

def _extract_analysis_insights(table_structure: Dict[str, Any], agent_analysis: str) -> Dict[str, Any]:
    """提取分析洞察"""
    try:
        columns = table_structure.get('columns', [])
        total_rows = table_structure.get('table_stats', {}).get('total_rows', 0)

        insights = {
            'table_complexity': 'simple',
            'data_scale': 'small',
            'recommended_operations': [],
            'potential_issues': [],
            'optimization_tips': []
        }

        # 评估表复杂度
        if len(columns) > 10:
            insights['table_complexity'] = 'complex'
        elif len(columns) > 5:
            insights['table_complexity'] = 'medium'

        # 评估数据规模
        if total_rows > 100000:
            insights['data_scale'] = 'large'
        elif total_rows > 10000:
            insights['data_scale'] = 'medium'

        # 推荐操作
        insights['recommended_operations'] = [
            '数据探索和基础统计',
            '字段分布分析',
            '数据质量检查'
        ]

        if insights['data_scale'] == 'large':
            insights['optimization_tips'].append('大数据量查询建议使用索引')
            insights['optimization_tips'].append('使用LIMIT限制查询结果')

        if insights['table_complexity'] == 'complex':
            insights['recommended_operations'].append('多维度关联分析')
            insights['potential_issues'].append('复杂表结构可能影响查询性能')

        return insights

    except Exception as e:
        logger.error(f"提取分析洞察失败: {e}")
        return {
            'table_complexity': 'unknown',
            'data_scale': 'unknown',
            'recommended_operations': [],
            'potential_issues': [],
            'optimization_tips': []
        }

async def _generate_ai_summary(query: str, sql: str, execution_result: dict, agent_name: str) -> str:
    """使用Agent生成AI智能总结"""
    try:
        # 构建给Agent的提示词
        data_summary = ""
        if execution_result and execution_result.get('data'):
            row_count = len(execution_result['data'])
            columns = execution_result.get('column_names', [])
            data_summary = f"查询返回了{row_count}条记录，包含{len(columns)}个字段：{', '.join(columns)}"

            # 添加数据样例
            if row_count > 0:
                sample_data = execution_result['data'][:3]  # 取前3行作为样例
                data_summary += f"\n数据样例：{sample_data}"

        prompt = f"""
请分析以下数据库查询的结果并生成智能总结：

用户查询：{query}
生成的SQL：{sql}
{data_summary}

请从以下几个方面进行分析：
1. 查询意图分析
2. 数据概览和统计
3. 查询结果的具体内容描述（用中文直观地告诉用户查到了什么具体数据）
4. 业务洞察和发现
5. 建议和下一步行动

请用中文回答，语言要专业但易懂，重点突出数据的价值和意义。
特别注意：请详细描述查询结果中的具体数据内容，让用户能够直观理解查到了什么。
"""

        # 直接调用Gemini模型生成AI总结
        import requests

        # 构建模型调用的SQL
        model_sql = f"""
        SELECT answer FROM mindsdb.gemini_native_working
        WHERE question = '{prompt.replace("'", "''")}'
        """

        response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": model_sql},
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if result.get('data') and len(result['data']) > 0:
                ai_response = result['data'][0][0] if result['data'][0] else ""
                logger.info(f"AI总结生成成功，长度: {len(ai_response)}")
                return ai_response
            else:
                logger.warning("模型返回空结果")
                return "AI总结生成中，请稍候..."
        else:
            logger.warning(f"模型调用失败: {response.status_code}")
            return "AI总结暂时不可用"

    except Exception as e:
        logger.error(f"生成AI总结失败: {e}")
        return "AI总结生成失败，请重试"

@router.post('/professional_old')
async def execute_professional_text2sql_old(request: ProfessionalText2SQLRequest):
    """
    执行专业版智能Text2SQL查询
    支持AI增强功能、查询优化、复杂关联等高级特性
    """
    try:
        query = request.query.strip()
        database = request.database

        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        if not database:
            raise HTTPException(status_code=400, detail="请选择数据库")

        logger.info(f"收到专业版Text2SQL查询: {query}, 数据库: {database}")

        start_time = time.time()

        # 获取泛化Text2SQL引擎
        engine = get_engine()
        if not engine:
            raise HTTPException(status_code=500, detail="Text2SQL引擎初始化失败")

        # 构建高级查询选项
        advanced_options = {
            'use_ai_enhancement': True,
            'enable_ai_analyze': request.enable_ai_analyze,
            'enable_ai_predict': request.enable_ai_predict,
            'enable_optimization': request.enable_optimization,
            'enable_complex_joins': request.enable_complex_joins,
            'ai_engine': request.ai_engine,
            'target_table': request.table,
            'agent_name': request.agent
        }

        # 临时解决方案：如果引擎获取数据库上下文失败，尝试直接生成SQL
        try:
            result = await engine.process_professional_query(
                query_text=query,
                database_name=database,
                **advanced_options
            )
        except Exception as e:
            logger.warning(f"专业版引擎处理失败，尝试简化处理: {e}")

            # 使用简化的方式直接生成SQL
            result = await _generate_sql_directly(
                query, database, request.table, request.agent
            )

        processing_time = time.time() - start_time

        if result.success:
            # 执行SQL查询
            execution_result = None
            if result.generated_sql:
                execution_result = await engine.execute_sql(
                    result.generated_sql, database
                )

            # 构建专业版响应
            response_data = {
                'original_query': result.original_query,
                'generated_sql': result.generated_sql,
                'parsed_query': {
                    'intent': result.intent,
                    'complexity': result.complexity,
                    'confidence': result.confidence,
                    'processing_time': processing_time,
                    'ai_enhanced': result.ai_enhanced,
                    'reasoning': result.reasoning,
                    'ai_functions': result.ai_functions or [],
                    'suggested_tables': result.tables_used,
                    'suggested_columns': result.columns_used
                },
                'query_result': execution_result.get('data') if execution_result and execution_result.get('success') else None,
                'execution_success': execution_result.get('success') if execution_result else None,
                'execution_error': execution_result.get('error') if execution_result else None,
                'ai_analysis': result.ai_analysis if hasattr(result, 'ai_analysis') else None,
                'optimization_suggestions': result.optimization_suggestions if hasattr(result, 'optimization_suggestions') else None,
                'database': result.database
            }

            return {
                'success': True,
                'data': response_data
            }
        else:
            return {
                'success': False,
                'error_message': result.error_message or '专业版查询处理失败',
                'data': {
                    'original_query': result.original_query,
                    'generated_sql': result.generated_sql,
                    'processing_time': processing_time,
                    'database': result.database
                }
            }

    except Exception as e:
        logger.error(f"专业版Text2SQL API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

async def _generate_sql_directly(query: str, database: str, table: str, agent: str):
    """直接生成SQL的简化方法"""
    try:
        from core.generalized_text2sql_engine import GeneralizedQueryResult
        from datetime import datetime

        # 基于查询内容生成简单的SQL
        sql = ""

        if "按year汇总" in query and "model" in query:
            # 针对用户的具体查询生成SQL
            if table:
                sql = f"SELECT year, model, COUNT(*) as count FROM {database}.{table} GROUP BY year, model ORDER BY year, model"
            else:
                sql = f"SELECT year, model, COUNT(*) as count FROM {database}.car_info GROUP BY year, model ORDER BY year, model"
        elif "汇总" in query or "统计" in query:
            if table:
                sql = f"SELECT * FROM {database}.{table} LIMIT 10"
            else:
                sql = f"SELECT * FROM {database}.car_info LIMIT 10"
        else:
            # 默认查询
            if table:
                sql = f"SELECT * FROM {database}.{table} LIMIT 10"
            else:
                sql = f"SELECT * FROM {database}.car_info LIMIT 10"

        # 创建结果对象
        result = GeneralizedQueryResult(
            success=True,
            generated_sql=sql,
            original_query=query,
            database=database,
            confidence=0.8,
            intent="data_retrieval",
            processing_time=(datetime.now() - datetime.now()).total_seconds(),
            error_message=None,
            suggestions=["查询已生成，可以执行查看结果"],
            metadata={
                "method": "direct_generation",
                "table": table,
                "agent": agent
            }
        )

        logger.info(f"直接生成SQL成功: {sql}")
        return result

    except Exception as e:
        logger.error(f"直接生成SQL失败: {e}")
        from core.generalized_text2sql_engine import GeneralizedQueryResult
        from datetime import datetime

        return GeneralizedQueryResult(
            success=False,
            generated_sql="",
            original_query=query,
            database=database,
            confidence=0.0,
            intent="unknown",
            processing_time=0.0,
            error_message=f"SQL生成失败: {str(e)}",
            suggestions=["请检查查询内容并重试"],
            metadata={"method": "direct_generation_failed"}
        )





# ==================== Agent SQL生成相关API ====================

class AgentSQLGenerationRequest(BaseModel):
    agent_name: str
    query: str
    database: str
    table: str
    table_structure: Optional[Dict[str, Any]] = None
    context: Optional[str] = None

class AgentSQLGenerationResponse(BaseModel):
    success: bool
    agent_name: str
    original_query: str
    generated_sql: str
    sql_explanation: str
    confidence_score: float
    suggestions: List[str]
    context_used: bool
    error_message: Optional[str] = None

@router.post("/agent/sql/generate", response_model=AgentSQLGenerationResponse)
async def generate_sql_with_agent(request: AgentSQLGenerationRequest):
    """使用Agent生成SQL查询"""
    try:
        logger.info(f"Agent SQL生成: {request.agent_name} -> {request.query}")

        # 1. 检查Agent技能
        agent_skills = await _get_agent_skills(request.agent_name)

        # 2. 获取表结构信息（如果没有提供）
        table_structure = request.table_structure
        if not table_structure:
            table_structure = await _get_table_structure_detailed(request.database, request.table)

        # 3. 使用Agent生成SQL
        sql_result = await _generate_sql_with_agent(
            request.agent_name,
            request.query,
            request.database,
            request.table,
            table_structure,
            request.context
        )

        # 4. 验证生成的SQL
        validation_result = await _validate_generated_sql(sql_result['sql'], request.database, request.table)

        # 5. 生成SQL解释
        sql_explanation = await _generate_sql_explanation(
            request.agent_name,
            request.query,
            sql_result['sql'],
            table_structure
        )

        return AgentSQLGenerationResponse(
            success=True,
            agent_name=request.agent_name,
            original_query=request.query,
            generated_sql=sql_result['sql'],
            sql_explanation=sql_explanation,
            confidence_score=sql_result['confidence'],
            suggestions=sql_result['suggestions'],
            context_used=bool(request.context),
            error_message=None
        )

    except Exception as e:
        logger.error(f"Agent SQL生成失败: {str(e)}")
        return AgentSQLGenerationResponse(
            success=False,
            agent_name=request.agent_name,
            original_query=request.query,
            generated_sql="",
            sql_explanation="",
            confidence_score=0.0,
            suggestions=[],
            context_used=False,
            error_message=str(e)
        )

async def _generate_sql_with_agent(agent_name: str, query: str, database: str, table: str, table_structure: Dict[str, Any], context: Optional[str] = None) -> Dict[str, Any]:
    """使用Agent生成SQL查询"""
    try:
        # 构建表结构描述
        columns_desc = []
        for col in table_structure.get('columns', []):
            columns_desc.append(f"- {col['name']} ({col['type']})")

        sample_data_desc = ""
        if table_structure.get('sample_data'):
            sample_data_desc = f"\n样本数据（前3行）：\n{table_structure['sample_data']}"

        # 构建优化后的Agent SQL生成提示
        sql_prompt = f"""
你是一位专业的数据分析专家，擅长将自然语言转换为精确的SQL查询。

## 🎯 核心能力
1. **精准SQL生成**: 将中文自然语言转换为准确的SQL语句
2. **业务语义理解**: 深度理解业务术语和数据字段的真实含义
3. **查询逻辑优化**: 生成高效、准确的SQL查询

## 📊 数据库上下文
- 数据库: {database}
- 表名: {table}
- 表结构:
{chr(10).join(columns_desc)}
{sample_data_desc}

## 🔍 重要业务术语映射 (car_sales表)
**请特别注意以下字段的业务含义**：
- `model`: 汽车型号 (如A3, Q3, R8等) - 这是具体的车型，不是品牌
- `year`: 汽车年份 - 生产年份
- `price`: 汽车价格 - 销售价格
- `fueltype`: 燃料类型 (Petrol汽油, Diesel柴油, Hybrid混合动力)
- `transmission`: 变速箱类型
- `mileage`: 里程数
- `tax`: 税费

## ⚠️ 常见业务理解错误预防
1. **品牌 vs 型号**:
   - ❌ 错误: "品牌"指model字段
   - ✅ 正确: "品牌"在此数据中实际指的是奥迪品牌下的不同型号(model)
   - 当用户说"品牌"时，应理解为要按model字段分组统计

2. **销售数量 vs 价格**:
   - 销售数量: 使用COUNT(*)统计记录数
   - 销售价格: 使用price字段

## 🧠 思维链推理流程
在生成SQL前，请按以下步骤思考：
1. **理解用户意图**: 用户想要什么信息？
2. **识别关键字段**: 需要哪些数据库字段？
3. **确定查询类型**: 是简单查询、聚合、排序还是筛选？
4. **生成SQL语句**: 构建准确的SQL查询
5. **验证逻辑**: 确保SQL逻辑符合用户意图

## 📝 Few-Shot示例
**示例1**:
用户: "查询每个品牌的汽车销售数量"
思考: 用户说"品牌"实际指model字段，要统计每个型号的数量
SQL: `SELECT model, COUNT(*) AS sales_count FROM car_sales GROUP BY model ORDER BY sales_count DESC;`

**示例2**:
用户: "统计不同燃料类型的平均价格"
思考: 按fueltype分组，计算price的平均值
SQL: `SELECT fueltype, AVG(price) AS avg_price FROM car_sales GROUP BY fueltype;`

**示例3**:
用户: "查找价格最高的汽车型号"
思考: 按price降序排列，取第一条记录的model
SQL: `SELECT model FROM car_sales ORDER BY price DESC LIMIT 1;`

## 用户查询: {query}

{f"上下文信息：{context}" if context else ""}

请按照思维链流程分析，然后生成准确的SQL查询语句。
要求：
1. SQL语法正确，符合标准SQL规范
2. 表名和字段名准确匹配
3. 查询逻辑符合用户意图
4. 考虑性能优化（如适当使用LIMIT）
5. 只返回SQL语句，不要包含其他解释

SQL查询：
"""

        # 调用Agent生成SQL
        import requests

        model_sql = f"""
        SELECT answer FROM mindsdb.gemini_native_working
        WHERE question = '{sql_prompt.replace("'", "''")}'
        """

        response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": model_sql},
            headers={"Content-Type": "application/json"},
            timeout=60  # 增加超时时间到60秒
        )

        if response.status_code == 200:
            result = response.json()
            if result.get('data') and len(result['data']) > 0:
                agent_response = result['data'][0][0] if result['data'][0] else ""

                # 提取SQL语句
                sql = _extract_sql_from_response(agent_response)

                # 评估置信度
                confidence = _evaluate_sql_confidence(sql, query, table_structure)

                # 生成建议
                suggestions = _generate_sql_suggestions(sql, query, table_structure)

                logger.info(f"Agent SQL生成成功: {sql}")
                return {
                    'sql': sql,
                    'confidence': confidence,
                    'suggestions': suggestions,
                    'raw_response': agent_response
                }

        # 如果Agent调用失败，使用备用方法
        return await _generate_fallback_sql(query, database, table, table_structure)

    except Exception as e:
        logger.error(f"Agent SQL生成失败: {e}")
        return await _generate_fallback_sql(query, database, table, table_structure)

def _extract_sql_from_response(response: str) -> str:
    """从Agent响应中提取SQL语句"""
    try:
        # 移除常见的前缀和后缀
        sql = response.strip()

        # 移除markdown代码块标记
        import re

        # 处理完整的markdown代码块
        markdown_pattern = r'```(?:sql)?\s*(.*?)\s*```'
        markdown_match = re.search(markdown_pattern, sql, re.DOTALL | re.IGNORECASE)
        if markdown_match:
            sql = markdown_match.group(1).strip()

        # 移除行内代码标记
        sql = re.sub(r'`+', '', sql)

        # 查找SQL语句的开始
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'WITH']
        for keyword in sql_keywords:
            if keyword in sql.upper():
                start_idx = sql.upper().find(keyword)
                sql = sql[start_idx:]
                break

        # 移除多余的解释文本
        lines = sql.split('\n')
        sql_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('--') and not line.startswith('#'):
                sql_lines.append(line)

        sql = ' '.join(sql_lines)

        # 移除多余的空格和特殊字符
        sql = re.sub(r'\s+', ' ', sql).strip()

        # 确保SQL以分号结尾
        if not sql.endswith(';'):
            sql += ';'

        return sql

    except Exception as e:
        logger.error(f"提取SQL失败: {e}")
        return response.strip()

def _evaluate_sql_confidence(sql: str, query: str, table_structure: Dict[str, Any]) -> float:
    """评估SQL生成的置信度"""
    try:
        confidence = 0.5  # 基础置信度

        # 检查SQL语法基础结构
        if 'SELECT' in sql.upper():
            confidence += 0.2

        # 检查表名是否正确
        table_columns = [col['name'] for col in table_structure.get('columns', [])]
        for col in table_columns:
            if col in sql:
                confidence += 0.1

        # 检查查询意图匹配
        if '统计' in query or '汇总' in query or 'COUNT' in sql.upper():
            confidence += 0.1

        if '排序' in query or 'ORDER BY' in sql.upper():
            confidence += 0.1

        # 限制置信度范围
        return min(max(confidence, 0.1), 1.0)

    except Exception as e:
        logger.error(f"评估置信度失败: {e}")
        return 0.5

def _generate_sql_suggestions(sql: str, query: str, table_structure: Dict[str, Any]) -> List[str]:
    """生成SQL优化建议"""
    suggestions = []

    try:
        # 性能建议
        if 'LIMIT' not in sql.upper():
            suggestions.append("建议添加LIMIT限制返回行数以提高性能")

        # 索引建议
        if 'WHERE' in sql.upper():
            suggestions.append("考虑为WHERE条件中的字段创建索引")

        # 查询优化建议
        if 'SELECT *' in sql.upper():
            suggestions.append("建议只选择需要的字段而不是使用SELECT *")

        # 数据类型建议
        if len(table_structure.get('columns', [])) > 5:
            suggestions.append("表字段较多，建议明确指定需要的字段")

        return suggestions[:3]  # 最多返回3个建议

    except Exception as e:
        logger.error(f"生成建议失败: {e}")
        return ["查询已生成，建议执行前先验证结果"]

async def _generate_fallback_sql(query: str, database: str, table: str, table_structure: Dict[str, Any]) -> Dict[str, Any]:
    """备用SQL生成方法"""
    try:
        columns = [col['name'] for col in table_structure.get('columns', [])]

        # 基于关键词的简单SQL生成
        sql = f"SELECT * FROM {database}.{table}"

        # 分析查询意图
        if '统计' in query or '汇总' in query or 'COUNT' in query.upper():
            if len(columns) > 0:
                sql = f"SELECT COUNT(*) as total_count FROM {database}.{table}"

        elif '按' in query and '分组' in query:
            # 寻找分组字段
            group_field = None
            for col in columns:
                if col in query:
                    group_field = col
                    break

            if group_field:
                sql = f"SELECT {group_field}, COUNT(*) as count FROM {database}.{table} GROUP BY {group_field}"

        elif '排序' in query or '最大' in query or '最小' in query:
            if len(columns) > 0:
                order_field = columns[0]  # 使用第一个字段
                for col in columns:
                    if col in query:
                        order_field = col
                        break

                order_dir = 'DESC' if '最大' in query or '降序' in query else 'ASC'
                sql = f"SELECT * FROM {database}.{table} ORDER BY {order_field} {order_dir}"

        # 添加LIMIT
        if 'LIMIT' not in sql.upper():
            sql += " LIMIT 10"

        # 确保以分号结尾
        if not sql.endswith(';'):
            sql += ';'

        return {
            'sql': sql,
            'confidence': 0.6,
            'suggestions': ["使用备用方法生成的SQL，建议验证后执行"],
            'raw_response': f"备用生成: {sql}"
        }

    except Exception as e:
        logger.error(f"备用SQL生成失败: {e}")
        return {
            'sql': f"SELECT * FROM {database}.{table} LIMIT 10;",
            'confidence': 0.3,
            'suggestions': ["生成基础查询，请根据需要调整"],
            'raw_response': f"基础查询: SELECT * FROM {database}.{table} LIMIT 10;"
        }

async def _validate_generated_sql(sql: str, database: str, table: str) -> Dict[str, Any]:
    """验证生成的SQL"""
    try:
        # 基础语法检查
        validation_result = {
            'is_valid': True,
            'issues': [],
            'warnings': []
        }

        # 检查SQL基础结构
        if not sql.strip():
            validation_result['is_valid'] = False
            validation_result['issues'].append("SQL为空")
            return validation_result

        # 检查是否包含表名
        if table not in sql:
            validation_result['warnings'].append(f"SQL中未找到表名 {table}")

        # 检查是否包含数据库名
        if database not in sql:
            validation_result['warnings'].append(f"SQL中未找到数据库名 {database}")

        # 检查危险操作
        dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER']
        for keyword in dangerous_keywords:
            if keyword in sql.upper():
                validation_result['is_valid'] = False
                validation_result['issues'].append(f"包含危险操作: {keyword}")

        return validation_result

    except Exception as e:
        logger.error(f"SQL验证失败: {e}")
        return {
            'is_valid': False,
            'issues': [f"验证失败: {str(e)}"],
            'warnings': []
        }

async def _generate_sql_explanation(agent_name: str, query: str, sql: str, table_structure: Dict[str, Any]) -> str:
    """生成SQL解释"""
    try:
        columns_info = ", ".join([f"{col['name']}({col['type']})" for col in table_structure.get('columns', [])])

        explanation_prompt = f"""
请用1-2句话简洁解释以下SQL查询：

用户查询：{query}
生成的SQL：{sql}

要求：
1. 只说明查询的核心目的（1句话）
2. 如有必要，简要说明查询逻辑（1句话）
3. 避免技术细节和复杂分析
4. 使用通俗易懂的语言

请用简洁的中文回答，总长度不超过100字。
"""

        # 调用Agent生成解释
        import requests

        model_sql = f"""
        SELECT answer FROM mindsdb.gemini_native_working
        WHERE question = '{explanation_prompt.replace("'", "''")}'
        """

        response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": model_sql},
            headers={"Content-Type": "application/json"},
            timeout=60  # 增加超时时间到60秒
        )

        if response.status_code == 200:
            result = response.json()
            if result.get('data') and len(result['data']) > 0:
                explanation = result['data'][0][0] if result['data'][0] else ""
                return explanation

        # 备用解释
        return f"这个SQL查询用于从{table_structure.get('column_count', 0)}个字段的表中检索数据，根据用户查询'{query}'生成相应的结果。"

    except Exception as e:
        logger.error(f"生成SQL解释失败: {e}")
        return f"SQL查询解释：根据查询'{query}'生成的SQL语句，用于从数据库中获取相关数据。"

# ==================== Agent技能配置相关API ====================

class AgentSkillConfigRequest(BaseModel):
    agent_name: str
    skills: List[Dict[str, Any]]

class AgentSkillConfigResponse(BaseModel):
    success: bool
    agent_name: str
    skills_configured: int
    message: str
    error_message: Optional[str] = None

@router.post("/agent/skills/configure", response_model=AgentSkillConfigResponse)
async def configure_agent_skills(request: AgentSkillConfigRequest):
    """配置Agent的技能"""
    try:
        logger.info(f"配置Agent技能: {request.agent_name} -> {len(request.skills)}个技能")

        # 1. 验证Agent是否存在
        agent_exists = await _check_agent_exists(request.agent_name)
        if not agent_exists:
            return AgentSkillConfigResponse(
                success=False,
                agent_name=request.agent_name,
                skills_configured=0,
                message="",
                error_message=f"Agent '{request.agent_name}' 不存在"
            )

        # 2. 更新Agent的技能配置
        update_result = await _update_agent_skills(request.agent_name, request.skills)

        if update_result['success']:
            return AgentSkillConfigResponse(
                success=True,
                agent_name=request.agent_name,
                skills_configured=len(request.skills),
                message=f"成功配置 {len(request.skills)} 个技能",
                error_message=None
            )
        else:
            return AgentSkillConfigResponse(
                success=False,
                agent_name=request.agent_name,
                skills_configured=0,
                message="",
                error_message=update_result['error']
            )

    except Exception as e:
        logger.error(f"配置Agent技能失败: {str(e)}")
        return AgentSkillConfigResponse(
            success=False,
            agent_name=request.agent_name,
            skills_configured=0,
            message="",
            error_message=str(e)
        )

async def _check_agent_exists(agent_name: str) -> bool:
    """检查Agent是否存在"""
    try:
        import requests

        query_sql = f"SELECT name FROM mindsdb.agents WHERE name = '{agent_name}'"

        response = requests.post(
            "http://localhost:47334/api/sql/query",
            json={"query": query_sql},
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            return result.get('data') and len(result['data']) > 0

        return False

    except Exception as e:
        logger.error(f"检查Agent存在性失败: {e}")
        return False

async def _update_agent_skills(agent_name: str, skills: List[Dict[str, Any]]) -> Dict[str, Any]:
    """更新Agent的技能配置"""
    try:
        import requests

        # 为每个技能使用ADD SKILL语法
        for skill in skills:
            skill_name = skill.get('name', f"{agent_name}_sql_skill")
            skill_type = skill.get('type', 'text2sql')
            skill_description = skill.get('description', '将自然语言转换为SQL查询的技能')
            skill_databases = skill.get('databases', ['datasource'])
            skill_tables = skill.get('tables', ['car_info', 'chat_llm_mindsdb_docs'])

            # 构建ADD SKILL查询
            databases_str = str(skill_databases).replace("'", '"')
            tables_str = str(skill_tables).replace("'", '"')

            add_skill_query = f"""ADD SKILL {skill_name} TO {agent_name} USING type = '{skill_type}', description = '{skill_description}', databases = {databases_str}, tables = {tables_str}"""

            logger.info(f"执行技能添加SQL: {add_skill_query}")

            response = requests.post(
                "http://localhost:47334/api/sql/query",
                json={"query": add_skill_query},
                headers={"Content-Type": "application/json"},
                timeout=15
            )

            if response.status_code == 200:
                logger.info(f"Agent技能添加成功: {agent_name} -> {skill_name}")
            else:
                error_msg = f"技能添加失败，状态码: {response.status_code}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}

        return {'success': True, 'error': None}

    except Exception as e:
        error_msg = f"更新Agent技能失败: {str(e)}"
        logger.error(error_msg)
        return {'success': False, 'error': error_msg}


# AI结果总结相关模型和API
class AIResultSummaryRequest(BaseModel):
    query: str
    sql: str
    total_records: int
    sample_data: List[Dict[str, Any]]
    columns: List[str]
    is_continue_query: bool = False
    original_context: Optional[Dict[str, Any]] = None

@router.post("/ai-result-summary")
async def generate_ai_result_summary(request: AIResultSummaryRequest):
    """
    生成AI中文结果总结
    """
    try:
        logger.info(f"开始生成AI结果总结，查询: {request.query}, 是否继续查询: {request.is_continue_query}")

        # 根据是否为继续查询选择不同的提示词模板
        if request.is_continue_query and request.original_context:
            summary_prompt = await _build_continue_query_prompt(request)
        else:
            summary_prompt = await _build_normal_query_prompt(request)

        # 调用Gemini API生成总结
        import requests

        gemini_api_key = "AIzaSyCri8-XPGXaJGKRLWsod3KBXZdDNBD9RqI"  # 使用用户的API密钥
        gemini_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent"

        headers = {
            "Content-Type": "application/json"
        }

        payload = {
            "contents": [{
                "parts": [{
                    "text": summary_prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 1024
            }
        }

        response = requests.post(
            f"{gemini_url}?key={gemini_api_key}",
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                summary_text = result['candidates'][0]['content']['parts'][0]['text']

                # 格式化总结文本
                formatted_summary = summary_text.replace('\n', '<br>')

                logger.info("AI结果总结生成成功")
                return {
                    "success": True,
                    "summary": formatted_summary
                }
            else:
                logger.error("Gemini API返回格式异常")
                return {
                    "success": False,
                    "error_message": "AI总结生成失败：API返回格式异常"
                }
        else:
            logger.error(f"Gemini API调用失败: {response.status_code} - {response.text}")
            return {
                "success": False,
                "error_message": f"AI总结生成失败：API调用错误 {response.status_code}"
            }

    except Exception as e:
        logger.error(f"生成AI结果总结失败: {str(e)}")
        return {
            "success": False,
            "error_message": f"AI总结生成失败: {str(e)}"
        }


# 继续查询功能的请求和响应模型
class ContinueQueryRequest(BaseModel):
    newQuery: str
    context: Dict[str, Any]

class ContinueQueryResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


@router.post("/continue-query", response_model=ContinueQueryResponse)
async def execute_continue_query(request: ContinueQueryRequest):
    """
    执行继续查询功能
    基于前一次查询的上下文，生成新的SQL查询
    """
    try:
        logger.info(f"开始执行继续查询: {request.newQuery}")
        logger.info(f"上下文信息: {request.context}")

        # 验证输入参数
        if not request.newQuery or not request.newQuery.strip():
            return ContinueQueryResponse(
                success=False,
                error_message="新查询内容不能为空"
            )

        if not request.context:
            return ContinueQueryResponse(
                success=False,
                error_message="缺少查询上下文信息"
            )

        # 获取上下文信息
        context = request.context
        previous_sql = context.get('sql', '')
        previous_query = context.get('query', '')
        database = context.get('database', 'datasource')
        table = context.get('table', '')
        agent_name = context.get('agent', '')
        columns = context.get('columns', [])
        sample_data = context.get('sampleData', [])

        # 构建包含上下文的丰富Prompt
        context_prompt = f"""
基于以下查询上下文，生成新的SQL查询：

【上下文信息】
- 原始查询: {previous_query}
- 之前生成的SQL: {previous_sql}
- 数据库: {database}
- 表: {table}
- 可用字段: {', '.join(columns) if columns else '未知'}
- 样本数据: {sample_data[:2] if sample_data else '无'}

【新的查询需求】
{request.newQuery}

请基于上述上下文信息，生成适合的SQL查询。注意：
1. 充分利用上下文中的表结构和字段信息
2. 考虑与前一次查询结果的关联性
3. 确保SQL语法正确且符合数据库规范
4. 如果需要，可以使用子查询或JOIN来关联前一次的查询结果
"""

        # 如果指定了Agent，使用Agent生成SQL
        if agent_name:
            try:
                logger.info(f"使用Agent生成继续查询SQL: {agent_name}")

                # 获取表结构信息
                table_structure = await _get_table_structure_detailed(database, table)

                # 调用现有的Agent SQL生成函数
                agent_result = await _generate_sql_with_agent(
                    agent_name=agent_name,
                    query=context_prompt,
                    database=database,
                    table=table,
                    table_structure=table_structure,
                    context=f"继续查询上下文：{previous_query}"
                )

                if agent_result and agent_result.get('sql'):
                    sql = agent_result.get('sql', '')
                    explanation = agent_result.get('explanation', '')
                    logger.info(f"Agent SQL生成成功: {sql}")

                    # 验证并修复SQL语法
                    fixed_sql = await _validate_and_fix_sql(sql, database, table)
                    logger.info(f"SQL修复后: {fixed_sql}")

                    return ContinueQueryResponse(
                        success=True,
                        data={
                            'sql': fixed_sql,
                            'explanation': explanation,
                            'context_used': True,
                            'agent_used': agent_name,
                            'original_context': context
                        }
                    )
                else:
                    logger.warning(f"Agent生成SQL失败，回退到普通模式")

            except Exception as agent_error:
                logger.error(f"Agent生成SQL出错: {str(agent_error)}")
                # 继续使用普通模式

        # 使用普通模式生成SQL（简化版本）
        logger.info("使用普通模式生成继续查询SQL")

        try:
            # 使用简化的SQL生成逻辑
            generated_sql = await _generate_continue_query_sql(
                new_query=request.newQuery,
                context=context,
                database=database,
                table=table
            )

            # 验证并修复SQL语法
            fixed_sql = await _validate_and_fix_sql(generated_sql, database, table)

            return ContinueQueryResponse(
                success=True,
                data={
                    'sql': fixed_sql,
                    'explanation': f"基于上下文生成的继续查询SQL",
                    'context_used': True,
                    'agent_used': None,
                    'original_context': context
                }
            )

        except Exception as sql_error:
            logger.error(f"普通模式生成SQL失败: {str(sql_error)}")
            return ContinueQueryResponse(
                success=False,
                error_message=f"生成SQL失败: {str(sql_error)}"
            )

    except Exception as e:
        logger.error(f"继续查询执行失败: {str(e)}")
        return ContinueQueryResponse(
            success=False,
            error_message=f"继续查询执行失败: {str(e)}"
        )


async def _generate_continue_query_sql(new_query: str, context: Dict[str, Any], database: str, table: str) -> str:
    """生成继续查询的SQL（简化版本）- 作为Agent生成失败时的备用方案"""
    try:
        previous_sql = context.get('sql', '')
        columns = context.get('columns', [])
        sample_data = context.get('sampleData', [])

        # 基于新查询内容和上下文生成SQL
        if "年份" in new_query and ("平均价格" in new_query or "价格差异" in new_query):
            # 分析不同年份的平均价格差异
            # 基于前面查询的高价车型（R8）进行年份分析
            sql = f"""
            WITH Top5Cars AS (
                SELECT * FROM {database}.{table} ORDER BY price DESC LIMIT 10
            )
            SELECT
                year,
                AVG(price) as avg_price,
                COUNT(*) as car_count,
                MIN(price) as min_price,
                MAX(price) as max_price
            FROM Top5Cars
            WHERE model LIKE '%R8%'
            GROUP BY year
            ORDER BY year
            """.strip()
        elif "分组" in new_query and "年份" in new_query:
            # 按年份分组统计
            sql = f"""
            SELECT
                year,
                COUNT(*) as count,
                AVG(price) as avg_price,
                model
            FROM {database}.{table}
            WHERE price > 100000
            GROUP BY year, model
            ORDER BY year, avg_price DESC
            """.strip()
        elif "趋势" in new_query or "变化" in new_query:
            # 趋势分析
            sql = f"""
            SELECT
                year,
                AVG(price) as avg_price,
                COUNT(*) as count,
                ROUND(AVG(price) - LAG(AVG(price)) OVER (ORDER BY year), 2) as price_change
            FROM {database}.{table}
            WHERE model LIKE '%R8%'
            GROUP BY year
            ORDER BY year
            """.strip()
        else:
            # 默认：基于原查询结果进行扩展分析
            sql = f"""
            SELECT
                year,
                model,
                AVG(price) as avg_price,
                COUNT(*) as count,
                MIN(price) as min_price,
                MAX(price) as max_price
            FROM {database}.{table}
            WHERE price > 100000
            GROUP BY year, model
            ORDER BY year, avg_price DESC
            """.strip()

        logger.info(f"生成的继续查询SQL: {sql}")
        return sql

    except Exception as e:
        logger.error(f"生成继续查询SQL失败: {str(e)}")
        # 返回一个安全的默认查询
        return f"SELECT year, AVG(price) as avg_price FROM {database}.{table} WHERE model LIKE '%R8%' GROUP BY year ORDER BY year"

async def _validate_and_fix_sql(sql: str, database: str, table: str) -> str:
    """验证并修复SQL语法错误"""
    try:
        # 检查常见的SQL语法错误
        sql = sql.strip()

        # 修复缺少WITH子句的问题
        if ") SELECT" in sql and not sql.upper().startswith("WITH"):
            # 找到第一个 ) SELECT 的位置
            parts = sql.split(") SELECT", 1)
            if len(parts) == 2:
                # 构建正确的WITH子句
                first_part = parts[0].strip()
                second_part = parts[1].strip()

                # 提取表名或别名
                if "FROM" in first_part:
                    # 简单的修复：为高价车查询添加WITH子句
                    sql = f"""
                    WITH Top5HighPriceCars AS (
                        {first_part}
                    )
                    SELECT {second_part}
                    """.strip()
                    logger.info(f"修复SQL语法，添加WITH子句: {sql}")

        return sql

    except Exception as e:
        logger.error(f"SQL修复失败: {str(e)}")
        # 返回一个安全的默认查询
        return f"SELECT year, AVG(price) as avg_price FROM {database}.{table} WHERE model LIKE '%R8%' GROUP BY year ORDER BY year"

# ==================== AI总结提示词构建函数 ====================

async def _build_normal_query_prompt(request: AIResultSummaryRequest) -> str:
    """构建普通查询的AI总结提示词"""
    summary_prompt = f"""
请用中文对以下SQL查询结果进行智能总结分析：

**用户查询**: {request.query}
**生成的SQL**: {request.sql}
**总记录数**: {request.total_records}条
**数据列**: {', '.join(request.columns)}

**数据样本** (前5条):
"""

    # 添加样本数据
    for i, row in enumerate(request.sample_data, 1):
        summary_prompt += f"\n{i}. "
        for col, value in row.items():
            summary_prompt += f"{col}: {value}, "
        summary_prompt = summary_prompt.rstrip(', ')

    summary_prompt += f"""

请提供以下内容的中文总结：
1. **数据概览**: 简要描述查询返回的数据内容和规模
2. **关键发现**: 从样本数据中发现的重要信息或趋势
3. **数据特点**: 数据的分布特征、时间范围等
4. **业务洞察**: 这些数据可能反映的业务情况或价值

请用简洁、专业的中文回复，每个部分用2-3句话概括。
"""
    return summary_prompt

async def _build_continue_query_prompt(request: AIResultSummaryRequest) -> str:
    """构建继续查询的AI总结提示词"""
    original_context = request.original_context

    summary_prompt = f"""
你是一个专业的数据分析师。现在需要分析一个基于前一次查询结果的继续查询。

**原始查询**: {original_context.get('query', '')}
**原始查询SQL**: {original_context.get('sql', '')}
**原始查询结果**: 查询返回{original_context.get('rowCount', 0)}条记录

**继续查询**: {request.query}
**继续查询SQL**: {request.sql}
**继续查询结果**: 查询返回{request.total_records}条记录

**继续查询数据样本**:
"""

    # 添加继续查询的样本数据
    for i, row in enumerate(request.sample_data, 1):
        summary_prompt += f"\n{i}. "
        for col, value in row.items():
            summary_prompt += f"{col}: {value}, "
        summary_prompt = summary_prompt.rstrip(', ')

    summary_prompt += f"""

请提供以下中文分析：
1. **查询关联性分析**: 说明两次查询之间的逻辑关系和连续性
2. **结果对比分析**: 对比分析两次查询结果的关联性和差异
3. **数据深度洞察**: 基于完整上下文提供深入的数据分析
4. **综合业务洞察**: 结合前后两次查询提供全面的业务建议
5. **后续分析建议**: 推荐可能的下一步分析方向

请用专业但易懂的中文进行分析，重点突出基于上下文的深度分析价值。
"""
    return summary_prompt
