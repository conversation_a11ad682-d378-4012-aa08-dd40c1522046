"""
中文NLP理解API
提供中文自然语言理解的REST接口
"""
from fastapi import APIRouter, Query
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging

from core.chinese_nlp_engine import chinese_nlp_engine
from core.api_response import APIResponseBuilder
from core.exception_handlers import BusinessException

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/nlp", tags=["chinese-nlp"])

class NLPUnderstandRequest(BaseModel):
    """NLP理解请求"""
    text: str = Field(..., min_length=1, max_length=1000, description="要理解的中文文本")
    session_id: Optional[str] = Field(None, description="会话ID，用于上下文管理")
    context: Optional[Dict[str, Any]] = Field(None, description="额外的上下文信息")

class BatchNLPRequest(BaseModel):
    """批量NLP理解请求"""
    texts: List[str] = Field(..., min_items=1, max_items=20, description="要理解的中文文本列表")
    session_id: Optional[str] = Field(None, description="会话ID")

class TextProcessRequest(BaseModel):
    """文本预处理请求"""
    text: str = Field(..., min_length=1, max_length=1000, description="要处理的文本")

@router.post("/understand")
async def understand_chinese_text(request: NLPUnderstandRequest):
    """理解中文自然语言文本"""
    try:
        result = await chinese_nlp_engine.understand_text(
            text=request.text,
            session_id=request.session_id,
            context=request.context
        )
        
        return APIResponseBuilder.success(
            data=result.to_dict(),
            message="中文文本理解成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"中文文本理解失败: {str(e)}")
        raise BusinessException(f"中文文本理解失败: {str(e)}")

@router.post("/batch-understand")
async def batch_understand_chinese_text(request: BatchNLPRequest):
    """批量理解中文文本"""
    try:
        results = []
        
        for i, text in enumerate(request.texts):
            try:
                result = await chinese_nlp_engine.understand_text(
                    text=text,
                    session_id=request.session_id,
                    context={"batch_index": i}
                )
                results.append({
                    "index": i,
                    "text": text,
                    "result": result.to_dict(),
                    "success": True
                })
            except Exception as e:
                logger.error(f"批量理解第{i}个文本失败: {str(e)}")
                results.append({
                    "index": i,
                    "text": text,
                    "result": None,
                    "success": False,
                    "error": str(e)
                })
        
        # 统计结果
        successful_count = sum(1 for r in results if r["success"])
        total_count = len(results)
        
        return APIResponseBuilder.success(
            data={
                "results": results,
                "summary": {
                    "total": total_count,
                    "successful": successful_count,
                    "failed": total_count - successful_count,
                    "success_rate": successful_count / total_count * 100
                }
            },
            message=f"批量理解完成，成功率: {successful_count}/{total_count}"
        ).dict()
        
    except Exception as e:
        logger.error(f"批量中文文本理解失败: {str(e)}")
        raise BusinessException(f"批量中文文本理解失败: {str(e)}")

@router.post("/preprocess")
async def preprocess_text(request: TextProcessRequest):
    """预处理中文文本"""
    try:
        from core.chinese_text_processor import text_processor
        
        processed_info = text_processor.process_text(request.text)
        
        return APIResponseBuilder.success(
            data=processed_info,
            message="文本预处理成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"文本预处理失败: {str(e)}")
        raise BusinessException(f"文本预处理失败: {str(e)}")

@router.get("/context/{session_id}")
async def get_conversation_context(session_id: str):
    """获取对话上下文"""
    try:
        context = chinese_nlp_engine.get_conversation_context(session_id)
        
        if context:
            return APIResponseBuilder.success(
                data=context,
                message="对话上下文获取成功"
            ).dict()
        else:
            return APIResponseBuilder.not_found(
                message=f"会话 {session_id} 的上下文不存在"
            ).dict()
        
    except Exception as e:
        logger.error(f"获取对话上下文失败: {str(e)}")
        raise BusinessException(f"获取对话上下文失败: {str(e)}")

@router.delete("/context/{session_id}")
async def clear_conversation_context(session_id: str):
    """清空对话上下文"""
    try:
        chinese_nlp_engine.clear_conversation_context(session_id)
        
        return APIResponseBuilder.success(
            data={"session_id": session_id, "cleared": True},
            message="对话上下文清空成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"清空对话上下文失败: {str(e)}")
        raise BusinessException(f"清空对话上下文失败: {str(e)}")

@router.get("/examples")
async def get_nlp_examples():
    """获取NLP理解示例"""
    examples = [
        {
            "category": "简单查询",
            "examples": [
                "查询所有用户的姓名和年龄",
                "显示产品表中的所有数据",
                "列出订单信息"
            ]
        },
        {
            "category": "条件查询",
            "examples": [
                "查询年龄大于25岁的用户",
                "找出销售额超过1000的订单",
                "显示北京地区的客户信息"
            ]
        },
        {
            "category": "聚合统计",
            "examples": [
                "统计用户总数",
                "计算平均销售额",
                "查询最高销售额的产品"
            ]
        },
        {
            "category": "排序和限制",
            "examples": [
                "按销售额降序排列前10个产品",
                "查询年龄最大的5个用户",
                "显示最近的20条订单"
            ]
        },
        {
            "category": "时间查询",
            "examples": [
                "查询昨天的订单",
                "统计本月的销售数据",
                "分析去年的用户增长趋势"
            ]
        },
        {
            "category": "分析预测",
            "examples": [
                "分析用户购买行为趋势",
                "预测下个月的销售额",
                "对比不同地区的销售表现"
            ]
        }
    ]
    
    return APIResponseBuilder.success(
        data={"examples": examples},
        message="NLP理解示例获取成功"
    ).dict()

@router.get("/capabilities")
async def get_nlp_capabilities():
    """获取NLP理解能力说明"""
    capabilities = {
        "intent_types": [
            {"type": "query", "description": "简单查询数据"},
            {"type": "analyze", "description": "分析数据（需要AI分析功能）"},
            {"type": "predict", "description": "预测数据（需要预测模型）"},
            {"type": "aggregate", "description": "聚合统计（求和、平均、计数等）"},
            {"type": "filter", "description": "过滤数据"},
            {"type": "sort", "description": "排序数据"},
            {"type": "count", "description": "计数"},
            {"type": "compare", "description": "比较分析"},
            {"type": "trend", "description": "趋势分析"}
        ],
        "entity_types": [
            {"type": "table", "description": "表名"},
            {"type": "column", "description": "列名"},
            {"type": "database", "description": "数据库名"},
            {"type": "value", "description": "数值"},
            {"type": "date", "description": "日期"},
            {"type": "time_range", "description": "时间范围"},
            {"type": "condition", "description": "条件"},
            {"type": "function", "description": "函数"},
            {"type": "operator", "description": "操作符"},
            {"type": "limit", "description": "限制数量"},
            {"type": "order", "description": "排序方式"}
        ],
        "supported_features": [
            "中文分词和预处理",
            "意图识别和分类",
            "命名实体识别",
            "查询结构提取",
            "上下文管理",
            "置信度评估",
            "错误检测和建议"
        ],
        "language_support": {
            "primary": "中文（简体）",
            "secondary": ["英文标识符", "中英混合表达"],
            "special_handling": ["数字表达", "时间表达", "专业术语"]
        }
    }
    
    return APIResponseBuilder.success(
        data=capabilities,
        message="NLP理解能力说明获取成功"
    ).dict()

@router.post("/test")
async def test_nlp_understanding():
    """测试NLP理解功能"""
    test_cases = [
        "查询所有用户的姓名和年龄",
        "统计销售额大于1000的订单数量",
        "分析最近一个月的用户增长趋势",
        "预测下个季度的销售额",
        "按销售额降序排列前10个产品"
    ]
    
    try:
        results = []
        
        for i, text in enumerate(test_cases):
            try:
                result = await chinese_nlp_engine.understand_text(
                    text=text,
                    session_id="test_session",
                    context={"test_case": i + 1}
                )
                
                results.append({
                    "test_case": i + 1,
                    "input_text": text,
                    "primary_intent": result.get_primary_intent().intent_type.value if result.get_primary_intent() else "unknown",
                    "entity_count": len(result.entities),
                    "confidence_level": result.confidence_level.value,
                    "processing_time": result.processing_time,
                    "success": True
                })
            except Exception as e:
                results.append({
                    "test_case": i + 1,
                    "input_text": text,
                    "success": False,
                    "error": str(e)
                })
        
        # 统计测试结果
        successful_tests = sum(1 for r in results if r["success"])
        total_tests = len(results)
        avg_processing_time = sum(r.get("processing_time", 0) for r in results if r["success"]) / successful_tests if successful_tests > 0 else 0
        
        summary = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": successful_tests / total_tests * 100,
            "average_processing_time": avg_processing_time
        }
        
        return APIResponseBuilder.success(
            data={
                "test_results": results,
                "summary": summary
            },
            message=f"NLP理解功能测试完成，成功率: {successful_tests}/{total_tests}"
        ).dict()
        
    except Exception as e:
        logger.error(f"NLP理解功能测试失败: {str(e)}")
        raise BusinessException(f"NLP理解功能测试失败: {str(e)}")
