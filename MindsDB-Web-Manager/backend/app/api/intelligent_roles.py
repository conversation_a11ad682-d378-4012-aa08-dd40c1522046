"""
智能角色生成API
提供智能角色推荐和角色管理的REST接口
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
import logging

try:
    from ..core.intelligent_role_generator import IntelligentRoleGenerator, BusinessRole, RoleRecommendation
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.intelligent_role_generator import IntelligentRoleGenerator, BusinessRole, RoleRecommendation
try:
    from ..core.enhanced_prompt_generation_service import EnhancedPromptGenerationService
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.enhanced_prompt_generation_service import EnhancedPromptGenerationService
try:
    from ..core.database_analysis_service import DatabaseAnalysisService
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.database_analysis_service import DatabaseAnalysisService
try:
    from ..core.api_utils import create_success_response, create_error_response
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.api_utils import create_success_response, create_error_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/intelligent-roles", tags=["智能角色生成"])

# Pydantic模型定义
class RoleRecommendationRequest(BaseModel):
    """角色推荐请求"""
    database_name: str = Field(..., description="数据库名称")
    use_llm: bool = Field(True, description="是否使用LLM增强推荐")
    max_alternatives: int = Field(3, description="最大备选角色数量")

class RoleBasedPromptRequest(BaseModel):
    """基于角色的提示词生成请求"""
    role_name: str = Field(..., description="角色名称")
    query_type: str = Field(..., description="查询类型")
    user_intent: str = Field(..., description="用户意图")
    database_name: str = Field(..., description="数据库名称")
    expertise_level: str = Field("intermediate", description="专业水平")
    custom_requirements: List[str] = Field(default=[], description="自定义要求")

class CustomRoleRequest(BaseModel):
    """自定义角色创建请求"""
    name: str = Field(..., description="角色英文名称")
    title: str = Field(..., description="角色中文标题")
    description: str = Field(..., description="角色描述")
    expertise_areas: List[str] = Field(..., description="专业领域")
    typical_queries: List[str] = Field(..., description="典型查询")

# 依赖注入
def get_role_generator() -> IntelligentRoleGenerator:
    """获取角色生成器实例"""
    return IntelligentRoleGenerator()

def get_prompt_service() -> EnhancedPromptGenerationService:
    """获取提示词服务实例"""
    return EnhancedPromptGenerationService()

def get_database_service() -> DatabaseAnalysisService:
    """获取数据库分析服务实例"""
    return DatabaseAnalysisService()

@router.post("/recommend")
async def recommend_roles(
    request: RoleRecommendationRequest,
    role_generator: IntelligentRoleGenerator = Depends(get_role_generator),
    db_service: DatabaseAnalysisService = Depends(get_database_service)
):
    """推荐智能角色"""
    try:
        logger.info(f"开始推荐角色: {request.database_name}")
        
        # 1. 分析数据库结构
        database_analysis = await db_service.analyze_database(request.database_name)
        
        if not database_analysis:
            return create_error_response(f"无法分析数据库: {request.database_name}", 404)
        
        # 2. 生成角色推荐
        recommendation = role_generator.generate_role_recommendation(
            database_analysis=database_analysis,
            use_llm=request.use_llm,
            max_alternatives=request.max_alternatives
        )
        
        return create_success_response({
            "recommendation": recommendation.to_dict(),
            "message": f"角色推荐生成成功: {recommendation.primary_role.title}"
        })
        
    except Exception as e:
        logger.error(f"推荐角色失败: {e}")
        return create_error_response(f"推荐角色失败: {str(e)}", 500)

@router.get("/available")
async def list_available_roles(
    role_generator: IntelligentRoleGenerator = Depends(get_role_generator)
):
    """列出所有可用角色"""
    try:
        roles = role_generator.list_available_roles()
        
        return create_success_response({
            "roles": [role.to_dict() for role in roles],
            "total_roles": len(roles)
        })
        
    except Exception as e:
        logger.error(f"获取可用角色失败: {e}")
        return create_error_response(f"获取可用角色失败: {str(e)}", 500)

@router.get("/roles/{role_name}")
async def get_role_details(
    role_name: str,
    role_generator: IntelligentRoleGenerator = Depends(get_role_generator)
):
    """获取角色详情"""
    try:
        role = role_generator.get_role_by_name(role_name)
        
        if not role:
            return create_error_response(f"角色不存在: {role_name}", 404)
        
        return create_success_response({
            "role": role.to_dict()
        })
        
    except Exception as e:
        logger.error(f"获取角色详情失败: {e}")
        return create_error_response(f"获取角色详情失败: {str(e)}", 500)

@router.post("/generate-prompt")
async def generate_role_based_prompt(
    request: RoleBasedPromptRequest,
    prompt_service: EnhancedPromptGenerationService = Depends(get_prompt_service),
    db_service: DatabaseAnalysisService = Depends(get_database_service)
):
    """基于角色生成提示词"""
    try:
        logger.info(f"基于角色生成提示词: {request.role_name}")
        
        # 1. 获取角色信息
        role = prompt_service.get_role_by_name(request.role_name)
        if not role:
            return create_error_response(f"角色不存在: {request.role_name}", 404)
        
        # 2. 分析数据库结构
        database_analysis = await db_service.analyze_database(request.database_name)
        if not database_analysis:
            return create_error_response(f"无法分析数据库: {request.database_name}", 404)
        
        # 3. 构建提示词请求
        try:
            from core.enhanced_prompt_generation_service import PromptRequest, QueryType
        except ImportError:
            # 如果相对导入失败，尝试绝对导入
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from core.enhanced_prompt_generation_service import PromptRequest, QueryType
        
        # 转换查询类型
        try:
            query_type = QueryType(request.query_type)
        except ValueError:
            query_type = QueryType.GENERAL_ANALYSIS
        
        prompt_request = PromptRequest(
            query_type=query_type,
            user_intent=request.user_intent,
            schema_info=database_analysis.to_dict(),
            expertise_level=request.expertise_level,
            domain=role.name,
            custom_requirements=request.custom_requirements
        )
        
        # 4. 生成角色增强的提示词
        prompt_response = await prompt_service.generate_role_based_prompt(
            request=prompt_request,
            role=role
        )
        
        return create_success_response({
            "prompt": {
                "system_prompt": prompt_response.system_prompt,
                "user_prompt": prompt_response.user_prompt,
                "examples": prompt_response.examples,
                "quality_score": prompt_response.quality_score,
                "optimization_suggestions": prompt_response.optimization_suggestions
            },
            "role": role.to_dict(),
            "message": f"基于{role.title}生成提示词成功"
        })
        
    except Exception as e:
        logger.error(f"基于角色生成提示词失败: {e}")
        return create_error_response(f"基于角色生成提示词失败: {str(e)}", 500)

@router.post("/analyze-and-recommend")
async def analyze_and_recommend(
    database_name: str,
    use_llm: bool = True,
    prompt_service: EnhancedPromptGenerationService = Depends(get_prompt_service),
    db_service: DatabaseAnalysisService = Depends(get_database_service)
):
    """分析数据库并推荐角色（一体化接口）"""
    try:
        logger.info(f"分析数据库并推荐角色: {database_name}")
        
        # 1. 分析数据库结构
        database_analysis = await db_service.analyze_database(database_name)
        
        if not database_analysis:
            return create_error_response(f"无法分析数据库: {database_name}", 404)
        
        # 2. 生成角色推荐
        recommendation = await prompt_service.generate_role_recommendation(
            database_analysis=database_analysis,
            use_llm=use_llm
        )
        
        return create_success_response({
            "database_analysis": {
                "database_name": database_analysis.database_name,
                "table_count": len(database_analysis.tables),
                "total_columns": sum(len(table.columns) for table in database_analysis.tables),
                "main_tables": [table.name for table in database_analysis.tables[:5]]
            },
            "role_recommendation": recommendation.to_dict(),
            "message": f"数据库分析和角色推荐完成"
        })
        
    except Exception as e:
        logger.error(f"分析数据库并推荐角色失败: {e}")
        return create_error_response(f"分析数据库并推荐角色失败: {str(e)}", 500)

@router.get("/business-domains")
async def get_business_domains():
    """获取支持的业务领域"""
    try:
        domains = {
            "sales": {
                "name": "销售分析",
                "description": "销售数据分析，包括客户行为、产品销售、收入趋势等",
                "keywords": ["sales", "customer", "order", "product", "revenue"]
            },
            "finance": {
                "name": "财务分析", 
                "description": "财务数据分析，包括成本控制、预算管理、盈利分析等",
                "keywords": ["finance", "accounting", "budget", "expense", "profit"]
            },
            "hr": {
                "name": "人力资源",
                "description": "人力资源数据分析，包括员工绩效、薪酬分析、组织结构等",
                "keywords": ["employee", "staff", "hr", "payroll", "department"]
            },
            "marketing": {
                "name": "市场营销",
                "description": "市场营销数据分析，包括活动效果、用户转化、品牌影响力等",
                "keywords": ["marketing", "campaign", "advertisement", "conversion"]
            },
            "operations": {
                "name": "运营分析",
                "description": "运营数据分析，包括库存管理、供应链优化、物流效率等",
                "keywords": ["inventory", "supply", "logistics", "warehouse"]
            },
            "product": {
                "name": "产品分析",
                "description": "产品数据分析，包括用户行为、功能使用、产品优化等",
                "keywords": ["product", "feature", "usage", "user", "analytics"]
            }
        }
        
        return create_success_response({
            "business_domains": domains,
            "total_domains": len(domains)
        })
        
    except Exception as e:
        logger.error(f"获取业务领域失败: {e}")
        return create_error_response(f"获取业务领域失败: {str(e)}", 500)

@router.get("/health")
async def get_role_service_health():
    """获取角色服务健康状态"""
    try:
        # 测试各个组件
        role_generator = IntelligentRoleGenerator()
        available_roles = role_generator.list_available_roles()
        
        health_status = {
            "status": "healthy",
            "available_roles_count": len(available_roles),
            "supported_domains": 6,
            "llm_integration": "available",
            "timestamp": logger.info.__module__  # 简单的时间戳替代
        }
        
        return create_success_response({
            "health": health_status
        })
        
    except Exception as e:
        logger.error(f"获取角色服务健康状态失败: {e}")
        return create_error_response(f"获取角色服务健康状态失败: {str(e)}", 500)
