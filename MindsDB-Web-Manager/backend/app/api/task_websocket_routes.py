"""
任务状态WebSocket路由
实现任务状态实时推送的WebSocket端点
"""

import json
import logging
import uuid
from typing import Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

from app.websocket.task_status_service import (
    get_websocket_service, 
    TaskStatusWebSocketService,
    notify_task_status_change,
    notify_task_progress,
    notify_task_log,
    TaskStatus
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/task", tags=["Task WebSocket"])

class TaskUpdateRequest(BaseModel):
    """任务更新请求模型"""
    task_id: str
    status: str
    progress: float = 0.0
    message: str = "任务状态更新"

@router.websocket("/ws/status")
async def websocket_task_status(
    websocket: WebSocket,
    user_id: Optional[str] = Query(None),
    token: Optional[str] = Query(None)
):
    """
    任务状态WebSocket端点
    
    Args:
        websocket: WebSocket连接
        user_id: 用户ID（可选）
        token: 认证令牌（可选）
    """
    # 生成连接ID
    connection_id = str(uuid.uuid4())
    
    # 获取WebSocket服务
    service = get_websocket_service()
    
    try:
        # 接受WebSocket连接
        await websocket.accept()
        logger.info(f"任务状态WebSocket连接已建立: {connection_id}")
        
        # 添加连接到服务
        await service.add_connection(connection_id, websocket, user_id)
        
        # 启动后台任务（如果还没启动）
        if not service.running:
            await service.start_background_tasks()
        
        # 监听客户端消息
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                await service.handle_client_message(connection_id, data)
                
            except WebSocketDisconnect:
                logger.info(f"任务状态WebSocket连接断开: {connection_id}")
                break
            except Exception as e:
                logger.error(f"处理任务状态WebSocket消息异常: {connection_id} - {e}")
                # 发送错误消息给客户端
                error_message = {
                    'message_type': 'error',
                    'error': str(e),
                    'timestamp': str(uuid.uuid4())
                }
                try:
                    await websocket.send_text(json.dumps(error_message))
                except:
                    break
    
    except Exception as e:
        logger.error(f"任务状态WebSocket连接异常: {connection_id} - {e}")
    
    finally:
        # 清理连接
        await service.remove_connection(connection_id)
        logger.info(f"任务状态WebSocket连接已清理: {connection_id}")

@router.get("/ws/test")
async def websocket_test_page():
    """WebSocket测试页面"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>任务状态 WebSocket 测试</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .status { padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
            .connected { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .disconnected { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .message { background-color: #f8f9fa; padding: 12px; margin: 8px 0; border-left: 4px solid #007bff; border-radius: 4px; font-family: monospace; font-size: 12px; }
            .controls { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
            .control-group { margin: 10px 0; }
            button { padding: 10px 15px; margin: 5px; cursor: pointer; border: none; border-radius: 4px; background: #007bff; color: white; }
            button:hover { background: #0056b3; }
            button.danger { background: #dc3545; }
            button.danger:hover { background: #c82333; }
            button.success { background: #28a745; }
            button.success:hover { background: #218838; }
            input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
            #messages { height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; background: #fff; border-radius: 4px; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
            .stat-card { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
            .stat-value { font-size: 24px; font-weight: bold; color: #007bff; }
            .stat-label { font-size: 14px; color: #6c757d; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔄 任务状态 WebSocket 测试</h1>
            
            <div id="status" class="status disconnected">未连接</div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" id="messageCount">0</div>
                    <div class="stat-label">消息数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="connectionTime">--</div>
                    <div class="stat-label">连接时长</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="subscribedTasks">0</div>
                    <div class="stat-label">订阅任务</div>
                </div>
            </div>
            
            <div class="controls">
                <h3>连接控制</h3>
                <div class="control-group">
                    <button onclick="connect()" class="success">连接</button>
                    <button onclick="disconnect()" class="danger">断开</button>
                    <button onclick="sendHeartbeat()">心跳</button>
                    <button onclick="getSystemStatus()">系统状态</button>
                    <button onclick="clearMessages()">清空日志</button>
                </div>
            </div>
            
            <div class="controls">
                <h3>任务订阅</h3>
                <div class="control-group">
                    <input type="text" id="taskId" placeholder="任务ID" value="test-task-001">
                    <button onclick="subscribeTask()" class="success">订阅任务</button>
                    <button onclick="unsubscribeTask()" class="danger">取消订阅</button>
                </div>
            </div>
            
            <div class="controls">
                <h3>任务状态模拟</h3>
                <div class="control-group">
                    <select id="taskStatus">
                        <option value="pending">待处理</option>
                        <option value="waiting_dependencies">等待依赖</option>
                        <option value="ready">就绪</option>
                        <option value="running">运行中</option>
                        <option value="success">成功</option>
                        <option value="failed">失败</option>
                        <option value="cancelled">已取消</option>
                        <option value="paused">已暂停</option>
                    </select>
                    <input type="number" id="progress" placeholder="进度 (0-100)" value="50" min="0" max="100">
                    <input type="text" id="statusMessage" placeholder="状态消息" value="任务执行中...">
                    <button onclick="simulateTaskUpdate()" class="success">模拟更新</button>
                </div>
            </div>
            
            <h3>📝 消息日志</h3>
            <div id="messages"></div>
        </div>

        <script>
            let ws = null;
            let connected = false;
            let messageCount = 0;
            let connectionStartTime = null;
            let subscribedTasksSet = new Set();

            function updateStatus(message, isConnected) {
                const statusDiv = document.getElementById('status');
                statusDiv.textContent = message;
                statusDiv.className = 'status ' + (isConnected ? 'connected' : 'disconnected');
                connected = isConnected;
                
                if (isConnected) {
                    connectionStartTime = new Date();
                    updateConnectionTime();
                } else {
                    connectionStartTime = null;
                    document.getElementById('connectionTime').textContent = '--';
                }
            }

            function updateConnectionTime() {
                if (connectionStartTime) {
                    const now = new Date();
                    const diff = Math.floor((now - connectionStartTime) / 1000);
                    const minutes = Math.floor(diff / 60);
                    const seconds = diff % 60;
                    document.getElementById('connectionTime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                }
            }

            function addMessage(message, type = 'info') {
                const messagesDiv = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                
                const timestamp = new Date().toLocaleTimeString();
                const typeLabel = type === 'sent' ? '📤 发送' : type === 'received' ? '📥 接收' : 'ℹ️ 信息';
                
                messageDiv.innerHTML = `<strong>[${timestamp}] ${typeLabel}:</strong><br><pre>${JSON.stringify(message, null, 2)}</pre>`;
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
                
                messageCount++;
                document.getElementById('messageCount').textContent = messageCount;
            }

            function clearMessages() {
                document.getElementById('messages').innerHTML = '';
                messageCount = 0;
                document.getElementById('messageCount').textContent = '0';
            }

            function connect() {
                if (connected) {
                    addMessage('已经连接');
                    return;
                }

                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/api/task/ws/status`;
                
                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    updateStatus('✅ 已连接', true);
                    addMessage('WebSocket连接已建立');
                };

                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    addMessage(message, 'received');
                    
                    // 处理特定消息类型
                    if (message.message_type === 'task_status_update') {
                        updateTaskStatus(message);
                    }
                };

                ws.onclose = function(event) {
                    updateStatus('❌ 连接已关闭', false);
                    addMessage('WebSocket连接已关闭');
                };

                ws.onerror = function(error) {
                    updateStatus('⚠️ 连接错误', false);
                    addMessage('WebSocket错误: ' + error);
                };
            }

            function disconnect() {
                if (ws && connected) {
                    ws.close();
                }
            }

            function sendMessage(message) {
                if (connected && ws) {
                    ws.send(JSON.stringify(message));
                    addMessage(message, 'sent');
                } else {
                    addMessage('未连接，无法发送消息');
                }
            }

            function sendHeartbeat() {
                sendMessage({ type: 'heartbeat' });
            }

            function getSystemStatus() {
                sendMessage({ type: 'get_system_status' });
            }

            function subscribeTask() {
                const taskId = document.getElementById('taskId').value;
                if (taskId) {
                    sendMessage({ type: 'subscribe_task', task_id: taskId });
                    subscribedTasksSet.add(taskId);
                    updateSubscribedTasksCount();
                }
            }

            function unsubscribeTask() {
                const taskId = document.getElementById('taskId').value;
                if (taskId) {
                    sendMessage({ type: 'unsubscribe_task', task_id: taskId });
                    subscribedTasksSet.delete(taskId);
                    updateSubscribedTasksCount();
                }
            }

            function updateSubscribedTasksCount() {
                document.getElementById('subscribedTasks').textContent = subscribedTasksSet.size;
            }

            function updateTaskStatus(message) {
                // 可以在这里添加任务状态的可视化更新
                console.log('任务状态更新:', message);
            }

            async function simulateTaskUpdate() {
                const taskId = document.getElementById('taskId').value;
                const status = document.getElementById('taskStatus').value;
                const progress = parseFloat(document.getElementById('progress').value);
                const message = document.getElementById('statusMessage').value;

                if (!taskId) {
                    addMessage('请输入任务ID');
                    return;
                }

                try {
                    const response = await fetch('/api/task/test/simulate_update', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            task_id: taskId,
                            status: status,
                            progress: progress,
                            message: message
                        })
                    });

                    if (response.ok) {
                        const result = await response.json();
                        addMessage('✅ 任务更新模拟请求已发送: ' + result.message);
                    } else {
                        addMessage('❌ 任务更新模拟请求失败');
                    }
                } catch (error) {
                    addMessage('❌ 请求错误: ' + error);
                }
            }

            // 定时更新连接时长
            setInterval(updateConnectionTime, 1000);

            // 页面加载时自动连接
            window.onload = function() {
                connect();
            };
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.post("/test/simulate_update")
async def simulate_task_update(request: TaskUpdateRequest):
    """
    模拟任务更新（用于测试）
    
    Args:
        request: 任务更新请求
    """
    try:
        # 转换状态
        task_status = TaskStatus(request.status)
        
        # 发送任务状态更新
        await notify_task_status_change(
            task_id=request.task_id,
            status=task_status,
            progress=request.progress,
            message=request.message,
            details={'simulated': True, 'timestamp': str(uuid.uuid4())}
        )
        
        # 如果是运行中状态，也发送进度更新
        if task_status == TaskStatus.RUNNING:
            await notify_task_progress(
                task_id=request.task_id,
                progress=request.progress,
                current_step=f"执行步骤 {int(request.progress/10) + 1}",
                total_steps=10,
                current_step_index=int(request.progress/10)
            )
        
        # 发送日志消息
        await notify_task_log(
            task_id=request.task_id,
            level="INFO",
            message=f"任务状态更新为: {request.status} (进度: {request.progress}%)",
            source="test_simulator"
        )
        
        return {
            "success": True,
            "message": f"任务 {request.task_id} 状态更新模拟完成",
            "task_id": request.task_id,
            "status": request.status,
            "progress": request.progress
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"无效的任务状态: {request.status}")
    except Exception as e:
        logger.error(f"模拟任务更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/ws/statistics")
async def get_websocket_statistics():
    """获取WebSocket统计信息"""
    service = get_websocket_service()
    return service.get_statistics()

@router.post("/ws/broadcast_system_status")
async def broadcast_system_status():
    """广播系统状态（用于测试）"""
    from app.websocket.task_status_service import SystemStatusMessage
    
    service = get_websocket_service()
    
    # 创建模拟系统状态
    system_status = SystemStatusMessage(
        active_tasks=5,
        completed_tasks=23,
        failed_tasks=2,
        system_load=0.65,
        memory_usage=0.78
    )
    
    await service.broadcast_system_status(system_status)
    
    return {
        "success": True,
        "message": "系统状态广播完成",
        "status": system_status.to_dict()
    }
