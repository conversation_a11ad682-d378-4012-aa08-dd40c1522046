"""
MindsDB代理服务
提供透明的MindsDB API代理功能，解决跨域问题并统一API访问
"""

import os
import logging
from typing import Any, Dict
from fastapi import APIRouter, Request, Response, HTTPException
from fastapi.responses import StreamingResponse
import httpx
import asyncio
from urllib.parse import urljoin

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 配置
MINDSDB_URL = os.getenv("MINDSDB_URL", "http://localhost:47334")
PROXY_TIMEOUT = float(os.getenv("PROXY_TIMEOUT", "30.0"))
MAX_RETRIES = int(os.getenv("PROXY_MAX_RETRIES", "3"))

# 需要过滤的请求头
FILTERED_HEADERS = {
    "host", "content-length", "connection", "upgrade", 
    "proxy-connection", "proxy-authorization", "te", "trailers", "transfer-encoding"
}

class MindsDBProxyError(Exception):
    """MindsDB代理错误"""
    pass

async def forward_request(
    method: str,
    target_url: str,
    headers: Dict[str, str],
    query_params: Dict[str, Any],
    body: bytes,
    timeout: float = PROXY_TIMEOUT
) -> httpx.Response:
    """
    转发请求到MindsDB服务
    
    Args:
        method: HTTP方法
        target_url: 目标URL
        headers: 请求头
        query_params: 查询参数
        body: 请求体
        timeout: 超时时间
        
    Returns:
        httpx.Response: MindsDB响应
        
    Raises:
        MindsDBProxyError: 代理错误
    """
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            logger.info(f"代理请求: {method} {target_url}")
            logger.debug(f"请求头: {headers}")
            logger.debug(f"查询参数: {query_params}")
            
            response = await client.request(
                method=method,
                url=target_url,
                headers=headers,
                params=query_params,
                content=body,
                follow_redirects=True
            )
            
            logger.info(f"MindsDB响应: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")
            
            return response
            
        except httpx.TimeoutException as e:
            logger.error(f"请求超时: {e}")
            raise MindsDBProxyError(f"MindsDB服务请求超时: {e}")
        except httpx.ConnectError as e:
            logger.error(f"连接错误: {e}")
            raise MindsDBProxyError(f"无法连接到MindsDB服务: {e}")
        except httpx.RequestError as e:
            logger.error(f"请求错误: {e}")
            raise MindsDBProxyError(f"代理请求失败: {e}")
        except Exception as e:
            logger.error(f"未知错误: {e}")
            raise MindsDBProxyError(f"代理服务发生未知错误: {e}")

def filter_headers(headers: Dict[str, str]) -> Dict[str, str]:
    """
    过滤不需要转发的请求头
    
    Args:
        headers: 原始请求头
        
    Returns:
        Dict[str, str]: 过滤后的请求头
    """
    return {
        key: value for key, value in headers.items() 
        if key.lower() not in FILTERED_HEADERS
    }

def filter_response_headers(headers: Dict[str, str]) -> Dict[str, str]:
    """
    过滤响应头
    
    Args:
        headers: 原始响应头
        
    Returns:
        Dict[str, str]: 过滤后的响应头
    """
    # 过滤可能导致问题的响应头
    filtered = {
        key: value for key, value in headers.items()
        if key.lower() not in {"content-encoding", "transfer-encoding", "connection"}
    }
    
    # 添加CORS头
    filtered.update({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD",
        "Access-Control-Allow-Headers": "*",
        "Access-Control-Expose-Headers": "*"
    })
    
    return filtered

@router.api_route(
    "/api/mindsdb-proxy/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
    summary="MindsDB代理服务",
    description="透明代理MindsDB API请求，解决跨域问题"
)
async def mindsdb_proxy(path: str, request: Request):
    """
    MindsDB代理端点
    
    Args:
        path: API路径
        request: FastAPI请求对象
        
    Returns:
        Response: 代理响应
        
    Raises:
        HTTPException: HTTP错误
    """
    try:
        # 构建目标URL
        target_url = urljoin(MINDSDB_URL.rstrip('/') + '/', path)
        
        # 处理OPTIONS请求（CORS预检）
        if request.method == "OPTIONS":
            return Response(
                status_code=200,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD",
                    "Access-Control-Allow-Headers": "*",
                    "Access-Control-Max-Age": "86400"
                }
            )
        
        # 过滤请求头
        filtered_headers = filter_headers(dict(request.headers))
        
        # 获取请求体
        request_body = await request.body()
        
        # 转发请求
        proxy_response = await forward_request(
            method=request.method,
            target_url=target_url,
            headers=filtered_headers,
            query_params=dict(request.query_params),
            body=request_body,
            timeout=PROXY_TIMEOUT
        )
        
        # 过滤响应头
        response_headers = filter_response_headers(dict(proxy_response.headers))
        
        # 检查是否为流式响应
        content_type = proxy_response.headers.get("content-type", "")
        if "stream" in content_type.lower() or "text/event-stream" in content_type.lower():
            # 处理流式响应
            async def stream_response():
                async for chunk in proxy_response.aiter_bytes():
                    yield chunk
            
            return StreamingResponse(
                stream_response(),
                status_code=proxy_response.status_code,
                headers=response_headers,
                media_type=content_type
            )
        else:
            # 处理普通响应
            return Response(
                content=proxy_response.content,
                status_code=proxy_response.status_code,
                headers=response_headers
            )
            
    except MindsDBProxyError as e:
        logger.error(f"代理错误: {e}")
        raise HTTPException(status_code=502, detail=str(e))
    except Exception as e:
        logger.error(f"代理服务未知错误: {e}")
        raise HTTPException(status_code=500, detail=f"代理服务内部错误: {str(e)}")

@router.get(
    "/api/mindsdb-proxy-status",
    summary="代理服务状态",
    description="检查MindsDB代理服务状态"
)
async def proxy_status():
    """
    检查代理服务状态
    
    Returns:
        Dict: 状态信息
    """
    try:
        # 测试连接到MindsDB
        async with httpx.AsyncClient(timeout=5.0) as client:
            test_url = urljoin(MINDSDB_URL.rstrip('/') + '/', 'api/status')
            response = await client.get(test_url)
            
            return {
                "status": "healthy",
                "mindsdb_url": MINDSDB_URL,
                "mindsdb_status": response.status_code,
                "proxy_timeout": PROXY_TIMEOUT,
                "max_retries": MAX_RETRIES,
                "message": "代理服务运行正常"
            }
    except Exception as e:
        logger.error(f"代理状态检查失败: {e}")
        return {
            "status": "unhealthy",
            "mindsdb_url": MINDSDB_URL,
            "error": str(e),
            "message": "代理服务异常"
        }

# 导出路由器
__all__ = ["router"]
