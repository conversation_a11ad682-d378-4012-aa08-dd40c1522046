"""
Agent测试仪表板API v3.0
提供Agent测试仪表板的核心功能API

功能特性:
- 测试套件管理
- 测试执行和监控
- 实时进度跟踪
- 性能分析和报告
- 测试模板管理
- 批量测试执行
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime, timedelta
import asyncio
import json
import uuid

# 导入核心服务
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from core.agent_test_executor import AgentTestExecutor, TestCase, TestSuite, TestPriority, TestStatus, TestExecution
from core.agent_test_suite_manager import AgentTestSuiteManager
from core.mindsdb_client import mindsdb_client
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v3/agent-testing/dashboard", tags=["Agent测试仪表板v3"])

# ==================== 请求/响应模型 ====================

class CreateTestCaseRequest(BaseModel):
    """创建测试用例请求"""
    name: str = Field(description="测试用例名称")
    description: str = Field(description="测试用例描述")
    query: str = Field(description="测试查询")
    expected_result: Optional[Dict[str, Any]] = Field(default=None, description="期望结果")
    timeout: float = Field(default=30.0, ge=1.0, le=300.0, description="超时时间(秒)")
    priority: str = Field(default="medium", description="优先级: low, medium, high, critical")
    tags: List[str] = Field(default=[], description="标签列表")

class CreateTestSuiteRequest(BaseModel):
    """创建测试套件请求"""
    name: str = Field(description="测试套件名称")
    description: str = Field(description="测试套件描述")
    agent_name: str = Field(description="目标Agent名称")
    test_cases: List[CreateTestCaseRequest] = Field(description="测试用例列表")
    concurrency: int = Field(default=1, ge=1, le=10, description="并发数")
    timeout: float = Field(default=300.0, ge=60.0, le=3600.0, description="总超时时间(秒)")
    tags: List[str] = Field(default=[], description="标签列表")

class ExecuteTestSuiteRequest(BaseModel):
    """执行测试套件请求"""
    suite_id: str = Field(description="测试套件ID")
    concurrency_override: Optional[int] = Field(default=None, ge=1, le=10, description="覆盖并发数")
    timeout_override: Optional[float] = Field(default=None, ge=60.0, le=3600.0, description="覆盖超时时间")
    tags_filter: Optional[List[str]] = Field(default=None, description="标签过滤器")

class CreateFromTemplateRequest(BaseModel):
    """从模板创建请求"""
    template_id: str = Field(description="模板ID")
    name: str = Field(description="测试套件名称")
    agent_name: str = Field(description="目标Agent名称")
    parameters: Dict[str, Any] = Field(default={}, description="模板参数")

class GenerateSmartTestsRequest(BaseModel):
    """智能生成测试请求"""
    agent_name: str = Field(description="Agent名称")
    agent_description: str = Field(description="Agent描述")
    test_count: int = Field(default=10, ge=1, le=50, description="生成测试数量")
    categories: List[str] = Field(default=[], description="测试类别")

# ==================== 核心服务实例 ====================

# 初始化服务
test_executor = AgentTestExecutor(mindsdb_client)
suite_manager = AgentTestSuiteManager()

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()

# ==================== API端点 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "agent-testing-dashboard",
        "version": "3.0.0",
        "timestamp": datetime.now(),
        "components": {
            "test_executor": "ok",
            "suite_manager": "ok",
            "mindsdb_client": "ok" if mindsdb_client else "not_available"
        }
    }

@router.get("/agents")
async def list_available_agents():
    """获取可用的Agent列表"""
    try:
        # 查询MindsDB中的Agent
        agents_query = "SHOW AGENTS"
        result = mindsdb_client.execute_query(agents_query)
        
        agents = []
        for row in result:
            agents.append({
                "name": row.get("name", ""),
                "model": row.get("model", ""),
                "provider": row.get("provider", ""),
                "status": "active",
                "created_at": row.get("created_at", ""),
                "updated_at": row.get("updated_at", "")
            })
        
        return APIResponseBuilder.success(
            data=agents,
            message=f"获取到 {len(agents)} 个可用Agent"
        )
        
    except Exception as e:
        logger.error(f"获取Agent列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/test-suites")
async def create_test_suite(request: CreateTestSuiteRequest):
    """创建测试套件"""
    try:
        # 转换测试用例
        test_cases = []
        for tc_req in request.test_cases:
            test_case = TestCase(
                id=str(uuid.uuid4()),
                name=tc_req.name,
                description=tc_req.description,
                query=tc_req.query,
                expected_result=tc_req.expected_result,
                timeout=tc_req.timeout,
                priority=TestPriority(tc_req.priority),
                tags=tc_req.tags
            )
            test_cases.append(test_case)
        
        # 创建测试套件
        test_suite = suite_manager.create_test_suite(
            name=request.name,
            description=request.description,
            agent_name=request.agent_name,
            test_cases=test_cases,
            concurrency=request.concurrency,
            timeout=request.timeout,
            tags=request.tags
        )
        
        return APIResponseBuilder.success(
            data={
                "suite_id": test_suite.id,
                "name": test_suite.name,
                "test_count": len(test_suite.test_cases),
                "agent_name": test_suite.agent_name
            },
            message="测试套件创建成功"
        )
        
    except Exception as e:
        logger.error(f"创建测试套件失败: {str(e)}")
        return APIResponseBuilder.error(f"创建失败: {str(e)}")

@router.get("/test-suites")
async def list_test_suites(
    agent_name: Optional[str] = None,
    tags: Optional[str] = None,
    limit: int = 50
):
    """获取测试套件列表"""
    try:
        tag_list = tags.split(',') if tags else None
        
        suites = suite_manager.list_test_suites(
            agent_name=agent_name,
            tags=tag_list,
            limit=limit
        )
        
        suite_list = []
        for suite in suites:
            suite_list.append({
                "id": suite.id,
                "name": suite.name,
                "description": suite.description,
                "agent_name": suite.agent_name,
                "test_count": len(suite.test_cases),
                "concurrency": suite.concurrency,
                "timeout": suite.timeout,
                "tags": suite.tags,
                "created_at": suite.metadata.get("created_at"),
                "updated_at": suite.metadata.get("updated_at")
            })
        
        return APIResponseBuilder.success(
            data=suite_list,
            message=f"获取到 {len(suite_list)} 个测试套件"
        )
        
    except Exception as e:
        logger.error(f"获取测试套件列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/test-suites/{suite_id}")
async def get_test_suite(suite_id: str):
    """获取测试套件详情"""
    try:
        test_suite = suite_manager.get_test_suite(suite_id)
        
        if not test_suite:
            raise HTTPException(status_code=404, detail="测试套件不存在")
        
        # 转换测试用例
        test_cases = []
        for tc in test_suite.test_cases:
            test_cases.append({
                "id": tc.id,
                "name": tc.name,
                "description": tc.description,
                "query": tc.query,
                "expected_result": tc.expected_result,
                "timeout": tc.timeout,
                "priority": tc.priority.value,
                "tags": tc.tags,
                "metadata": tc.metadata
            })
        
        return APIResponseBuilder.success(
            data={
                "id": test_suite.id,
                "name": test_suite.name,
                "description": test_suite.description,
                "agent_name": test_suite.agent_name,
                "concurrency": test_suite.concurrency,
                "timeout": test_suite.timeout,
                "retry_count": test_suite.retry_count,
                "tags": test_suite.tags,
                "test_cases": test_cases,
                "metadata": test_suite.metadata
            },
            message="获取测试套件详情成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取测试套件详情失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/test-suites/{suite_id}/execute")
async def execute_test_suite(
    suite_id: str,
    request: ExecuteTestSuiteRequest,
    background_tasks: BackgroundTasks
):
    """执行测试套件"""
    try:
        test_suite = suite_manager.get_test_suite(suite_id)
        
        if not test_suite:
            raise HTTPException(status_code=404, detail="测试套件不存在")
        
        # 应用覆盖参数
        if request.concurrency_override:
            test_suite.concurrency = request.concurrency_override
        if request.timeout_override:
            test_suite.timeout = request.timeout_override
        
        # 过滤测试用例
        if request.tags_filter:
            filtered_cases = []
            for tc in test_suite.test_cases:
                if any(tag in tc.tags for tag in request.tags_filter):
                    filtered_cases.append(tc)
            test_suite.test_cases = filtered_cases
        
        # 创建进度回调
        async def progress_callback(execution: TestExecution):
            progress_data = {
                "execution_id": execution.id,
                "status": execution.status.value,
                "progress": execution.progress,
                "completed_tests": execution.completed_tests,
                "total_tests": execution.total_tests,
                "failed_tests": execution.failed_tests
            }
            await manager.broadcast(json.dumps({
                "type": "progress_update",
                "data": progress_data
            }))
        
        # 后台执行测试
        background_tasks.add_task(
            test_executor.execute_test_suite,
            test_suite,
            progress_callback
        )
        
        return APIResponseBuilder.success(
            data={
                "message": "测试套件开始执行",
                "suite_id": suite_id,
                "test_count": len(test_suite.test_cases),
                "concurrency": test_suite.concurrency
            },
            message="测试执行已启动"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行测试套件失败: {str(e)}")
        return APIResponseBuilder.error(f"执行失败: {str(e)}")

@router.get("/executions")
async def list_test_executions():
    """获取测试执行列表"""
    try:
        active_executions = test_executor.get_active_executions()
        execution_history = test_executor.get_execution_history(limit=20)
        
        def format_execution(execution: TestExecution):
            return {
                "id": execution.id,
                "suite_name": execution.test_suite.name,
                "agent_name": execution.test_suite.agent_name,
                "status": execution.status.value,
                "progress": execution.progress,
                "start_time": execution.start_time.isoformat(),
                "end_time": execution.end_time.isoformat() if execution.end_time else None,
                "total_tests": execution.total_tests,
                "completed_tests": execution.completed_tests,
                "failed_tests": execution.failed_tests,
                "performance_summary": execution.performance_summary
            }
        
        return APIResponseBuilder.success(
            data={
                "active_executions": [format_execution(ex) for ex in active_executions],
                "execution_history": [format_execution(ex) for ex in execution_history]
            },
            message="获取测试执行列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取测试执行列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/executions/{execution_id}")
async def get_test_execution(execution_id: str):
    """获取测试执行详情"""
    try:
        execution = test_executor.get_execution_status(execution_id)
        
        if not execution:
            raise HTTPException(status_code=404, detail="测试执行不存在")
        
        # 格式化测试结果
        results = []
        for result in execution.results:
            results.append({
                "test_case_id": result.test_case_id,
                "agent_name": result.agent_name,
                "status": result.status.value,
                "start_time": result.start_time.isoformat(),
                "end_time": result.end_time.isoformat() if result.end_time else None,
                "response": result.response,
                "response_time": result.response_time,
                "error_message": result.error_message,
                "performance_metrics": result.performance_metrics,
                "metadata": result.metadata
            })
        
        return APIResponseBuilder.success(
            data={
                "id": execution.id,
                "status": execution.status.value,
                "progress": execution.progress,
                "start_time": execution.start_time.isoformat(),
                "end_time": execution.end_time.isoformat() if execution.end_time else None,
                "total_tests": execution.total_tests,
                "completed_tests": execution.completed_tests,
                "failed_tests": execution.failed_tests,
                "performance_summary": execution.performance_summary,
                "results": results,
                "test_suite": {
                    "id": execution.test_suite.id,
                    "name": execution.test_suite.name,
                    "agent_name": execution.test_suite.agent_name
                }
            },
            message="获取测试执行详情成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取测试执行详情失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/executions/{execution_id}/cancel")
async def cancel_test_execution(execution_id: str):
    """取消测试执行"""
    try:
        success = test_executor.cancel_execution(execution_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="测试执行不存在或已完成")
        
        return APIResponseBuilder.success(
            data={"execution_id": execution_id},
            message="测试执行已取消"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消测试执行失败: {str(e)}")
        return APIResponseBuilder.error(f"取消失败: {str(e)}")

@router.get("/templates")
async def list_test_templates(category: Optional[str] = None):
    """获取测试模板列表"""
    try:
        templates = suite_manager.get_available_templates(category=category)
        
        template_list = []
        for template in templates:
            template_list.append({
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "category": template.category,
                "test_case_count": len(template.test_cases),
                "tags": template.tags,
                "metadata": template.metadata
            })
        
        return APIResponseBuilder.success(
            data=template_list,
            message=f"获取到 {len(template_list)} 个测试模板"
        )
        
    except Exception as e:
        logger.error(f"获取测试模板列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/templates/{template_id}/create-suite")
async def create_suite_from_template(
    template_id: str,
    request: CreateFromTemplateRequest
):
    """从模板创建测试套件"""
    try:
        test_suite = suite_manager.create_from_template(
            template_id=template_id,
            name=request.name,
            agent_name=request.agent_name,
            parameters=request.parameters
        )
        
        if not test_suite:
            raise HTTPException(status_code=404, detail="模板不存在")
        
        return APIResponseBuilder.success(
            data={
                "suite_id": test_suite.id,
                "name": test_suite.name,
                "test_count": len(test_suite.test_cases),
                "template_id": template_id
            },
            message="从模板创建测试套件成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"从模板创建测试套件失败: {str(e)}")
        return APIResponseBuilder.error(f"创建失败: {str(e)}")

@router.post("/generate-smart-tests")
async def generate_smart_tests(request: GenerateSmartTestsRequest):
    """智能生成测试用例"""
    try:
        test_cases = suite_manager.generate_smart_test_cases(
            agent_name=request.agent_name,
            agent_description=request.agent_description,
            count=request.test_count
        )
        
        # 转换为响应格式
        test_case_list = []
        for tc in test_cases:
            test_case_list.append({
                "id": tc.id,
                "name": tc.name,
                "description": tc.description,
                "query": tc.query,
                "priority": tc.priority.value,
                "tags": tc.tags,
                "timeout": tc.timeout
            })
        
        return APIResponseBuilder.success(
            data=test_case_list,
            message=f"智能生成了 {len(test_case_list)} 个测试用例"
        )
        
    except Exception as e:
        logger.error(f"智能生成测试用例失败: {str(e)}")
        return APIResponseBuilder.error(f"生成失败: {str(e)}")

@router.websocket("/ws/progress")
async def websocket_progress_endpoint(websocket: WebSocket):
    """WebSocket进度监控端点"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # 处理客户端消息（如果需要）
            pass
    except WebSocketDisconnect:
        manager.disconnect(websocket)
