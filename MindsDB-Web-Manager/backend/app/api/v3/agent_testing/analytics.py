"""
Agent测试高级分析API v3.0
提供Agent测试数据的高级分析、可视化和报告生成功能

功能特性:
- 性能趋势分析
- 对比分析
- 异常检测
- 预测分析
- 可视化图表生成
- 报告生成和导出
"""

from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime, timedelta
import asyncio
import json
import uuid
import os

# 导入核心服务
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from core.agent_analytics_engine import AgentAnalyticsEngine, AnalysisType, MetricType
from core.agent_chart_generator import AgentChartGenerator, ChartType, ColorScheme
from core.agent_report_generator import AgentReportGenerator, ReportType, ReportFormat
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v3/agent-testing/analytics", tags=["Agent高级分析v3"])

# ==================== 请求/响应模型 ====================

class TrendAnalysisRequest(BaseModel):
    """趋势分析请求"""
    agent_name: str = Field(description="Agent名称")
    metric_type: str = Field(description="指标类型: response_time, success_rate, throughput, error_rate")
    start_date: datetime = Field(description="开始时间")
    end_date: datetime = Field(description="结束时间")
    include_forecast: bool = Field(default=True, description="是否包含预测")
    forecast_periods: int = Field(default=24, ge=1, le=168, description="预测周期数(小时)")

class ComparisonAnalysisRequest(BaseModel):
    """对比分析请求"""
    baseline_agent: str = Field(description="基准Agent")
    comparison_agents: List[str] = Field(description="对比Agent列表")
    metric_type: str = Field(description="指标类型")
    start_date: datetime = Field(description="开始时间")
    end_date: datetime = Field(description="结束时间")
    statistical_tests: bool = Field(default=True, description="是否进行统计检验")

class AnomalyDetectionRequest(BaseModel):
    """异常检测请求"""
    agent_name: str = Field(description="Agent名称")
    metric_type: str = Field(description="指标类型")
    start_date: datetime = Field(description="开始时间")
    end_date: datetime = Field(description="结束时间")
    sensitivity: float = Field(default=0.1, ge=0.01, le=0.5, description="检测敏感度")
    methods: List[str] = Field(default=["z_score", "iqr", "isolation_forest"], description="检测方法")

class ChartGenerationRequest(BaseModel):
    """图表生成请求"""
    chart_type: str = Field(description="图表类型: line, bar, scatter, radar, histogram")
    data_source: str = Field(description="数据源: trend, comparison, anomaly, prediction")
    agent_names: List[str] = Field(description="Agent名称列表")
    metric_type: str = Field(description="指标类型")
    time_range: Dict[str, datetime] = Field(description="时间范围")
    chart_config: Dict[str, Any] = Field(default={}, description="图表配置")

class ReportGenerationRequest(BaseModel):
    """报告生成请求"""
    report_type: str = Field(description="报告类型: performance, comparison, anomaly, prediction, comprehensive")
    format: str = Field(default="html", description="报告格式: html, pdf, json, excel")
    agent_names: List[str] = Field(description="Agent名称列表")
    time_range: Dict[str, datetime] = Field(description="时间范围")
    include_charts: bool = Field(default=True, description="是否包含图表")
    include_raw_data: bool = Field(default=False, description="是否包含原始数据")
    custom_title: Optional[str] = Field(default=None, description="自定义标题")

# ==================== 核心服务实例 ====================

analytics_engine = AgentAnalyticsEngine()
chart_generator = AgentChartGenerator()
report_generator = AgentReportGenerator()

# ==================== API端点 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "agent-analytics",
        "version": "3.0.0",
        "timestamp": datetime.now(),
        "components": {
            "analytics_engine": "ok",
            "chart_generator": "ok",
            "report_generator": "ok"
        }
    }

@router.post("/trend-analysis")
async def analyze_trend(request: TrendAnalysisRequest):
    """
    执行趋势分析
    
    分析Agent在指定时间范围内的性能趋势，包括：
    - 趋势方向和强度
    - 季节性模式检测
    - 变化点识别
    - 未来预测（可选）
    """
    try:
        # 验证指标类型
        try:
            metric_type = MetricType(request.metric_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的指标类型: {request.metric_type}")
        
        # 模拟获取数据（实际应从数据库获取）
        sample_data = _generate_sample_data(
            request.agent_name, 
            request.start_date, 
            request.end_date,
            metric_type
        )
        
        if len(sample_data) < 10:
            raise HTTPException(status_code=400, detail="数据点不足，无法进行趋势分析")
        
        # 执行趋势分析
        analysis_result = analytics_engine.analyze_performance_trends(
            agent_name=request.agent_name,
            metric_type=metric_type,
            data=sample_data,
            time_range=(request.start_date, request.end_date)
        )
        
        # 生成预测（如果需要）
        forecast_data = None
        if request.include_forecast:
            prediction_result = analytics_engine.predict_performance(
                agent_name=request.agent_name,
                metric_type=metric_type,
                data=sample_data,
                forecast_periods=request.forecast_periods
            )
            forecast_data = prediction_result.results['forecast']
        
        return APIResponseBuilder.success(
            data={
                "analysis_result": {
                    "analysis_type": analysis_result.analysis_type.value,
                    "metric_type": analysis_result.metric_type.value,
                    "agent_name": analysis_result.agent_name,
                    "time_range": {
                        "start": analysis_result.time_range[0].isoformat(),
                        "end": analysis_result.time_range[1].isoformat()
                    },
                    "data_points": analysis_result.data_points,
                    "confidence_score": analysis_result.confidence_score,
                    "insights": analysis_result.insights,
                    "recommendations": analysis_result.recommendations,
                    "results": analysis_result.results
                },
                "forecast_data": forecast_data
            },
            message="趋势分析完成"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"趋势分析失败: {str(e)}")
        return APIResponseBuilder.error(f"分析失败: {str(e)}")

@router.post("/comparison-analysis")
async def analyze_comparison(request: ComparisonAnalysisRequest):
    """
    执行对比分析
    
    对比多个Agent的性能指标，包括：
    - 统计指标对比
    - 统计显著性检验
    - 效应量计算
    - 性能排名
    """
    try:
        # 验证指标类型
        try:
            metric_type = MetricType(request.metric_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的指标类型: {request.metric_type}")
        
        # 验证Agent列表
        all_agents = [request.baseline_agent] + request.comparison_agents
        if len(set(all_agents)) != len(all_agents):
            raise HTTPException(status_code=400, detail="Agent列表中存在重复")
        
        # 模拟获取各Agent数据
        agent_data = {}
        for agent_name in all_agents:
            agent_data[agent_name] = _generate_sample_data(
                agent_name, 
                request.start_date, 
                request.end_date,
                metric_type
            )
        
        # 验证数据充足性
        for agent_name, data in agent_data.items():
            if len(data) < 5:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Agent {agent_name} 的数据点不足，无法进行对比分析"
                )
        
        # 执行对比分析
        analysis_result = analytics_engine.compare_agents(
            baseline_agent=request.baseline_agent,
            comparison_agents=request.comparison_agents,
            metric_type=metric_type,
            data=agent_data,
            time_range=(request.start_date, request.end_date)
        )
        
        return APIResponseBuilder.success(
            data={
                "analysis_result": {
                    "analysis_type": analysis_result.analysis_type.value,
                    "metric_type": analysis_result.metric_type.value,
                    "baseline_agent": request.baseline_agent,
                    "comparison_agents": request.comparison_agents,
                    "time_range": {
                        "start": analysis_result.time_range[0].isoformat(),
                        "end": analysis_result.time_range[1].isoformat()
                    },
                    "data_points": analysis_result.data_points,
                    "confidence_score": analysis_result.confidence_score,
                    "insights": analysis_result.insights,
                    "recommendations": analysis_result.recommendations,
                    "results": analysis_result.results
                }
            },
            message="对比分析完成"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"对比分析失败: {str(e)}")
        return APIResponseBuilder.error(f"分析失败: {str(e)}")

@router.post("/anomaly-detection")
async def detect_anomalies(request: AnomalyDetectionRequest):
    """
    执行异常检测
    
    检测Agent性能数据中的异常点，包括：
    - 多种检测方法
    - 异常严重程度分级
    - 异常模式分析
    - 异常原因推断
    """
    try:
        # 验证指标类型
        try:
            metric_type = MetricType(request.metric_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的指标类型: {request.metric_type}")
        
        # 模拟获取数据
        sample_data = _generate_sample_data(
            request.agent_name, 
            request.start_date, 
            request.end_date,
            metric_type,
            include_anomalies=True  # 包含一些异常点用于演示
        )
        
        if len(sample_data) < 10:
            raise HTTPException(status_code=400, detail="数据点不足，无法进行异常检测")
        
        # 执行异常检测
        analysis_result = analytics_engine.detect_anomalies(
            agent_name=request.agent_name,
            metric_type=metric_type,
            data=sample_data,
            sensitivity=request.sensitivity
        )
        
        return APIResponseBuilder.success(
            data={
                "analysis_result": {
                    "analysis_type": analysis_result.analysis_type.value,
                    "metric_type": analysis_result.metric_type.value,
                    "agent_name": analysis_result.agent_name,
                    "time_range": {
                        "start": analysis_result.time_range[0].isoformat(),
                        "end": analysis_result.time_range[1].isoformat()
                    },
                    "data_points": analysis_result.data_points,
                    "confidence_score": analysis_result.confidence_score,
                    "insights": analysis_result.insights,
                    "recommendations": analysis_result.recommendations,
                    "results": analysis_result.results
                }
            },
            message="异常检测完成"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"异常检测失败: {str(e)}")
        return APIResponseBuilder.error(f"检测失败: {str(e)}")

@router.post("/generate-chart")
async def generate_chart(request: ChartGenerationRequest):
    """
    生成可视化图表
    
    根据分析数据生成各种类型的图表，包括：
    - 趋势图
    - 对比图
    - 异常检测图
    - 分布图
    - 雷达图
    """
    try:
        # 验证图表类型
        try:
            chart_type = ChartType(request.chart_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的图表类型: {request.chart_type}")
        
        # 验证指标类型
        try:
            metric_type = MetricType(request.metric_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的指标类型: {request.metric_type}")
        
        # 根据数据源类型生成相应图表
        if request.data_source == "trend":
            if len(request.agent_names) != 1:
                raise HTTPException(status_code=400, detail="趋势图只支持单个Agent")
            
            agent_name = request.agent_names[0]
            sample_data = _generate_sample_data(
                agent_name,
                request.time_range["start"],
                request.time_range["end"],
                metric_type
            )
            
            chart_result = chart_generator.generate_trend_chart(
                data=[{"timestamp": item["timestamp"], "value": item["value"]} for item in sample_data],
                metric_name=metric_type.value,
                agent_name=agent_name
            )
            
        elif request.data_source == "comparison":
            if len(request.agent_names) < 2:
                raise HTTPException(status_code=400, detail="对比图至少需要2个Agent")
            
            agent_data = {}
            for agent_name in request.agent_names:
                sample_data = _generate_sample_data(
                    agent_name,
                    request.time_range["start"],
                    request.time_range["end"],
                    metric_type
                )
                agent_data[agent_name] = [{"timestamp": item["timestamp"], "value": item["value"]} for item in sample_data]
            
            chart_result = chart_generator.generate_comparison_chart(
                data=agent_data,
                metric_name=metric_type.value,
                chart_type=chart_type
            )
            
        elif request.data_source == "anomaly":
            if len(request.agent_names) != 1:
                raise HTTPException(status_code=400, detail="异常检测图只支持单个Agent")
            
            agent_name = request.agent_names[0]
            sample_data = _generate_sample_data(
                agent_name,
                request.time_range["start"],
                request.time_range["end"],
                metric_type,
                include_anomalies=True
            )
            
            # 模拟异常点
            anomalies = [
                {"timestamp": item["timestamp"], "value": item["value"]}
                for item in sample_data[::10]  # 每10个点取一个作为异常点
            ]
            
            chart_result = chart_generator.generate_anomaly_chart(
                data=[{"timestamp": item["timestamp"], "value": item["value"]} for item in sample_data],
                anomalies=anomalies,
                metric_name=metric_type.value,
                agent_name=agent_name
            )
            
        else:
            raise HTTPException(status_code=400, detail=f"不支持的数据源类型: {request.data_source}")
        
        return APIResponseBuilder.success(
            data={
                "chart_id": chart_result.chart_id,
                "chart_type": chart_result.config.chart_type.value,
                "title": chart_result.config.title,
                "chartjs_config": chart_result.chart_js_config,
                "echarts_config": chart_result.echarts_config,
                "plotly_config": chart_result.plotly_config,
                "metadata": chart_result.data.metadata
            },
            message="图表生成成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图表生成失败: {str(e)}")
        return APIResponseBuilder.error(f"生成失败: {str(e)}")

@router.get("/metrics/summary")
async def get_metrics_summary(
    agent_name: str = Query(description="Agent名称"),
    start_date: datetime = Query(description="开始时间"),
    end_date: datetime = Query(description="结束时间")
):
    """
    获取指标摘要
    
    返回Agent在指定时间范围内的关键性能指标摘要
    """
    try:
        # 模拟获取各种指标数据
        metrics_summary = {}
        
        for metric_type in MetricType:
            sample_data = _generate_sample_data(agent_name, start_date, end_date, metric_type)
            
            if sample_data:
                values = [item["value"] for item in sample_data]
                metrics_summary[metric_type.value] = {
                    "mean": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                    "std": (sum((x - sum(values)/len(values))**2 for x in values) / len(values))**0.5,
                    "count": len(values),
                    "latest": values[-1] if values else None
                }
        
        return APIResponseBuilder.success(
            data={
                "agent_name": agent_name,
                "time_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "metrics": metrics_summary,
                "generated_at": datetime.now().isoformat()
            },
            message="指标摘要获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取指标摘要失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

# ==================== 辅助函数 ====================

def _generate_sample_data(
    agent_name: str, 
    start_date: datetime, 
    end_date: datetime, 
    metric_type: MetricType,
    include_anomalies: bool = False
) -> List[Dict[str, Any]]:
    """生成示例数据"""
    import random
    import numpy as np
    
    # 计算时间点
    current_time = start_date
    data_points = []
    
    # 基础值根据指标类型设定
    base_values = {
        MetricType.RESPONSE_TIME: 0.5,  # 秒
        MetricType.SUCCESS_RATE: 0.95,  # 95%
        MetricType.THROUGHPUT: 100.0,   # 请求/秒
        MetricType.ERROR_RATE: 0.05,    # 5%
        MetricType.ACCURACY: 0.92,      # 92%
        MetricType.LATENCY: 0.3         # 秒
    }
    
    base_value = base_values.get(metric_type, 1.0)
    
    while current_time <= end_date:
        # 添加趋势和噪声
        hours_from_start = (current_time - start_date).total_seconds() / 3600
        trend = 0.001 * hours_from_start  # 轻微上升趋势
        noise = random.gauss(0, base_value * 0.1)  # 10%的噪声
        
        # 添加周期性变化（24小时周期）
        seasonal = 0.1 * base_value * np.sin(2 * np.pi * hours_from_start / 24)
        
        value = base_value + trend + noise + seasonal
        
        # 确保值在合理范围内
        if metric_type in [MetricType.SUCCESS_RATE, MetricType.ACCURACY]:
            value = max(0.0, min(1.0, value))
        elif metric_type == MetricType.ERROR_RATE:
            value = max(0.0, min(0.5, value))
        else:
            value = max(0.0, value)
        
        # 添加异常点
        if include_anomalies and random.random() < 0.05:  # 5%概率
            if metric_type in [MetricType.RESPONSE_TIME, MetricType.LATENCY]:
                value *= random.uniform(2.0, 5.0)  # 响应时间异常增加
            elif metric_type == MetricType.SUCCESS_RATE:
                value *= random.uniform(0.5, 0.8)  # 成功率异常下降
            elif metric_type == MetricType.ERROR_RATE:
                value *= random.uniform(2.0, 10.0)  # 错误率异常增加
        
        data_points.append({
            "timestamp": current_time.isoformat(),
            "value": value,
            "agent_name": agent_name,
            "metric_type": metric_type.value
        })
        
        current_time += timedelta(hours=1)  # 每小时一个数据点
    
    return data_points
