"""
Text2SQL权限管理API v3.0
提供用户权限管理和访问控制功能

功能特性:
- 用户认证和授权
- 角色权限管理
- 权限检查和控制
- 审计日志管理
- 访问令牌管理
- 安全策略配置
"""

from fastapi import APIRouter, HTTPException, Depends, Header, Request
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, EmailStr
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime, timedelta
import json

# 导入核心服务
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from core.text2sql_permission_manager import Text2SQLPermissionManager, UserRole
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v3/text2sql/permissions", tags=["Text2SQL权限管理v3"])

# 安全认证
security = HTTPBearer()

# ==================== 请求/响应模型 ====================

class CreateUserRequest(BaseModel):
    """创建用户请求"""
    username: str = Field(min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(description="邮箱地址")
    password: str = Field(min_length=6, description="密码")
    role: str = Field(default="viewer", description="用户角色")
    permissions: List[str] = Field(default=[], description="额外权限")

class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(description="用户名")
    password: str = Field(description="密码")
    remember_me: bool = Field(default=False, description="记住我")

class UpdateUserRoleRequest(BaseModel):
    """更新用户角色请求"""
    user_id: str = Field(description="用户ID")
    role: str = Field(description="新角色")

class GrantPermissionRequest(BaseModel):
    """授予权限请求"""
    user_id: str = Field(description="用户ID")
    permission: str = Field(description="权限名称")

class RevokePermissionRequest(BaseModel):
    """撤销权限请求"""
    user_id: str = Field(description="用户ID")
    permission: str = Field(description="权限名称")

class CheckPermissionRequest(BaseModel):
    """检查权限请求"""
    permission: str = Field(description="权限名称")
    resource_type: Optional[str] = Field(default=None, description="资源类型")
    resource_name: Optional[str] = Field(default=None, description="资源名称")

# ==================== 核心服务实例 ====================

permission_manager = Text2SQLPermissionManager()

# 创建默认管理员用户
try:
    admin_user_id = permission_manager.create_user(
        username="admin",
        email="<EMAIL>",
        password="admin123",
        role=UserRole.SUPER_ADMIN
    )
    logger.info(f"创建默认管理员用户: {admin_user_id}")
except Exception as e:
    logger.info("默认管理员用户已存在或创建失败")

# ==================== 依赖函数 ====================

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        user_info = permission_manager.verify_token(token)
        
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的访问令牌")
        
        return user_info
    except Exception as e:
        raise HTTPException(status_code=401, detail="认证失败")

async def require_permission(permission: str):
    """权限检查装饰器"""
    def permission_checker(current_user: dict = Depends(get_current_user)):
        user_id = current_user.get("user_id")
        if not permission_manager.check_permission(user_id, permission):
            raise HTTPException(status_code=403, detail=f"缺少权限: {permission}")
        return current_user
    return permission_checker

# ==================== API端点 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "text2sql-permissions",
        "version": "3.0.0",
        "timestamp": datetime.now(),
        "components": {
            "permission_manager": "ok",
            "authentication": "ok",
            "authorization": "ok"
        }
    }

@router.post("/auth/login")
async def login(request: LoginRequest):
    """
    用户登录
    
    验证用户凭据并返回访问令牌
    """
    try:
        # 用户认证
        user_id = permission_manager.authenticate_user(
            username=request.username,
            password=request.password
        )
        
        if not user_id:
            return APIResponseBuilder.error("用户名或密码错误", status_code=401)
        
        # 创建访问令牌
        expires_hours = 168 if request.remember_me else 24  # 7天或1天
        token = permission_manager.create_access_token(user_id, expires_hours)
        
        # 获取用户信息
        user_info = permission_manager.get_user_info(user_id)
        
        return APIResponseBuilder.success(
            data={
                "access_token": token,
                "token_type": "bearer",
                "expires_in": expires_hours * 3600,
                "user": user_info
            },
            message="登录成功"
        )
        
    except Exception as e:
        logger.error(f"用户登录失败: {str(e)}")
        return APIResponseBuilder.error(f"登录失败: {str(e)}")

@router.post("/auth/logout")
async def logout(current_user: dict = Depends(get_current_user)):
    """
    用户登出
    
    撤销当前访问令牌
    """
    try:
        token_id = current_user.get("token_id")
        if token_id and token_id in permission_manager.access_tokens:
            permission_manager.access_tokens[token_id].is_active = False
        
        return APIResponseBuilder.success(
            data={"logged_out": True},
            message="登出成功"
        )
        
    except Exception as e:
        logger.error(f"用户登出失败: {str(e)}")
        return APIResponseBuilder.error(f"登出失败: {str(e)}")

@router.get("/auth/me")
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """
    获取当前用户信息
    
    返回当前登录用户的详细信息
    """
    try:
        user_id = current_user.get("user_id")
        user_info = permission_manager.get_user_info(user_id)
        
        if not user_info:
            return APIResponseBuilder.error("用户信息不存在", status_code=404)
        
        return APIResponseBuilder.success(
            data=user_info,
            message="用户信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/users/create")
async def create_user(
    request: CreateUserRequest,
    current_user: dict = Depends(require_permission("user_management"))
):
    """
    创建用户
    
    创建新的系统用户，需要用户管理权限
    """
    try:
        user_id = permission_manager.create_user(
            username=request.username,
            email=request.email,
            password=request.password,
            role=request.role,
            permissions=set(request.permissions)
        )
        
        return APIResponseBuilder.success(
            data={
                "user_id": user_id,
                "username": request.username,
                "role": request.role,
                "created": True
            },
            message="用户创建成功"
        )
        
    except Exception as e:
        logger.error(f"创建用户失败: {str(e)}")
        return APIResponseBuilder.error(f"创建失败: {str(e)}")

@router.get("/users/list")
async def get_users(
    include_inactive: bool = False,
    current_user: dict = Depends(require_permission("user_management"))
):
    """
    获取用户列表
    
    返回所有用户的列表，需要用户管理权限
    """
    try:
        users = permission_manager.get_all_users(include_inactive=include_inactive)
        
        return APIResponseBuilder.success(
            data={
                "users": users,
                "total": len(users),
                "include_inactive": include_inactive
            },
            message="用户列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/users/{user_id}")
async def get_user_details(
    user_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    获取用户详情
    
    返回指定用户的详细信息
    """
    try:
        # 检查权限：只能查看自己的信息或需要用户管理权限
        current_user_id = current_user.get("user_id")
        if user_id != current_user_id:
            if not permission_manager.check_permission(current_user_id, "user_management"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        user_info = permission_manager.get_user_info(user_id)
        
        if not user_info:
            return APIResponseBuilder.error("用户不存在", status_code=404)
        
        return APIResponseBuilder.success(
            data=user_info,
            message="用户详情获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取用户详情失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.put("/users/role")
async def update_user_role(
    request: UpdateUserRoleRequest,
    current_user: dict = Depends(require_permission("user_management"))
):
    """
    更新用户角色
    
    修改指定用户的角色，需要用户管理权限
    """
    try:
        success = permission_manager.update_user_role(
            user_id=request.user_id,
            new_role=request.role
        )
        
        if success:
            return APIResponseBuilder.success(
                data={
                    "user_id": request.user_id,
                    "role": request.role,
                    "updated": True
                },
                message="用户角色更新成功"
            )
        else:
            return APIResponseBuilder.error("用户角色更新失败")
        
    except Exception as e:
        logger.error(f"更新用户角色失败: {str(e)}")
        return APIResponseBuilder.error(f"更新失败: {str(e)}")

@router.post("/users/deactivate/{user_id}")
async def deactivate_user(
    user_id: str,
    current_user: dict = Depends(require_permission("user_management"))
):
    """
    停用用户
    
    停用指定用户，需要用户管理权限
    """
    try:
        success = permission_manager.deactivate_user(user_id)
        
        if success:
            return APIResponseBuilder.success(
                data={
                    "user_id": user_id,
                    "deactivated": True
                },
                message="用户停用成功"
            )
        else:
            return APIResponseBuilder.error("用户停用失败")
        
    except Exception as e:
        logger.error(f"停用用户失败: {str(e)}")
        return APIResponseBuilder.error(f"停用失败: {str(e)}")

@router.post("/permissions/grant")
async def grant_permission(
    request: GrantPermissionRequest,
    current_user: dict = Depends(require_permission("permission_management"))
):
    """
    授予权限
    
    为指定用户授予权限，需要权限管理权限
    """
    try:
        success = permission_manager.grant_permission(
            user_id=request.user_id,
            permission=request.permission
        )
        
        if success:
            return APIResponseBuilder.success(
                data={
                    "user_id": request.user_id,
                    "permission": request.permission,
                    "granted": True
                },
                message="权限授予成功"
            )
        else:
            return APIResponseBuilder.error("权限授予失败")
        
    except Exception as e:
        logger.error(f"授予权限失败: {str(e)}")
        return APIResponseBuilder.error(f"授予失败: {str(e)}")

@router.post("/permissions/revoke")
async def revoke_permission(
    request: RevokePermissionRequest,
    current_user: dict = Depends(require_permission("permission_management"))
):
    """
    撤销权限
    
    撤销指定用户的权限，需要权限管理权限
    """
    try:
        success = permission_manager.revoke_permission(
            user_id=request.user_id,
            permission=request.permission
        )
        
        if success:
            return APIResponseBuilder.success(
                data={
                    "user_id": request.user_id,
                    "permission": request.permission,
                    "revoked": True
                },
                message="权限撤销成功"
            )
        else:
            return APIResponseBuilder.error("权限撤销失败")
        
    except Exception as e:
        logger.error(f"撤销权限失败: {str(e)}")
        return APIResponseBuilder.error(f"撤销失败: {str(e)}")

@router.post("/permissions/check")
async def check_permission(
    request: CheckPermissionRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    检查权限
    
    检查当前用户是否拥有指定权限
    """
    try:
        user_id = current_user.get("user_id")
        has_permission = permission_manager.check_permission(
            user_id=user_id,
            permission=request.permission,
            resource_type=request.resource_type,
            resource_name=request.resource_name
        )
        
        return APIResponseBuilder.success(
            data={
                "user_id": user_id,
                "permission": request.permission,
                "resource_type": request.resource_type,
                "resource_name": request.resource_name,
                "has_permission": has_permission
            },
            message="权限检查完成"
        )
        
    except Exception as e:
        logger.error(f"检查权限失败: {str(e)}")
        return APIResponseBuilder.error(f"检查失败: {str(e)}")

@router.get("/audit/logs")
async def get_audit_logs(
    user_id: Optional[str] = None,
    action: Optional[str] = None,
    limit: int = 100,
    current_user: dict = Depends(require_permission("audit_view"))
):
    """
    获取审计日志
    
    返回系统审计日志，需要审计查看权限
    """
    try:
        logs = permission_manager.get_audit_logs(
            user_id=user_id,
            action=action,
            limit=limit
        )
        
        return APIResponseBuilder.success(
            data={
                "logs": logs,
                "total": len(logs),
                "filters": {
                    "user_id": user_id,
                    "action": action,
                    "limit": limit
                }
            },
            message="审计日志获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取审计日志失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/statistics")
async def get_permission_statistics(
    current_user: dict = Depends(require_permission("user_management"))
):
    """
    获取权限统计信息
    
    返回用户和权限的统计数据，需要用户管理权限
    """
    try:
        statistics = permission_manager.get_permission_statistics()
        
        return APIResponseBuilder.success(
            data=statistics,
            message="权限统计信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取权限统计信息失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/roles")
async def get_available_roles():
    """
    获取可用角色列表
    
    返回所有可用的用户角色
    """
    try:
        roles = [
            {
                "value": role.value,
                "label": {
                    "super_admin": "超级管理员",
                    "admin": "管理员",
                    "analyst": "数据分析师",
                    "viewer": "只读用户",
                    "guest": "访客"
                }.get(role.value, role.value),
                "description": {
                    "super_admin": "拥有所有权限的超级管理员",
                    "admin": "系统管理员，可以管理用户和权限",
                    "analyst": "可以执行查询和分析数据",
                    "viewer": "只能查看数据，不能执行查询",
                    "guest": "受限访问的访客用户"
                }.get(role.value, "")
            }
            for role in UserRole
        ]
        
        return APIResponseBuilder.success(
            data={
                "roles": roles,
                "default": "viewer"
            },
            message="角色列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取角色列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")
