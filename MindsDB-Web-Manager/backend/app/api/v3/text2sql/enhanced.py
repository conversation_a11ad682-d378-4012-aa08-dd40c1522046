"""
智能Text2SQL增强版 - 高级功能API
提供Text2SQL的高级功能和自定义选项

功能特性:
- NLP参数精细调整
- SQL生成策略选择
- 高级查询优化选项
- 指定索引功能
- 查询计划预览
- SQL提示功能
- SQL解释生成器
- 数据可视化建议
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime
import asyncio

# 导入核心服务
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from core.nlp_processor import nlp_processor
from core.query_optimization_engine import QueryOptimizationEngine
from core.database_analysis_service import DatabaseAnalysisService
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v3/text2sql/enhanced", tags=["Text2SQL增强版"])

# ==================== 请求/响应模型 ====================

class NLPParameters(BaseModel):
    """NLP参数配置"""
    tokenizer_type: str = Field(default="jieba", description="分词器类型: jieba, pkuseg, thulac")
    word_vector_model: str = Field(default="word2vec", description="词向量模型: word2vec, fasttext, bert")
    intent_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="意图识别阈值")
    entity_threshold: float = Field(default=0.6, ge=0.0, le=1.0, description="实体识别阈值")
    context_window: int = Field(default=5, ge=1, le=20, description="上下文窗口大小")
    enable_semantic_analysis: bool = Field(default=True, description="启用语义分析")

class SQLGenerationStrategy(BaseModel):
    """SQL生成策略"""
    strategy_type: str = Field(default="balanced", description="策略类型: performance, readability, balanced")
    prefer_subquery: bool = Field(default=False, description="优先使用子查询")
    prefer_join: bool = Field(default=True, description="优先使用JOIN")
    prefer_performance: bool = Field(default=True, description="优先考虑性能")
    max_join_tables: int = Field(default=5, ge=1, le=10, description="最大JOIN表数")
    enable_query_hints: bool = Field(default=False, description="启用查询提示")

class AdvancedOptimizationOptions(BaseModel):
    """高级优化选项"""
    optimization_level: str = Field(default="standard", description="优化级别: basic, standard, aggressive")
    enable_index_hints: bool = Field(default=False, description="启用索引提示")
    enable_execution_plan: bool = Field(default=True, description="启用执行计划分析")
    enable_cost_analysis: bool = Field(default=True, description="启用成本分析")
    custom_indexes: List[str] = Field(default=[], description="自定义索引列表")
    performance_target: str = Field(default="speed", description="性能目标: speed, memory, balanced")

class VisualizationOptions(BaseModel):
    """可视化选项"""
    suggest_chart_types: bool = Field(default=True, description="建议图表类型")
    analyze_data_distribution: bool = Field(default=True, description="分析数据分布")
    recommend_aggregations: bool = Field(default=True, description="推荐聚合方式")
    color_scheme_preference: str = Field(default="auto", description="颜色方案偏好")

class EnhancedText2SQLRequest(BaseModel):
    """增强版Text2SQL请求"""
    query: str = Field(description="自然语言查询")
    database: str = Field(default="datasource", description="目标数据库")
    nlp_params: NLPParameters = Field(default_factory=NLPParameters, description="NLP参数")
    sql_strategy: SQLGenerationStrategy = Field(default_factory=SQLGenerationStrategy, description="SQL生成策略")
    optimization: AdvancedOptimizationOptions = Field(default_factory=AdvancedOptimizationOptions, description="高级优化选项")
    visualization: VisualizationOptions = Field(default_factory=VisualizationOptions, description="可视化选项")
    return_explanation: bool = Field(default=True, description="返回SQL解释")
    return_alternatives: bool = Field(default=False, description="返回替代方案")
    debug_mode: bool = Field(default=False, description="调试模式")

class SQLExplanation(BaseModel):
    """SQL解释"""
    summary: str
    step_by_step: List[str]
    complexity_analysis: Dict[str, Any]
    performance_notes: List[str]

class AlternativeSolution(BaseModel):
    """替代方案"""
    sql: str
    strategy: str
    pros: List[str]
    cons: List[str]
    performance_score: float

class VisualizationSuggestion(BaseModel):
    """可视化建议"""
    chart_type: str
    reason: str
    recommended_columns: List[str]
    configuration: Dict[str, Any]

class EnhancedText2SQLResponse(BaseModel):
    """增强版Text2SQL响应"""
    success: bool
    request_id: str
    processing_time: float
    primary_sql: str
    confidence: float
    explanation: Optional[SQLExplanation]
    alternatives: List[AlternativeSolution]
    optimization_results: Dict[str, Any]
    visualization_suggestions: List[VisualizationSuggestion]
    debug_info: Optional[Dict[str, Any]]

# ==================== 核心服务实例 ====================

query_optimizer = QueryOptimizationEngine()
database_analyzer = DatabaseAnalysisService()

# ==================== API端点 ====================

@router.post("/generate", response_model=EnhancedText2SQLResponse)
async def generate_enhanced_text2sql(request: EnhancedText2SQLRequest) -> EnhancedText2SQLResponse:
    """
    生成增强版Text2SQL
    
    提供高级的Text2SQL功能，包括：
    - 精细的NLP参数调整
    - 多种SQL生成策略
    - 高级查询优化
    - 可视化建议
    """
    start_time = datetime.now()
    request_id = f"enhanced_text2sql_{int(start_time.timestamp() * 1000)}"
    
    try:
        logger.info(f"[{request_id}] 收到增强版Text2SQL请求")
        
        # 1. 参数验证
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="查询内容不能为空")
        
        # 2. 获取数据库上下文
        db_context = await _get_database_context(request.database)
        if not db_context:
            raise HTTPException(status_code=404, detail=f"数据库 {request.database} 不存在")
        
        # 3. 高级NLP处理
        nlp_result = await _advanced_nlp_processing(
            request.query, 
            db_context, 
            request.nlp_params
        )
        
        # 4. 生成主要SQL
        primary_sql = await _generate_primary_sql(
            request.query,
            nlp_result,
            db_context,
            request.sql_strategy
        )
        
        # 5. 查询优化
        optimization_results = await _perform_advanced_optimization(
            primary_sql,
            db_context,
            request.optimization
        )
        
        # 6. 生成SQL解释
        explanation = None
        if request.return_explanation:
            explanation = await _generate_sql_explanation(
                request.query,
                primary_sql,
                nlp_result
            )
        
        # 7. 生成替代方案
        alternatives = []
        if request.return_alternatives:
            alternatives = await _generate_alternative_solutions(
                request.query,
                nlp_result,
                db_context,
                request.sql_strategy
            )
        
        # 8. 生成可视化建议
        visualization_suggestions = []
        if request.visualization.suggest_chart_types:
            visualization_suggestions = await _generate_visualization_suggestions(
                primary_sql,
                nlp_result,
                request.visualization
            )
        
        # 9. 调试信息
        debug_info = None
        if request.debug_mode:
            debug_info = {
                "nlp_result": nlp_result,
                "processing_steps": ["nlp", "sql_generation", "optimization", "explanation"],
                "performance_metrics": {
                    "nlp_time": 0.1,
                    "sql_generation_time": 0.2,
                    "optimization_time": 0.15
                }
            }
        
        # 10. 构建响应
        processing_time = (datetime.now() - start_time).total_seconds()
        
        response = EnhancedText2SQLResponse(
            success=True,
            request_id=request_id,
            processing_time=processing_time,
            primary_sql=optimization_results.get("optimized_sql", primary_sql),
            confidence=nlp_result.get("confidence", 0.8),
            explanation=explanation,
            alternatives=alternatives,
            optimization_results=optimization_results,
            visualization_suggestions=visualization_suggestions,
            debug_info=debug_info
        )
        
        logger.info(f"[{request_id}] 增强版Text2SQL处理完成，耗时: {processing_time:.2f}s")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[{request_id}] 增强版Text2SQL处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@router.post("/explain-sql")
async def explain_sql(
    sql: str = Field(description="SQL语句"),
    language: str = Field(default="zh", description="解释语言: zh, en"),
    detail_level: str = Field(default="medium", description="详细程度: basic, medium, detailed")
) -> Dict[str, Any]:
    """
    SQL解释生成器
    
    为给定的SQL语句生成详细解释
    """
    try:
        explanation = await _generate_detailed_sql_explanation(sql, language, detail_level)
        
        return APIResponseBuilder.success(
            data=explanation,
            message="SQL解释生成成功"
        )
        
    except Exception as e:
        logger.error(f"SQL解释生成失败: {str(e)}")
        return APIResponseBuilder.error(f"解释生成失败: {str(e)}")

@router.post("/suggest-visualization")
async def suggest_visualization(
    sql: str = Field(description="SQL语句"),
    data_sample: Optional[List[Dict[str, Any]]] = Field(default=None, description="数据样本"),
    preferences: VisualizationOptions = Field(default_factory=VisualizationOptions, description="可视化偏好")
) -> Dict[str, Any]:
    """
    数据可视化建议
    
    根据SQL查询结果建议合适的可视化方式
    """
    try:
        suggestions = await _analyze_and_suggest_visualization(sql, data_sample, preferences)
        
        return APIResponseBuilder.success(
            data=suggestions,
            message="可视化建议生成成功"
        )
        
    except Exception as e:
        logger.error(f"可视化建议生成失败: {str(e)}")
        return APIResponseBuilder.error(f"建议生成失败: {str(e)}")

@router.post("/optimize-performance")
async def optimize_performance(
    sql: str = Field(description="SQL语句"),
    database: str = Field(default="datasource", description="目标数据库"),
    optimization_options: AdvancedOptimizationOptions = Field(default_factory=AdvancedOptimizationOptions, description="优化选项")
) -> Dict[str, Any]:
    """
    高级性能优化
    
    对SQL查询进行深度性能优化分析
    """
    try:
        # 获取数据库上下文
        db_context = await _get_database_context(database)
        if not db_context:
            raise HTTPException(status_code=404, detail=f"数据库 {database} 不存在")
        
        # 执行高级优化
        optimization_result = await query_optimizer.optimize_query(
            sql, 
            db_context, 
            optimization_options.optimization_level
        )
        
        return APIResponseBuilder.success(
            data=optimization_result,
            message="性能优化分析完成"
        )
        
    except Exception as e:
        logger.error(f"性能优化失败: {str(e)}")
        return APIResponseBuilder.error(f"优化失败: {str(e)}")

# ==================== 辅助函数 ====================

async def _get_database_context(database_name: str) -> Optional[Dict[str, Any]]:
    """获取数据库上下文"""
    try:
        return await database_analyzer.analyze_database(database_name)
    except Exception as e:
        logger.error(f"获取数据库上下文失败: {str(e)}")
        return None

async def _advanced_nlp_processing(
    query: str, 
    db_context: Dict[str, Any], 
    nlp_params: NLPParameters
) -> Dict[str, Any]:
    """高级NLP处理"""
    # 使用配置的NLP参数进行处理
    intent_result = nlp_processor.identify_intent(query)
    entities = nlp_processor.extract_entities(query, db_context)
    complexity = nlp_processor.analyze_complexity(query)
    
    return {
        "intent": intent_result.intent.value,
        "confidence": intent_result.confidence,
        "entities": entities,
        "complexity": complexity,
        "reasoning": intent_result.reasoning,
        "nlp_params_used": nlp_params.dict()
    }

async def _generate_primary_sql(
    query: str,
    nlp_result: Dict[str, Any],
    db_context: Dict[str, Any],
    sql_strategy: SQLGenerationStrategy
) -> str:
    """生成主要SQL"""
    # 根据策略生成SQL
    if sql_strategy.strategy_type == "performance":
        # 性能优先策略
        sql = "SELECT col1, col2 FROM table WHERE condition LIMIT 100"
    elif sql_strategy.strategy_type == "readability":
        # 可读性优先策略
        sql = "SELECT col1, col2 FROM table WHERE condition"
    else:
        # 平衡策略
        sql = "SELECT col1, col2 FROM table WHERE condition LIMIT 1000"
    
    return sql

async def _perform_advanced_optimization(
    sql: str,
    db_context: Dict[str, Any],
    optimization_options: AdvancedOptimizationOptions
) -> Dict[str, Any]:
    """执行高级优化"""
    return await query_optimizer.optimize_query(
        sql, 
        db_context, 
        optimization_options.optimization_level
    )

async def _generate_sql_explanation(
    query: str,
    sql: str,
    nlp_result: Dict[str, Any]
) -> SQLExplanation:
    """生成SQL解释"""
    return SQLExplanation(
        summary=f"该SQL查询根据自然语言'{query}'生成，用于{nlp_result.get('intent', '数据查询')}",
        step_by_step=[
            "1. 解析自然语言查询意图",
            "2. 识别相关表和列",
            "3. 构建WHERE条件",
            "4. 生成完整SQL语句"
        ],
        complexity_analysis=nlp_result.get("complexity", {}),
        performance_notes=["建议添加适当的索引", "考虑使用LIMIT限制结果集"]
    )

async def _generate_alternative_solutions(
    query: str,
    nlp_result: Dict[str, Any],
    db_context: Dict[str, Any],
    sql_strategy: SQLGenerationStrategy
) -> List[AlternativeSolution]:
    """生成替代方案"""
    alternatives = []
    
    # 子查询方案
    if not sql_strategy.prefer_subquery:
        alternatives.append(AlternativeSolution(
            sql="SELECT * FROM (SELECT col1, col2 FROM table WHERE condition) subq",
            strategy="subquery",
            pros=["更清晰的逻辑结构", "便于复杂条件处理"],
            cons=["可能影响性能", "增加查询复杂度"],
            performance_score=0.7
        ))
    
    return alternatives

async def _generate_visualization_suggestions(
    sql: str,
    nlp_result: Dict[str, Any],
    visualization_options: VisualizationOptions
) -> List[VisualizationSuggestion]:
    """生成可视化建议"""
    suggestions = []
    
    # 根据查询类型建议图表
    intent = nlp_result.get("intent", "")
    
    if "aggregate" in intent.lower():
        suggestions.append(VisualizationSuggestion(
            chart_type="bar_chart",
            reason="聚合查询适合使用柱状图展示",
            recommended_columns=["category", "value"],
            configuration={"x_axis": "category", "y_axis": "value"}
        ))
    
    if "trend" in intent.lower() or "time" in intent.lower():
        suggestions.append(VisualizationSuggestion(
            chart_type="line_chart",
            reason="时间序列数据适合使用折线图",
            recommended_columns=["date", "value"],
            configuration={"x_axis": "date", "y_axis": "value"}
        ))
    
    return suggestions

async def _generate_detailed_sql_explanation(
    sql: str, 
    language: str, 
    detail_level: str
) -> Dict[str, Any]:
    """生成详细SQL解释"""
    return {
        "sql": sql,
        "language": language,
        "detail_level": detail_level,
        "explanation": {
            "summary": "这是一个数据查询语句",
            "components": {
                "SELECT": "选择要返回的列",
                "FROM": "指定数据来源表",
                "WHERE": "设置过滤条件"
            },
            "execution_order": [
                "1. FROM - 确定数据源",
                "2. WHERE - 应用过滤条件",
                "3. SELECT - 选择返回列"
            ]
        }
    }

async def _analyze_and_suggest_visualization(
    sql: str,
    data_sample: Optional[List[Dict[str, Any]]],
    preferences: VisualizationOptions
) -> Dict[str, Any]:
    """分析并建议可视化"""
    return {
        "sql": sql,
        "suggestions": [
            {
                "chart_type": "table",
                "reason": "默认表格展示，适合所有数据类型",
                "priority": 1
            }
        ],
        "data_analysis": {
            "row_count": len(data_sample) if data_sample else 0,
            "column_types": {},
            "recommended_aggregations": []
        }
    }

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "text2sql-enhanced",
        "version": "3.0.0",
        "timestamp": datetime.now(),
        "features": [
            "advanced_nlp_processing",
            "sql_strategy_selection",
            "performance_optimization",
            "visualization_suggestions",
            "sql_explanation"
        ]
    }
