"""
Text2SQL数据访问控制API v3.0
提供数据级访问控制和查询权限管理功能

功能特性:
- 数据库级权限控制
- 表级和列级权限管理
- 查询权限验证
- 数据脱敏和过滤
- 访问日志和监控
- 权限管理和配置
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union, Set
import logging
from datetime import datetime, timedelta
import json

# 导入核心服务
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from core.data_access_controller import DataAccessController, AccessLevel, DataSensitivityLevel, MaskingType
from core.text2sql_permission_manager import Text2SQLPermissionManager
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v3/text2sql/data-access", tags=["Text2SQL数据访问控制v3"])

# ==================== 请求/响应模型 ====================

class GrantDatabasePermissionRequest(BaseModel):
    """授予数据库权限请求"""
    user_id: str = Field(description="用户ID")
    database_name: str = Field(description="数据库名称")
    access_level: str = Field(description="访问级别")
    allowed_operations: List[str] = Field(default=["SELECT"], description="允许的操作")
    expires_hours: Optional[int] = Field(default=None, description="过期小时数")

class GrantTablePermissionRequest(BaseModel):
    """授予表权限请求"""
    user_id: str = Field(description="用户ID")
    database_name: str = Field(description="数据库名称")
    table_name: str = Field(description="表名称")
    access_level: str = Field(description="访问级别")
    allowed_columns: List[str] = Field(default=[], description="允许的列")
    max_rows: Optional[int] = Field(default=None, description="最大行数")

class GrantColumnPermissionRequest(BaseModel):
    """授予列权限请求"""
    user_id: str = Field(description="用户ID")
    database_name: str = Field(description="数据库名称")
    table_name: str = Field(description="表名称")
    column_name: str = Field(description="列名称")
    access_level: str = Field(description="访问级别")
    sensitivity_level: str = Field(default="public", description="敏感级别")
    masking_type: str = Field(default="none", description="脱敏类型")

class CheckQueryPermissionRequest(BaseModel):
    """检查查询权限请求"""
    query: str = Field(description="SQL查询")
    user_id: Optional[str] = Field(default=None, description="用户ID")

class FilterResultsRequest(BaseModel):
    """过滤结果请求"""
    results: List[Dict[str, Any]] = Field(description="查询结果")
    database_name: str = Field(description="数据库名称")
    table_name: str = Field(description="表名称")
    user_id: Optional[str] = Field(default=None, description="用户ID")

# ==================== 核心服务实例 ====================

data_access_controller = DataAccessController()
permission_manager = Text2SQLPermissionManager()

# ==================== 依赖函数 ====================

async def get_current_user_from_token(request: Request):
    """从请求中获取当前用户"""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="缺少认证令牌")
        
        token = auth_header.split(" ")[1]
        user_info = permission_manager.verify_token(token)
        
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的访问令牌")
        
        return user_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=401, detail="认证失败")

async def require_admin_permission(current_user: dict = Depends(get_current_user_from_token)):
    """需要管理员权限"""
    user_id = current_user.get("user_id")
    if not permission_manager.check_permission(user_id, "permission_management"):
        raise HTTPException(status_code=403, detail="需要管理员权限")
    return current_user

# ==================== API端点 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "text2sql-data-access",
        "version": "3.0.0",
        "timestamp": datetime.now(),
        "components": {
            "data_access_controller": "ok",
            "permission_manager": "ok"
        }
    }

@router.post("/permissions/database/grant")
async def grant_database_permission(
    request: GrantDatabasePermissionRequest,
    current_user: dict = Depends(require_admin_permission)
):
    """
    授予数据库权限
    
    为指定用户授予数据库级别的访问权限
    """
    try:
        expires_at = None
        if request.expires_hours:
            expires_at = datetime.now() + timedelta(hours=request.expires_hours)
        
        permission_id = data_access_controller.grant_database_permission(
            user_id=request.user_id,
            database_name=request.database_name,
            access_level=request.access_level,
            allowed_operations=set(request.allowed_operations),
            expires_at=expires_at
        )
        
        return APIResponseBuilder.success(
            data={
                "permission_id": permission_id,
                "user_id": request.user_id,
                "database_name": request.database_name,
                "access_level": request.access_level,
                "granted": True
            },
            message="数据库权限授予成功"
        )
        
    except Exception as e:
        logger.error(f"授予数据库权限失败: {str(e)}")
        return APIResponseBuilder.error(f"授予失败: {str(e)}")

@router.post("/permissions/table/grant")
async def grant_table_permission(
    request: GrantTablePermissionRequest,
    current_user: dict = Depends(require_admin_permission)
):
    """
    授予表权限
    
    为指定用户授予表级别的访问权限
    """
    try:
        permission_id = data_access_controller.grant_table_permission(
            user_id=request.user_id,
            database_name=request.database_name,
            table_name=request.table_name,
            access_level=request.access_level,
            allowed_columns=set(request.allowed_columns) if request.allowed_columns else None,
            max_rows=request.max_rows
        )
        
        return APIResponseBuilder.success(
            data={
                "permission_id": permission_id,
                "user_id": request.user_id,
                "database_name": request.database_name,
                "table_name": request.table_name,
                "access_level": request.access_level,
                "granted": True
            },
            message="表权限授予成功"
        )
        
    except Exception as e:
        logger.error(f"授予表权限失败: {str(e)}")
        return APIResponseBuilder.error(f"授予失败: {str(e)}")

@router.post("/permissions/column/grant")
async def grant_column_permission(
    request: GrantColumnPermissionRequest,
    current_user: dict = Depends(require_admin_permission)
):
    """
    授予列权限
    
    为指定用户授予列级别的访问权限和数据脱敏配置
    """
    try:
        permission_id = data_access_controller.grant_column_permission(
            user_id=request.user_id,
            database_name=request.database_name,
            table_name=request.table_name,
            column_name=request.column_name,
            access_level=request.access_level,
            sensitivity_level=request.sensitivity_level,
            masking_type=request.masking_type
        )
        
        return APIResponseBuilder.success(
            data={
                "permission_id": permission_id,
                "user_id": request.user_id,
                "database_name": request.database_name,
                "table_name": request.table_name,
                "column_name": request.column_name,
                "access_level": request.access_level,
                "sensitivity_level": request.sensitivity_level,
                "masking_type": request.masking_type,
                "granted": True
            },
            message="列权限授予成功"
        )
        
    except Exception as e:
        logger.error(f"授予列权限失败: {str(e)}")
        return APIResponseBuilder.error(f"授予失败: {str(e)}")

@router.post("/query/check-permission")
async def check_query_permission(
    request: CheckQueryPermissionRequest,
    current_request: Request,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    检查查询权限
    
    验证用户是否有权限执行指定的SQL查询
    """
    try:
        user_id = request.user_id or current_user.get("user_id")
        user_role = current_user.get("role")
        ip_address = current_request.client.host if current_request.client else None
        
        has_permission, denial_reason = data_access_controller.check_query_permission(
            user_id=user_id,
            user_role=user_role,
            query=request.query,
            ip_address=ip_address
        )
        
        return APIResponseBuilder.success(
            data={
                "user_id": user_id,
                "query": request.query,
                "has_permission": has_permission,
                "denial_reason": denial_reason,
                "checked_at": datetime.now().isoformat()
            },
            message="查询权限检查完成"
        )
        
    except Exception as e:
        logger.error(f"检查查询权限失败: {str(e)}")
        return APIResponseBuilder.error(f"检查失败: {str(e)}")

@router.post("/results/filter")
async def filter_query_results(
    request: FilterResultsRequest,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    过滤查询结果
    
    根据用户权限过滤查询结果，应用数据脱敏
    """
    try:
        user_id = request.user_id or current_user.get("user_id")
        
        filtered_results = data_access_controller.filter_query_results(
            user_id=user_id,
            query_results=request.results,
            database_name=request.database_name,
            table_name=request.table_name
        )
        
        return APIResponseBuilder.success(
            data={
                "user_id": user_id,
                "database_name": request.database_name,
                "table_name": request.table_name,
                "original_rows": len(request.results),
                "filtered_rows": len(filtered_results),
                "results": filtered_results
            },
            message="查询结果过滤完成"
        )
        
    except Exception as e:
        logger.error(f"过滤查询结果失败: {str(e)}")
        return APIResponseBuilder.error(f"过滤失败: {str(e)}")

@router.get("/permissions/user/{user_id}")
async def get_user_permissions(
    user_id: str,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取用户权限摘要
    
    返回指定用户的所有数据访问权限
    """
    try:
        # 检查权限：只能查看自己的权限或需要管理员权限
        current_user_id = current_user.get("user_id")
        if user_id != current_user_id:
            if not permission_manager.check_permission(current_user_id, "permission_management"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        permissions_summary = data_access_controller.get_user_permissions_summary(user_id)
        
        return APIResponseBuilder.success(
            data=permissions_summary,
            message="用户权限摘要获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取用户权限摘要失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/logs/access")
async def get_access_logs(
    user_id: Optional[str] = None,
    database_name: Optional[str] = None,
    access_granted: Optional[bool] = None,
    limit: int = 100,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取访问日志
    
    返回数据访问日志，支持多种过滤条件
    """
    try:
        # 检查权限：普通用户只能查看自己的日志
        current_user_id = current_user.get("user_id")
        if user_id and user_id != current_user_id:
            if not permission_manager.check_permission(current_user_id, "audit_view"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        # 如果没有指定用户ID且不是管理员，则只显示当前用户的日志
        if not user_id and not permission_manager.check_permission(current_user_id, "audit_view"):
            user_id = current_user_id
        
        logs = data_access_controller.get_access_logs(
            user_id=user_id,
            database_name=database_name,
            access_granted=access_granted,
            limit=limit
        )
        
        return APIResponseBuilder.success(
            data={
                "logs": logs,
                "total": len(logs),
                "filters": {
                    "user_id": user_id,
                    "database_name": database_name,
                    "access_granted": access_granted,
                    "limit": limit
                }
            },
            message="访问日志获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取访问日志失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/statistics")
async def get_access_statistics(
    current_user: dict = Depends(require_admin_permission)
):
    """
    获取访问统计信息
    
    返回数据访问的统计分析数据
    """
    try:
        statistics = data_access_controller.get_access_statistics()
        
        return APIResponseBuilder.success(
            data=statistics,
            message="访问统计信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取访问统计信息失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/config/access-levels")
async def get_access_levels():
    """
    获取访问级别列表
    
    返回所有可用的访问级别
    """
    try:
        access_levels = [
            {
                "value": level.value,
                "label": {
                    "none": "无访问权限",
                    "read": "只读权限",
                    "write": "读写权限",
                    "admin": "管理员权限"
                }.get(level.value, level.value),
                "description": {
                    "none": "完全禁止访问",
                    "read": "只能查询数据",
                    "write": "可以查询和修改数据",
                    "admin": "完全控制权限"
                }.get(level.value, "")
            }
            for level in AccessLevel
        ]
        
        return APIResponseBuilder.success(
            data={
                "access_levels": access_levels,
                "default": "read"
            },
            message="访问级别列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取访问级别列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/config/sensitivity-levels")
async def get_sensitivity_levels():
    """
    获取敏感级别列表
    
    返回所有可用的数据敏感级别
    """
    try:
        sensitivity_levels = [
            {
                "value": level.value,
                "label": {
                    "public": "公开数据",
                    "internal": "内部数据",
                    "confidential": "机密数据",
                    "restricted": "限制数据"
                }.get(level.value, level.value),
                "description": {
                    "public": "可以公开访问的数据",
                    "internal": "仅限内部使用的数据",
                    "confidential": "机密级别的敏感数据",
                    "restricted": "高度限制的敏感数据"
                }.get(level.value, "")
            }
            for level in DataSensitivityLevel
        ]
        
        return APIResponseBuilder.success(
            data={
                "sensitivity_levels": sensitivity_levels,
                "default": "public"
            },
            message="敏感级别列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取敏感级别列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/config/masking-types")
async def get_masking_types():
    """
    获取脱敏类型列表
    
    返回所有可用的数据脱敏类型
    """
    try:
        masking_types = [
            {
                "value": masking_type.value,
                "label": {
                    "none": "不脱敏",
                    "partial": "部分脱敏",
                    "full": "完全脱敏",
                    "hash": "哈希脱敏",
                    "encrypt": "加密脱敏"
                }.get(masking_type.value, masking_type.value),
                "description": {
                    "none": "显示原始数据",
                    "partial": "部分字符用*替换",
                    "full": "全部字符用*替换",
                    "hash": "显示数据的哈希值",
                    "encrypt": "显示加密后的数据"
                }.get(masking_type.value, ""),
                "example": {
                    "none": "13812345678",
                    "partial": "138****5678",
                    "full": "***********",
                    "hash": "a1b2c3d4",
                    "encrypt": "[ENCRYPTED:11]"
                }.get(masking_type.value, "")
            }
            for masking_type in MaskingType
        ]
        
        return APIResponseBuilder.success(
            data={
                "masking_types": masking_types,
                "default": "none"
            },
            message="脱敏类型列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取脱敏类型列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")
