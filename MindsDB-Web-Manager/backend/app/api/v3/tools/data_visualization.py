"""
数据可视化工具API v3.0
提供数据可视化功能，包括图表创建、仪表板管理、数据查询等

功能特性:
- 图表创建和管理
- 仪表板创建和管理
- 数据源管理
- 数据查询和导出
- 实时数据更新
- 图表配置和定制
"""

from fastapi import APIRouter, HTTPException, Depends, Request, Query
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime
import json
import io

# 导入核心服务
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from core.data_visualization_manager import DataVisualizationManager, ChartType, DataSourceType, RefreshInterval
from core.text2sql_permission_manager import Text2SQLPermissionManager
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v3/tools/data-visualization", tags=["数据可视化工具v3"])

# ==================== 请求/响应模型 ====================

class CreateDataSourceRequest(BaseModel):
    """创建数据源请求"""
    name: str = Field(description="数据源名称")
    type: str = Field(description="数据源类型")
    connection_config: Dict[str, Any] = Field(description="连接配置")
    description: str = Field(default="", description="描述")

class CreateChartRequest(BaseModel):
    """创建图表请求"""
    title: str = Field(description="图表标题")
    chart_type: str = Field(description="图表类型")
    data_source_id: str = Field(description="数据源ID")
    query: str = Field(description="查询语句")
    config: Dict[str, Any] = Field(default={}, description="图表配置")
    refresh_interval: str = Field(default="none", description="刷新间隔")
    is_public: bool = Field(default=False, description="是否公开")

class UpdateChartRequest(BaseModel):
    """更新图表请求"""
    title: Optional[str] = Field(default=None, description="图表标题")
    chart_type: Optional[str] = Field(default=None, description="图表类型")
    query: Optional[str] = Field(default=None, description="查询语句")
    config: Optional[Dict[str, Any]] = Field(default=None, description="图表配置")
    refresh_interval: Optional[str] = Field(default=None, description="刷新间隔")
    is_public: Optional[bool] = Field(default=None, description="是否公开")

class CreateDashboardRequest(BaseModel):
    """创建仪表板请求"""
    name: str = Field(description="仪表板名称")
    description: str = Field(default="", description="描述")
    chart_ids: List[str] = Field(default=[], description="图表ID列表")
    layout: Dict[str, Any] = Field(default={}, description="布局配置")
    is_public: bool = Field(default=False, description="是否公开")
    tags: List[str] = Field(default=[], description="标签")

class UpdateDashboardRequest(BaseModel):
    """更新仪表板请求"""
    name: Optional[str] = Field(default=None, description="仪表板名称")
    description: Optional[str] = Field(default=None, description="描述")
    chart_ids: Optional[List[str]] = Field(default=None, description="图表ID列表")
    layout: Optional[Dict[str, Any]] = Field(default=None, description="布局配置")
    is_public: Optional[bool] = Field(default=None, description="是否公开")
    tags: Optional[List[str]] = Field(default=None, description="标签")

# ==================== 核心服务实例 ====================

visualization_manager = DataVisualizationManager()
permission_manager = Text2SQLPermissionManager()

# ==================== 依赖函数 ====================

async def get_current_user_from_token(request: Request):
    """从请求中获取当前用户"""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="缺少认证令牌")
        
        token = auth_header.split(" ")[1]
        user_info = permission_manager.verify_token(token)
        
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的访问令牌")
        
        return user_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=401, detail="认证失败")

# ==================== API端点 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "data-visualization",
        "version": "3.0.0",
        "timestamp": datetime.now(),
        "components": {
            "visualization_manager": "ok",
            "permission_manager": "ok"
        }
    }

# ==================== 数据源管理 ====================

@router.post("/data-sources")
async def create_data_source(
    request: CreateDataSourceRequest,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    创建数据源
    
    创建新的数据源配置
    """
    try:
        source_id = visualization_manager.create_data_source(
            name=request.name,
            source_type=request.type,
            connection_config=request.connection_config,
            description=request.description
        )
        
        return APIResponseBuilder.success(
            data={
                "source_id": source_id,
                "name": request.name,
                "type": request.type,
                "created": True
            },
            message="数据源创建成功"
        )
        
    except Exception as e:
        logger.error(f"创建数据源失败: {str(e)}")
        return APIResponseBuilder.error(f"创建失败: {str(e)}")

@router.get("/data-sources")
async def list_data_sources(
    is_active: Optional[bool] = Query(default=None, description="是否活跃"),
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取数据源列表
    
    返回所有可用的数据源
    """
    try:
        sources = visualization_manager.list_data_sources(is_active=is_active)
        
        return APIResponseBuilder.success(
            data={
                "data_sources": sources,
                "total": len(sources)
            },
            message="数据源列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取数据源列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

# ==================== 图表管理 ====================

@router.post("/charts")
async def create_chart(
    request: CreateChartRequest,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    创建图表
    
    创建新的数据可视化图表
    """
    try:
        chart_id = visualization_manager.create_chart(
            title=request.title,
            chart_type=request.chart_type,
            data_source_id=request.data_source_id,
            query=request.query,
            config=request.config,
            refresh_interval=request.refresh_interval,
            created_by=current_user.get("user_id", ""),
            is_public=request.is_public
        )
        
        return APIResponseBuilder.success(
            data={
                "chart_id": chart_id,
                "title": request.title,
                "chart_type": request.chart_type,
                "created": True
            },
            message="图表创建成功"
        )
        
    except Exception as e:
        logger.error(f"创建图表失败: {str(e)}")
        return APIResponseBuilder.error(f"创建失败: {str(e)}")

@router.get("/charts")
async def list_charts(
    created_by: Optional[str] = Query(default=None, description="创建者"),
    is_public: Optional[bool] = Query(default=None, description="是否公开"),
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取图表列表
    
    返回用户可访问的图表列表
    """
    try:
        # 如果不是管理员，只能查看自己的图表或公开图表
        user_id = current_user.get("user_id")
        if not permission_manager.check_permission(user_id, "chart_management"):
            created_by = user_id
        
        charts = visualization_manager.list_charts(
            created_by=created_by,
            is_public=is_public
        )
        
        return APIResponseBuilder.success(
            data={
                "charts": charts,
                "total": len(charts)
            },
            message="图表列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取图表列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/charts/{chart_id}")
async def get_chart_config(
    chart_id: str,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取图表配置
    
    返回指定图表的配置信息
    """
    try:
        chart = visualization_manager.get_chart_config(chart_id)
        
        if not chart:
            return APIResponseBuilder.error("图表不存在", status_code=404)
        
        # 检查访问权限
        user_id = current_user.get("user_id")
        if not chart.is_public and chart.created_by != user_id:
            if not permission_manager.check_permission(user_id, "chart_management"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        return APIResponseBuilder.success(
            data={
                "chart_id": chart.chart_id,
                "title": chart.title,
                "chart_type": chart.chart_type.value,
                "data_source_id": chart.data_source_id,
                "query": chart.query,
                "config": chart.config,
                "refresh_interval": chart.refresh_interval.value,
                "created_at": chart.created_at.isoformat(),
                "updated_at": chart.updated_at.isoformat(),
                "created_by": chart.created_by,
                "is_public": chart.is_public
            },
            message="图表配置获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取图表配置失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/charts/{chart_id}/data")
async def get_chart_data(
    chart_id: str,
    force_refresh: bool = Query(default=False, description="强制刷新"),
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取图表数据
    
    返回图表的数据内容
    """
    try:
        chart = visualization_manager.get_chart_config(chart_id)
        
        if not chart:
            return APIResponseBuilder.error("图表不存在", status_code=404)
        
        # 检查访问权限
        user_id = current_user.get("user_id")
        if not chart.is_public and chart.created_by != user_id:
            if not permission_manager.check_permission(user_id, "chart_view"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        chart_data = visualization_manager.get_chart_data(chart_id, force_refresh)
        
        return APIResponseBuilder.success(
            data={
                "chart_id": chart_data.chart_id,
                "data": chart_data.data,
                "columns": chart_data.columns,
                "total_rows": chart_data.total_rows,
                "generated_at": chart_data.generated_at.isoformat(),
                "query_time": chart_data.query_time
            },
            message="图表数据获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取图表数据失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.put("/charts/{chart_id}")
async def update_chart(
    chart_id: str,
    request: UpdateChartRequest,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    更新图表
    
    更新图表的配置信息
    """
    try:
        chart = visualization_manager.get_chart_config(chart_id)
        
        if not chart:
            return APIResponseBuilder.error("图表不存在", status_code=404)
        
        # 检查权限
        user_id = current_user.get("user_id")
        if chart.created_by != user_id:
            if not permission_manager.check_permission(user_id, "chart_management"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        # 准备更新数据
        update_data = {}
        for field, value in request.dict(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        success = visualization_manager.update_chart(chart_id, **update_data)
        
        if success:
            return APIResponseBuilder.success(
                data={"chart_id": chart_id, "updated": True},
                message="图表更新成功"
            )
        else:
            return APIResponseBuilder.error("图表更新失败")
        
    except Exception as e:
        logger.error(f"更新图表失败: {str(e)}")
        return APIResponseBuilder.error(f"更新失败: {str(e)}")

@router.delete("/charts/{chart_id}")
async def delete_chart(
    chart_id: str,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    删除图表
    
    删除指定的图表
    """
    try:
        chart = visualization_manager.get_chart_config(chart_id)
        
        if not chart:
            return APIResponseBuilder.error("图表不存在", status_code=404)
        
        # 检查权限
        user_id = current_user.get("user_id")
        if chart.created_by != user_id:
            if not permission_manager.check_permission(user_id, "chart_management"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        success = visualization_manager.delete_chart(chart_id)
        
        if success:
            return APIResponseBuilder.success(
                data={"chart_id": chart_id, "deleted": True},
                message="图表删除成功"
            )
        else:
            return APIResponseBuilder.error("图表删除失败")
        
    except Exception as e:
        logger.error(f"删除图表失败: {str(e)}")
        return APIResponseBuilder.error(f"删除失败: {str(e)}")

# ==================== 仪表板管理 ====================

@router.post("/dashboards")
async def create_dashboard(
    request: CreateDashboardRequest,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    创建仪表板
    
    创建新的数据可视化仪表板
    """
    try:
        dashboard_id = visualization_manager.create_dashboard(
            name=request.name,
            description=request.description,
            chart_ids=request.chart_ids,
            layout=request.layout,
            created_by=current_user.get("user_id", ""),
            is_public=request.is_public,
            tags=request.tags
        )
        
        return APIResponseBuilder.success(
            data={
                "dashboard_id": dashboard_id,
                "name": request.name,
                "created": True
            },
            message="仪表板创建成功"
        )
        
    except Exception as e:
        logger.error(f"创建仪表板失败: {str(e)}")
        return APIResponseBuilder.error(f"创建失败: {str(e)}")

@router.get("/dashboards")
async def list_dashboards(
    created_by: Optional[str] = Query(default=None, description="创建者"),
    is_public: Optional[bool] = Query(default=None, description="是否公开"),
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取仪表板列表
    
    返回用户可访问的仪表板列表
    """
    try:
        # 如果不是管理员，只能查看自己的仪表板或公开仪表板
        user_id = current_user.get("user_id")
        if not permission_manager.check_permission(user_id, "dashboard_management"):
            created_by = user_id
        
        dashboards = visualization_manager.list_dashboards(
            created_by=created_by,
            is_public=is_public
        )
        
        return APIResponseBuilder.success(
            data={
                "dashboards": dashboards,
                "total": len(dashboards)
            },
            message="仪表板列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取仪表板列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/dashboards/{dashboard_id}")
async def get_dashboard_config(
    dashboard_id: str,
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取仪表板配置
    
    返回指定仪表板的配置信息
    """
    try:
        dashboard = visualization_manager.get_dashboard_config(dashboard_id)
        
        if not dashboard:
            return APIResponseBuilder.error("仪表板不存在", status_code=404)
        
        # 检查访问权限
        user_id = current_user.get("user_id")
        if not dashboard.is_public and dashboard.created_by != user_id:
            if not permission_manager.check_permission(user_id, "dashboard_management"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        return APIResponseBuilder.success(
            data={
                "dashboard_id": dashboard.dashboard_id,
                "name": dashboard.name,
                "description": dashboard.description,
                "chart_ids": dashboard.chart_ids,
                "layout": dashboard.layout,
                "created_at": dashboard.created_at.isoformat(),
                "updated_at": dashboard.updated_at.isoformat(),
                "created_by": dashboard.created_by,
                "is_public": dashboard.is_public,
                "tags": dashboard.tags
            },
            message="仪表板配置获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取仪表板配置失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

# ==================== 数据导出 ====================

@router.get("/charts/{chart_id}/export")
async def export_chart_data(
    chart_id: str,
    format: str = Query(default="json", description="导出格式"),
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    导出图表数据
    
    导出图表数据为指定格式
    """
    try:
        chart = visualization_manager.get_chart_config(chart_id)
        
        if not chart:
            return APIResponseBuilder.error("图表不存在", status_code=404)
        
        # 检查访问权限
        user_id = current_user.get("user_id")
        if not chart.is_public and chart.created_by != user_id:
            if not permission_manager.check_permission(user_id, "chart_view"):
                return APIResponseBuilder.error("权限不足", status_code=403)
        
        export_data = visualization_manager.export_chart_data(chart_id, format)
        
        if format.lower() == "csv":
            # 返回CSV文件
            return StreamingResponse(
                io.StringIO(export_data["data"]),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename={export_data['filename']}"}
            )
        else:
            # 返回JSON数据
            return APIResponseBuilder.success(
                data=export_data,
                message="数据导出成功"
            )
        
    except Exception as e:
        logger.error(f"导出图表数据失败: {str(e)}")
        return APIResponseBuilder.error(f"导出失败: {str(e)}")

# ==================== 统计信息 ====================

@router.get("/statistics")
async def get_visualization_statistics(
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取可视化统计信息
    
    返回数据可视化的统计分析数据
    """
    try:
        statistics = visualization_manager.get_visualization_statistics()
        
        return APIResponseBuilder.success(
            data=statistics,
            message="可视化统计信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取可视化统计信息失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

# ==================== 配置信息 ====================

@router.get("/config/chart-types")
async def get_chart_types():
    """
    获取图表类型列表
    
    返回所有支持的图表类型
    """
    try:
        chart_types = [
            {
                "value": chart_type.value,
                "label": {
                    "bar": "柱状图",
                    "line": "折线图",
                    "pie": "饼图",
                    "scatter": "散点图",
                    "area": "面积图",
                    "histogram": "直方图",
                    "box": "箱线图",
                    "heatmap": "热力图",
                    "gauge": "仪表盘",
                    "table": "表格"
                }.get(chart_type.value, chart_type.value),
                "description": {
                    "bar": "用于比较不同类别的数据",
                    "line": "用于显示数据随时间的变化趋势",
                    "pie": "用于显示数据的组成比例",
                    "scatter": "用于显示两个变量之间的关系",
                    "area": "用于显示数据的累积变化",
                    "histogram": "用于显示数据的分布情况",
                    "box": "用于显示数据的统计分布",
                    "heatmap": "用于显示数据的密度分布",
                    "gauge": "用于显示单一指标的当前值",
                    "table": "用于显示详细的数据表格"
                }.get(chart_type.value, "")
            }
            for chart_type in ChartType
        ]
        
        return APIResponseBuilder.success(
            data={
                "chart_types": chart_types,
                "default": "bar"
            },
            message="图表类型列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取图表类型列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/config/refresh-intervals")
async def get_refresh_intervals():
    """
    获取刷新间隔列表
    
    返回所有支持的刷新间隔
    """
    try:
        refresh_intervals = [
            {
                "value": interval.value,
                "label": {
                    "none": "不自动刷新",
                    "30s": "30秒",
                    "1m": "1分钟",
                    "5m": "5分钟",
                    "15m": "15分钟",
                    "30m": "30分钟",
                    "1h": "1小时",
                    "6h": "6小时",
                    "24h": "24小时"
                }.get(interval.value, interval.value)
            }
            for interval in RefreshInterval
        ]
        
        return APIResponseBuilder.success(
            data={
                "refresh_intervals": refresh_intervals,
                "default": "none"
            },
            message="刷新间隔列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取刷新间隔列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")
