"""
系统监控工具API v3.0
提供系统状态监控、性能分析、告警管理等功能

功能特性:
- 系统资源监控
- 服务状态监控
- 性能指标收集
- 实时告警系统
- 监控仪表板
- 历史数据分析
- 日志监控
- 健康检查
"""

from fastapi import APIRouter, HTTPException, Depends, Request, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime

# 导入核心服务
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from core.system_monitor_manager import SystemMonitorManager, AlertLevel
from core.text2sql_permission_manager import Text2SQLPermissionManager
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v3/tools/system-monitor", tags=["系统监控工具v3"])

# ==================== 请求/响应模型 ====================

class AddAlertRuleRequest(BaseModel):
    """添加告警规则请求"""
    name: str = Field(description="规则名称")
    metric_name: str = Field(description="指标名称")
    condition: str = Field(description="条件")
    threshold: float = Field(description="阈值")
    level: str = Field(description="告警级别")
    cooldown_minutes: int = Field(default=5, description="冷却时间（分钟）")

class UpdateAlertRuleRequest(BaseModel):
    """更新告警规则请求"""
    name: Optional[str] = Field(default=None, description="规则名称")
    condition: Optional[str] = Field(default=None, description="条件")
    threshold: Optional[float] = Field(default=None, description="阈值")
    level: Optional[str] = Field(default=None, description="告警级别")
    enabled: Optional[bool] = Field(default=None, description="是否启用")
    cooldown_minutes: Optional[int] = Field(default=None, description="冷却时间（分钟）")

# ==================== 核心服务实例 ====================

monitor_manager = SystemMonitorManager()
permission_manager = Text2SQLPermissionManager()

# 启动监控
monitor_manager.start_monitoring()

# ==================== 依赖函数 ====================

async def get_current_user_from_token(request: Request):
    """从请求中获取当前用户"""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="缺少认证令牌")
        
        token = auth_header.split(" ")[1]
        user_info = permission_manager.verify_token(token)
        
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的访问令牌")
        
        return user_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=401, detail="认证失败")

async def require_monitor_permission(current_user: dict = Depends(get_current_user_from_token)):
    """需要监控权限"""
    user_id = current_user.get("user_id")
    if not permission_manager.check_permission(user_id, "system_monitor"):
        raise HTTPException(status_code=403, detail="需要系统监控权限")
    return current_user

# ==================== API端点 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "system-monitor",
        "version": "3.0.0",
        "timestamp": datetime.now(),
        "components": {
            "monitor_manager": "ok",
            "permission_manager": "ok"
        }
    }

# ==================== 系统监控 ====================

@router.get("/overview")
async def get_system_overview(
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取系统概览
    
    返回系统的整体状态和关键指标
    """
    try:
        overview = monitor_manager.get_system_overview()
        
        return APIResponseBuilder.success(
            data=overview,
            message="系统概览获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统概览失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/metrics")
async def get_metrics_history(
    metric_name: Optional[str] = Query(default=None, description="指标名称"),
    hours: int = Query(default=1, description="历史小时数"),
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取指标历史数据
    
    返回指定时间范围内的指标历史数据
    """
    try:
        metrics = monitor_manager.get_metrics_history(metric_name, hours)
        
        return APIResponseBuilder.success(
            data={
                "metrics": metrics,
                "total": len(metrics),
                "metric_name": metric_name,
                "hours": hours
            },
            message="指标历史数据获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取指标历史数据失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/services")
async def get_services_status(
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取服务状态
    
    返回所有监控服务的状态信息
    """
    try:
        services = monitor_manager.get_services_status()
        
        return APIResponseBuilder.success(
            data={
                "services": services,
                "total": len(services)
            },
            message="服务状态获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取服务状态失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

# ==================== 告警管理 ====================

@router.get("/alerts")
async def get_alerts(
    resolved: Optional[bool] = Query(default=None, description="是否已解决"),
    level: Optional[str] = Query(default=None, description="告警级别"),
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取告警列表
    
    返回系统告警信息
    """
    try:
        alerts = monitor_manager.get_alerts(resolved=resolved, level=level)
        
        return APIResponseBuilder.success(
            data={
                "alerts": alerts,
                "total": len(alerts),
                "filters": {
                    "resolved": resolved,
                    "level": level
                }
            },
            message="告警列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取告警列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(
    alert_id: str,
    current_user: dict = Depends(require_monitor_permission)
):
    """
    解决告警
    
    标记指定告警为已解决
    """
    try:
        success = monitor_manager.resolve_alert(alert_id)
        
        if success:
            return APIResponseBuilder.success(
                data={"alert_id": alert_id, "resolved": True},
                message="告警解决成功"
            )
        else:
            return APIResponseBuilder.error("告警不存在或已解决", status_code=404)
        
    except Exception as e:
        logger.error(f"解决告警失败: {str(e)}")
        return APIResponseBuilder.error(f"解决失败: {str(e)}")

# ==================== 告警规则管理 ====================

@router.get("/alert-rules")
async def get_alert_rules(
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取告警规则列表
    
    返回所有告警规则配置
    """
    try:
        rules = monitor_manager.get_alert_rules()
        
        return APIResponseBuilder.success(
            data={
                "alert_rules": rules,
                "total": len(rules)
            },
            message="告警规则列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取告警规则列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/alert-rules")
async def add_alert_rule(
    request: AddAlertRuleRequest,
    current_user: dict = Depends(require_monitor_permission)
):
    """
    添加告警规则
    
    创建新的告警规则
    """
    try:
        rule_id = monitor_manager.add_alert_rule(
            name=request.name,
            metric_name=request.metric_name,
            condition=request.condition,
            threshold=request.threshold,
            level=request.level,
            cooldown_minutes=request.cooldown_minutes
        )
        
        return APIResponseBuilder.success(
            data={
                "rule_id": rule_id,
                "name": request.name,
                "created": True
            },
            message="告警规则添加成功"
        )
        
    except Exception as e:
        logger.error(f"添加告警规则失败: {str(e)}")
        return APIResponseBuilder.error(f"添加失败: {str(e)}")

# ==================== 统计信息 ====================

@router.get("/statistics")
async def get_monitoring_statistics(
    current_user: dict = Depends(get_current_user_from_token)
):
    """
    获取监控统计信息
    
    返回监控系统的统计分析数据
    """
    try:
        statistics = monitor_manager.get_monitoring_statistics()
        
        return APIResponseBuilder.success(
            data=statistics,
            message="监控统计信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取监控统计信息失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

# ==================== 配置信息 ====================

@router.get("/config/alert-levels")
async def get_alert_levels():
    """
    获取告警级别列表
    
    返回所有可用的告警级别
    """
    try:
        alert_levels = [
            {
                "value": level.value,
                "label": {
                    "info": "信息",
                    "warning": "警告",
                    "error": "错误",
                    "critical": "严重"
                }.get(level.value, level.value),
                "description": {
                    "info": "一般信息提示",
                    "warning": "需要注意的警告",
                    "error": "系统错误",
                    "critical": "严重问题，需要立即处理"
                }.get(level.value, ""),
                "color": {
                    "info": "#17a2b8",
                    "warning": "#ffc107",
                    "error": "#fd7e14",
                    "critical": "#dc3545"
                }.get(level.value, "#6c757d")
            }
            for level in AlertLevel
        ]
        
        return APIResponseBuilder.success(
            data={
                "alert_levels": alert_levels,
                "default": "warning"
            },
            message="告警级别列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取告警级别列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/config/metric-names")
async def get_metric_names():
    """
    获取可用指标名称列表
    
    返回所有可监控的指标名称
    """
    try:
        metric_names = [
            {
                "name": "cpu_percent",
                "label": "CPU使用率",
                "unit": "%",
                "description": "CPU使用百分比"
            },
            {
                "name": "memory_percent",
                "label": "内存使用率",
                "unit": "%",
                "description": "内存使用百分比"
            },
            {
                "name": "disk_percent",
                "label": "磁盘使用率",
                "unit": "%",
                "description": "磁盘使用百分比"
            },
            {
                "name": "load_avg_1m",
                "label": "1分钟负载",
                "unit": "",
                "description": "系统1分钟平均负载"
            },
            {
                "name": "load_avg_5m",
                "label": "5分钟负载",
                "unit": "",
                "description": "系统5分钟平均负载"
            },
            {
                "name": "load_avg_15m",
                "label": "15分钟负载",
                "unit": "",
                "description": "系统15分钟平均负载"
            },
            {
                "name": "network_bytes_sent",
                "label": "网络发送字节",
                "unit": "bytes",
                "description": "网络发送的总字节数"
            },
            {
                "name": "network_bytes_recv",
                "label": "网络接收字节",
                "unit": "bytes",
                "description": "网络接收的总字节数"
            },
            {
                "name": "process_count",
                "label": "进程数量",
                "unit": "processes",
                "description": "系统运行的进程数量"
            },
            {
                "name": "system_uptime",
                "label": "系统运行时间",
                "unit": "seconds",
                "description": "系统启动后的运行时间"
            }
        ]
        
        return APIResponseBuilder.success(
            data={
                "metric_names": metric_names,
                "total": len(metric_names)
            },
            message="指标名称列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取指标名称列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/config/conditions")
async def get_alert_conditions():
    """
    获取告警条件列表
    
    返回所有可用的告警条件
    """
    try:
        conditions = [
            {
                "value": ">",
                "label": "大于",
                "description": "当指标值大于阈值时触发"
            },
            {
                "value": "<",
                "label": "小于",
                "description": "当指标值小于阈值时触发"
            },
            {
                "value": ">=",
                "label": "大于等于",
                "description": "当指标值大于等于阈值时触发"
            },
            {
                "value": "<=",
                "label": "小于等于",
                "description": "当指标值小于等于阈值时触发"
            },
            {
                "value": "==",
                "label": "等于",
                "description": "当指标值等于阈值时触发"
            },
            {
                "value": "!=",
                "label": "不等于",
                "description": "当指标值不等于阈值时触发"
            }
        ]
        
        return APIResponseBuilder.success(
            data={
                "conditions": conditions,
                "default": ">"
            },
            message="告警条件列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取告警条件列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")
