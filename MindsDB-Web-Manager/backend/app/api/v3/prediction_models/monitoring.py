"""
预测模型监控API v3.0
提供模型训练监控和Agent集成管理功能

功能特性:
- 训练任务监控
- 实时指标跟踪
- Agent集成管理
- 性能分析
- 状态管理
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime, timedelta
import asyncio
import json

# 导入核心服务
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from core.model_training_monitor import ModelTrainingMonitor, TrainingStatus, ModelType
from core.agent_model_integration import AgentModelIntegration, AgentType, AgentStatus
from core.mindsdb_client import MindsDBClient
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v3/prediction-models/monitoring", tags=["预测模型监控v3"])

# ==================== 请求/响应模型 ====================

class RegisterJobRequest(BaseModel):
    """注册训练任务请求"""
    model_name: str = Field(description="模型名称")
    model_type: str = Field(description="模型类型")
    config: Dict[str, Any] = Field(default={}, description="训练配置")
    is_agent: bool = Field(default=False, description="是否注册为Agent")
    agent_id: Optional[str] = Field(default=None, description="Agent ID")
    created_by: Optional[str] = Field(default=None, description="创建者")

class RegisterAgentRequest(BaseModel):
    """注册Agent请求"""
    model_name: str = Field(description="模型名称")
    model_version: str = Field(default="1.0.0", description="模型版本")
    agent_type: str = Field(default="prediction_model", description="Agent类型")
    description: str = Field(default="", description="描述")
    config: Dict[str, Any] = Field(default={}, description="配置")
    created_by: Optional[str] = Field(default=None, description="创建者")

class UpdateAgentStatusRequest(BaseModel):
    """更新Agent状态请求"""
    agent_id: str = Field(description="Agent ID")
    status: str = Field(description="新状态")

class RecordPerformanceRequest(BaseModel):
    """记录性能数据请求"""
    agent_id: str = Field(description="Agent ID")
    metrics: Dict[str, Any] = Field(description="性能指标")

class UpdateAgentConfigRequest(BaseModel):
    """更新Agent配置请求"""
    agent_id: str = Field(description="Agent ID")
    config: Dict[str, Any] = Field(description="新配置")

# ==================== 核心服务实例 ====================

mindsdb_client = MindsDBClient()
training_monitor = ModelTrainingMonitor(mindsdb_client)
agent_integration = AgentModelIntegration(mindsdb_client)

# 启动监控系统
training_monitor.start_monitoring()

# ==================== API端点 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "model-monitoring",
        "version": "3.0.0",
        "timestamp": datetime.now(),
        "components": {
            "training_monitor": "ok",
            "agent_integration": "ok",
            "mindsdb_client": "ok"
        }
    }

# ==================== 训练监控API ====================

@router.post("/training/start")
async def start_training_monitoring():
    """
    启动训练监控系统

    确保训练监控系统正在运行
    """
    try:
        # 启动监控系统（如果尚未启动）
        training_monitor.start_monitoring()

        return APIResponseBuilder.success(
            data={
                "monitoring_started": True,
                "active_jobs": len(training_monitor.active_jobs),
                "completed_jobs": len(training_monitor.completed_jobs)
            },
            message="训练监控系统已启动"
        )

    except Exception as e:
        logger.error(f"启动训练监控失败: {str(e)}")
        return APIResponseBuilder.error(f"启动失败: {str(e)}")

@router.post("/training/register")
async def register_training_job(request: RegisterJobRequest):
    """
    注册训练任务

    将模型训练任务注册到监控系统，开始跟踪训练进度
    """
    try:
        job_id = training_monitor.register_job(
            model_name=request.model_name,
            model_type=request.model_type,
            config=request.config,
            is_agent=request.is_agent,
            agent_id=request.agent_id,
            created_by=request.created_by
        )

        return APIResponseBuilder.success(
            data={
                "job_id": job_id,
                "model_name": request.model_name,
                "status": "registered",
                "monitoring_started": True
            },
            message="训练任务已注册"
        )

    except Exception as e:
        logger.error(f"注册训练任务失败: {str(e)}")
        return APIResponseBuilder.error(f"注册失败: {str(e)}")

@router.get("/training/jobs")
async def get_training_jobs(
    status: Optional[str] = Query(None, description="状态过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制")
):
    """
    获取训练任务列表
    
    返回所有训练任务的状态和进度信息
    """
    try:
        jobs = training_monitor.get_all_jobs(status_filter=status, limit=limit)
        
        return APIResponseBuilder.success(
            data={
                "jobs": jobs,
                "total": len(jobs),
                "filtered_by_status": status
            },
            message="训练任务列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取训练任务列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/training/jobs/{job_id}")
async def get_training_job_status(job_id: str):
    """
    获取训练任务状态
    
    返回指定训练任务的详细状态和进度信息
    """
    try:
        job_status = training_monitor.get_job_status(job_id)
        
        if not job_status:
            return APIResponseBuilder.error("训练任务不存在", status_code=404)
        
        return APIResponseBuilder.success(
            data=job_status,
            message="训练任务状态获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取训练任务状态失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/training/jobs/{job_id}/metrics")
async def get_training_job_metrics(job_id: str):
    """
    获取训练任务指标
    
    返回训练过程中的详细指标数据和历史记录
    """
    try:
        metrics = training_monitor.get_job_metrics(job_id)
        
        if not metrics:
            return APIResponseBuilder.error("训练任务不存在或无指标数据", status_code=404)
        
        return APIResponseBuilder.success(
            data=metrics,
            message="训练指标获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取训练指标失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/training/jobs/{job_id}/logs")
async def get_training_job_logs(job_id: str):
    """
    获取训练任务日志
    
    返回训练过程中的详细日志信息
    """
    try:
        logs = training_monitor.get_job_logs(job_id)
        
        if not logs:
            return APIResponseBuilder.error("训练任务不存在或无日志数据", status_code=404)
        
        return APIResponseBuilder.success(
            data=logs,
            message="训练日志获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取训练日志失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/training/jobs/{job_id}/cancel")
async def cancel_training_job(job_id: str):
    """
    取消训练任务
    
    停止指定的训练任务
    """
    try:
        success = training_monitor.cancel_job(job_id)
        
        if success:
            return APIResponseBuilder.success(
                data={"job_id": job_id, "cancelled": True},
                message="训练任务已取消"
            )
        else:
            return APIResponseBuilder.error("取消训练任务失败")
        
    except Exception as e:
        logger.error(f"取消训练任务失败: {str(e)}")
        return APIResponseBuilder.error(f"取消失败: {str(e)}")

@router.get("/training/models/{model_name}/versions")
async def get_model_versions(model_name: str):
    """
    获取模型版本
    
    返回指定模型的所有版本信息
    """
    try:
        versions = training_monitor.get_model_versions(model_name)
        
        return APIResponseBuilder.success(
            data={
                "model_name": model_name,
                "versions": versions,
                "total_versions": len(versions)
            },
            message="模型版本获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取模型版本失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

# ==================== Agent集成API ====================

@router.post("/agents/register")
async def register_model_agent(request: RegisterAgentRequest):
    """
    注册模型Agent
    
    将训练完成的模型注册为Agent，用于预测服务
    """
    try:
        agent_id = agent_integration.register_model_as_agent(
            model_name=request.model_name,
            model_version=request.model_version,
            agent_type=request.agent_type,
            description=request.description,
            config=request.config,
            created_by=request.created_by
        )
        
        return APIResponseBuilder.success(
            data={
                "agent_id": agent_id,
                "model_name": request.model_name,
                "agent_type": request.agent_type,
                "status": "registered"
            },
            message="模型Agent注册成功"
        )
        
    except Exception as e:
        logger.error(f"注册模型Agent失败: {str(e)}")
        return APIResponseBuilder.error(f"注册失败: {str(e)}")

@router.get("/agents")
async def get_model_agents(
    status: Optional[str] = Query(None, description="状态过滤"),
    agent_type: Optional[str] = Query(None, description="类型过滤")
):
    """
    获取模型Agent列表
    
    返回所有注册的模型Agent信息
    """
    try:
        agents = agent_integration.get_all_model_agents(
            status_filter=status,
            agent_type_filter=agent_type
        )
        
        return APIResponseBuilder.success(
            data={
                "agents": agents,
                "total": len(agents),
                "filters": {
                    "status": status,
                    "agent_type": agent_type
                }
            },
            message="模型Agent列表获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取模型Agent列表失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/agents/{agent_id}")
async def get_agent_details(agent_id: str):
    """
    获取Agent详细信息
    
    返回指定Agent的详细配置和状态信息
    """
    try:
        # 从Agent集成系统获取详细信息
        agents = agent_integration.get_all_model_agents()
        agent = next((a for a in agents if a['agent_id'] == agent_id), None)
        
        if not agent:
            return APIResponseBuilder.error("Agent不存在", status_code=404)
        
        return APIResponseBuilder.success(
            data=agent,
            message="Agent详细信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取Agent详细信息失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.put("/agents/status")
async def update_agent_status(request: UpdateAgentStatusRequest):
    """
    更新Agent状态
    
    修改指定Agent的运行状态
    """
    try:
        success = agent_integration.update_agent_status(
            agent_id=request.agent_id,
            status=request.status
        )
        
        if success:
            return APIResponseBuilder.success(
                data={
                    "agent_id": request.agent_id,
                    "status": request.status,
                    "updated": True
                },
                message="Agent状态更新成功"
            )
        else:
            return APIResponseBuilder.error("Agent状态更新失败")
        
    except Exception as e:
        logger.error(f"更新Agent状态失败: {str(e)}")
        return APIResponseBuilder.error(f"更新失败: {str(e)}")

@router.get("/agents/{agent_id}/performance")
async def get_agent_performance(
    agent_id: str,
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间")
):
    """
    获取Agent性能数据
    
    返回Agent的性能指标和历史数据
    """
    try:
        time_range = None
        if start_date and end_date:
            time_range = (start_date, end_date)
        
        performance = agent_integration.get_agent_performance(
            agent_id=agent_id,
            time_range=time_range
        )
        
        return APIResponseBuilder.success(
            data=performance,
            message="Agent性能数据获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取Agent性能数据失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.post("/agents/performance")
async def record_agent_performance(request: RecordPerformanceRequest):
    """
    记录Agent性能数据
    
    记录Agent的实时性能指标
    """
    try:
        agent_integration.record_agent_performance(
            agent_id=request.agent_id,
            metrics=request.metrics
        )
        
        return APIResponseBuilder.success(
            data={
                "agent_id": request.agent_id,
                "recorded": True,
                "timestamp": datetime.now().isoformat()
            },
            message="Agent性能数据记录成功"
        )
        
    except Exception as e:
        logger.error(f"记录Agent性能数据失败: {str(e)}")
        return APIResponseBuilder.error(f"记录失败: {str(e)}")

@router.put("/agents/config")
async def update_agent_config(request: UpdateAgentConfigRequest):
    """
    更新Agent配置
    
    修改Agent的运行配置参数
    """
    try:
        success = agent_integration.update_agent_config(
            agent_id=request.agent_id,
            config=request.config
        )
        
        if success:
            return APIResponseBuilder.success(
                data={
                    "agent_id": request.agent_id,
                    "config_updated": True
                },
                message="Agent配置更新成功"
            )
        else:
            return APIResponseBuilder.error("Agent配置更新失败")
        
    except Exception as e:
        logger.error(f"更新Agent配置失败: {str(e)}")
        return APIResponseBuilder.error(f"更新失败: {str(e)}")

@router.delete("/agents/{agent_id}")
async def delete_agent(agent_id: str):
    """
    删除Agent
    
    从系统中删除指定的Agent
    """
    try:
        success = agent_integration.delete_agent(agent_id)
        
        if success:
            return APIResponseBuilder.success(
                data={
                    "agent_id": agent_id,
                    "deleted": True
                },
                message="Agent删除成功"
            )
        else:
            return APIResponseBuilder.error("Agent删除失败")
        
    except Exception as e:
        logger.error(f"删除Agent失败: {str(e)}")
        return APIResponseBuilder.error(f"删除失败: {str(e)}")

@router.get("/agents/models/{model_name}")
async def get_agent_by_model(model_name: str):
    """
    根据模型获取Agent
    
    查找基于指定模型创建的Agent
    """
    try:
        agent = agent_integration.get_agent_by_model(model_name)
        
        if not agent:
            return APIResponseBuilder.error("未找到基于该模型的Agent", status_code=404)
        
        return APIResponseBuilder.success(
            data=agent,
            message="Agent信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"根据模型获取Agent失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/statistics")
async def get_monitoring_statistics():
    """
    获取监控统计信息
    
    返回训练任务和Agent的统计数据
    """
    try:
        # 获取训练任务统计
        all_jobs = training_monitor.get_all_jobs(limit=1000)
        training_stats = {
            "total_jobs": len(all_jobs),
            "active_jobs": len([j for j in all_jobs if j['status'] in ['training', 'pending', 'initializing']]),
            "completed_jobs": len([j for j in all_jobs if j['status'] == 'completed']),
            "failed_jobs": len([j for j in all_jobs if j['status'] == 'failed'])
        }
        
        # 获取Agent统计
        agent_stats = agent_integration.get_agent_statistics()
        
        return APIResponseBuilder.success(
            data={
                "training": training_stats,
                "agents": agent_stats,
                "timestamp": datetime.now().isoformat()
            },
            message="监控统计信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取监控统计信息失败: {str(e)}")
        return APIResponseBuilder.error(f"获取失败: {str(e)}")

@router.get("/list")
async def get_prediction_models_list():
    """
    获取预测模型列表

    返回所有已创建的预测模型及其状态信息
    """
    try:
        # 从MindsDB获取模型列表
        from core.mindsdb_client import MindsDBClient

        client = MindsDBClient()
        models_result = client.execute_query("SHOW MODELS")  # 移除await，因为这个方法不是异步的

        models = []
        if models_result and models_result.get('type') == 'table':
            # MindsDB返回的格式是 {"type": "table", "data": [...], "column_names": [...]}
            data_rows = models_result.get('data', [])
            column_names = models_result.get('column_names', [])

            # 创建列名到索引的映射
            col_map = {name: idx for idx, name in enumerate(column_names)}

            for row in data_rows:
                model_info = {
                    "name": row[col_map.get('NAME', 0)] if 'NAME' in col_map else '',
                    "engine": row[col_map.get('ENGINE', 1)] if 'ENGINE' in col_map else '',
                    "status": row[col_map.get('STATUS', 5)] if 'STATUS' in col_map else 'unknown',
                    "accuracy": row[col_map.get('ACCURACY', 6)] if 'ACCURACY' in col_map else None,
                    "predict": row[col_map.get('PREDICT', 7)] if 'PREDICT' in col_map else '',
                    "update_status": row[col_map.get('UPDATE_STATUS', 8)] if 'UPDATE_STATUS' in col_map else '',
                    "mindsdb_version": row[col_map.get('MINDSDB_VERSION', 9)] if 'MINDSDB_VERSION' in col_map else '',
                    "error": row[col_map.get('ERROR', 10)] if 'ERROR' in col_map else None,
                    "select_data_query": row[col_map.get('SELECT_DATA_QUERY', 11)] if 'SELECT_DATA_QUERY' in col_map else '',
                    "training_options": row[col_map.get('TRAINING_OPTIONS', 12)] if 'TRAINING_OPTIONS' in col_map else '',
                    "created_at": row[col_map.get('CREATED_AT', 17)] if 'CREATED_AT' in col_map else '',
                    "training_time": row[col_map.get('TRAINING_TIME', 18)] if 'TRAINING_TIME' in col_map else None
                }
                models.append(model_info)

        return APIResponseBuilder.success(
            data={
                "models": models,
                "total": len(models)
            },
            message=f"成功获取 {len(models)} 个模型"
        )

    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message=f"获取模型列表失败: {str(e)}"
        )
