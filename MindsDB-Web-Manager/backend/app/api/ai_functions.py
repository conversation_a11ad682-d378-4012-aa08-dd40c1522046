"""
AI功能API
提供MindsDB AI增强功能的REST接口
"""
from fastapi import APIRouter, Query
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging

from core.ai_function_manager import ai_function_manager, AIFunctionType, AnalysisType
from core.sql_generator import get_sql_generator
from core.schema_manager import get_schema_manager
from core.chinese_nlp_engine import chinese_nlp_engine
from core.api_response import APIResponseBuilder
from core.exception_handlers import BusinessException

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ai_functions", tags=["ai-functions"])

class AIAnalysisRequest(BaseModel):
    """AI分析请求"""
    query: str = Field(..., min_length=1, max_length=1000, description="自然语言查询")
    table_name: Optional[str] = Field(None, description="指定表名")
    column_name: Optional[str] = Field(None, description="指定列名")
    analysis_type: Optional[str] = Field(None, description="分析类型")
    session_id: Optional[str] = Field(None, description="会话ID")

class PredictionRequest(BaseModel):
    """预测请求"""
    query: str = Field(..., min_length=1, max_length=1000, description="自然语言查询")
    model_name: Optional[str] = Field(None, description="模型名称")
    target_column: Optional[str] = Field(None, description="预测目标列")
    conditions: Optional[Dict[str, Any]] = Field(None, description="预测条件")
    session_id: Optional[str] = Field(None, description="会话ID")

class ModelCreationRequest(BaseModel):
    """模型创建请求"""
    model_name: str = Field(..., min_length=1, max_length=100, description="模型名称")
    target_column: str = Field(..., min_length=1, description="预测目标列")
    table_name: str = Field(..., min_length=1, description="训练数据表")
    options: Optional[Dict[str, Any]] = Field(None, description="模型选项")

class InteractiveQueryRequest(BaseModel):
    """交互式查询请求"""
    query: str = Field(..., min_length=1, max_length=2000, description="自然语言查询")
    session_id: str = Field(..., description="会话ID")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="对话上下文")

class FollowUpQueryRequest(BaseModel):
    """追问查询请求"""
    query: str = Field(..., min_length=1, max_length=1000, description="追问内容")
    session_id: str = Field(..., description="会话ID")
    previous_result: Optional[Dict[str, Any]] = Field(default_factory=dict, description="之前的查询结果")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="对话上下文")

@router.post("/analyze")
async def ai_analyze(request: AIAnalysisRequest):
    """AI分析功能"""
    try:
        sql_generator = get_sql_generator()
        schema_manager = get_schema_manager()
        
        # 1. NLP理解
        nlp_result = await chinese_nlp_engine.understand_text(
            text=request.query,
            session_id=request.session_id
        )
        
        # 2. 创建生成上下文
        context = await schema_manager.create_generation_context()
        
        # 3. 生成AI分析SQL
        sql_result = await sql_generator.generate_sql(nlp_result, context)
        
        # 4. 验证是否为AI分析类型
        if sql_result.sql_type.value not in ['AI_ANALYZE', 'SELECT']:
            return APIResponseBuilder.error(
                message="查询不适合AI分析功能",
                error_code="INVALID_AI_QUERY"
            ).dict()
        
        # 5. 提供AI分析建议
        suggestions = []
        if request.table_name and request.column_name:
            suggested_types = ai_function_manager.suggest_analysis_type(
                request.column_name, 
                request.table_name
            )
            suggestions = [analysis_type.value for analysis_type in suggested_types]
        
        response_data = {
            "nlp_understanding": nlp_result.to_dict(),
            "sql_generation": sql_result.to_dict(),
            "ai_analysis_suggestions": suggestions,
            "execution_safe": sql_result.is_safe_to_execute()
        }
        
        return APIResponseBuilder.success(
            data=response_data,
            message="AI分析SQL生成成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"AI分析失败: {str(e)}")
        raise BusinessException(f"AI分析失败: {str(e)}")

@router.post("/predict")
async def ai_predict(request: PredictionRequest):
    """AI预测功能"""
    try:
        sql_generator = get_sql_generator()
        schema_manager = get_schema_manager()
        
        # 1. NLP理解
        nlp_result = await chinese_nlp_engine.understand_text(
            text=request.query,
            session_id=request.session_id
        )
        
        # 2. 创建生成上下文
        context = await schema_manager.create_generation_context()
        
        # 3. 生成预测SQL
        sql_result = await sql_generator.generate_sql(nlp_result, context)
        
        # 4. 验证是否为预测类型
        if sql_result.sql_type.value not in ['PREDICT', 'SELECT']:
            return APIResponseBuilder.error(
                message="查询不适合预测功能",
                error_code="INVALID_PREDICT_QUERY"
            ).dict()
        
        response_data = {
            "nlp_understanding": nlp_result.to_dict(),
            "sql_generation": sql_result.to_dict(),
            "execution_safe": sql_result.is_safe_to_execute()
        }
        
        return APIResponseBuilder.success(
            data=response_data,
            message="AI预测SQL生成成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"AI预测失败: {str(e)}")
        raise BusinessException(f"AI预测失败: {str(e)}")

@router.post("/create-model")
async def create_model(request: ModelCreationRequest):
    """创建ML模型"""
    try:
        # 生成CREATE MODEL SQL
        sql = ai_function_manager.generate_create_model_sql(
            model_name=request.model_name,
            target_column=request.target_column,
            table=request.table_name,
            options=request.options
        )
        
        response_data = {
            "model_name": request.model_name,
            "target_column": request.target_column,
            "table_name": request.table_name,
            "generated_sql": sql,
            "options": request.options or {}
        }
        
        return APIResponseBuilder.success(
            data=response_data,
            message="模型创建SQL生成成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"模型创建失败: {str(e)}")
        raise BusinessException(f"模型创建失败: {str(e)}")

@router.get("/functions")
async def get_ai_functions():
    """获取所有AI功能"""
    try:
        functions = ai_function_manager.get_all_functions()
        
        return APIResponseBuilder.success(
            data={"ai_functions": functions},
            message="AI功能列表获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取AI功能失败: {str(e)}")
        raise BusinessException(f"获取AI功能失败: {str(e)}")

@router.get("/analysis-types")
async def get_analysis_types():
    """获取所有分析类型"""
    try:
        analysis_types = ai_function_manager.get_all_analysis_types()
        
        return APIResponseBuilder.success(
            data={"analysis_types": analysis_types},
            message="分析类型列表获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取分析类型失败: {str(e)}")
        raise BusinessException(f"获取分析类型失败: {str(e)}")

@router.get("/suggest-analysis")
async def suggest_analysis_type(
    column_name: str = Query(..., description="列名"),
    table_context: str = Query("", description="表上下文")
):
    """建议分析类型"""
    try:
        suggestions = ai_function_manager.suggest_analysis_type(column_name, table_context)
        
        suggestion_data = []
        for analysis_type in suggestions:
            config = ai_function_manager.get_analysis_config(analysis_type)
            if config:
                suggestion_data.append({
                    "analysis_type": analysis_type.value,
                    "chinese_name": config.chinese_name,
                    "description": config.description,
                    "example_usage": config.example_usage
                })
        
        return APIResponseBuilder.success(
            data={
                "column_name": column_name,
                "suggestions": suggestion_data
            },
            message="分析类型建议获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取分析建议失败: {str(e)}")
        raise BusinessException(f"获取分析建议失败: {str(e)}")

@router.get("/examples")
async def get_ai_examples():
    """获取AI功能示例"""
    examples = {
        "ai_analyze_examples": [
            {
                "query": "分析销售额的趋势",
                "expected_sql": "SELECT AI_ANALYZE(sales_amount, 'trend_analysis') FROM sales",
                "description": "趋势分析示例"
            },
            {
                "query": "检测价格异常",
                "expected_sql": "SELECT AI_ANALYZE(price, 'anomaly_detection') FROM products",
                "description": "异常检测示例"
            },
            {
                "query": "分析用户行为模式",
                "expected_sql": "SELECT AI_ANALYZE(user_behavior, 'behavior_analysis') FROM users",
                "description": "行为分析示例"
            }
        ],
        "predict_examples": [
            {
                "query": "预测下个月的销售额",
                "expected_sql": "SELECT PREDICT(sales_amount) FROM sales_model WHERE period = 'next_month'",
                "description": "销售预测示例"
            },
            {
                "query": "预测产品价格",
                "expected_sql": "SELECT PREDICT(price) FROM price_model WHERE category = 'electronics'",
                "description": "价格预测示例"
            }
        ],
        "model_creation_examples": [
            {
                "query": "创建销售预测模型",
                "expected_sql": "CREATE MODEL sales_predictor PREDICT sales_amount FROM sales_data",
                "description": "模型创建示例"
            }
        ]
    }
    
    return APIResponseBuilder.success(
        data=examples,
        message="AI功能示例获取成功"
    ).dict()

@router.post("/interactive-query")
async def interactive_ai_query(request: InteractiveQueryRequest):
    """
    交互式AI查询处理 - 支持多轮对话和参数补全
    """
    try:
        query = request.query
        session_id = request.session_id
        context = request.context

        if not query:
            return APIResponseBuilder.error(
                message="查询内容不能为空",
                error_code="EMPTY_QUERY"
            ).dict()

        # 1. NLP理解
        nlp_result = await chinese_nlp_engine.understand_text(
            text=query,
            session_id=session_id,
            context=context
        )

        # 2. 检查是否为AI功能查询
        ai_intent = _detect_ai_intent(nlp_result)

        if not ai_intent:
            return APIResponseBuilder.success(
                data={
                    "type": "normal_query",
                    "nlp_understanding": nlp_result.to_dict(),
                    "message": "这不是AI功能查询，请使用普通的Text2SQL功能"
                },
                message="非AI功能查询"
            ).dict()

        # 3. 检查参数完整性
        missing_params = _check_missing_parameters(ai_intent, nlp_result)

        if missing_params:
            # 生成追问
            follow_up = _generate_follow_up_question(ai_intent, missing_params)

            return APIResponseBuilder.success(
                data={
                    "type": "parameter_inquiry",
                    "ai_intent": ai_intent,
                    "missing_parameters": missing_params,
                    "follow_up_question": follow_up,
                    "nlp_understanding": nlp_result.to_dict()
                },
                message="需要补充参数信息"
            ).dict()

        # 4. 参数完整，生成并执行AI查询
        sql_generator = get_sql_generator()
        schema_manager = get_schema_manager()
        generation_context = await schema_manager.create_generation_context()

        sql_result = await sql_generator.generate_sql(nlp_result, generation_context)

        # 5. 执行查询并获取结果
        execution_result = None
        if sql_result.is_safe_to_execute():
            try:
                from core.mindsdb_client import get_mindsdb_client
                client = get_mindsdb_client()
                if client:
                    execution_result = client.execute_query(sql_result.generated_sql)
            except Exception as e:
                logger.warning(f"查询执行失败: {str(e)}")

        # 6. 生成后续建议
        suggestions = _generate_follow_up_suggestions(ai_intent, execution_result)

        response_data = {
            "type": "ai_query_complete",
            "ai_intent": ai_intent,
            "nlp_understanding": nlp_result.to_dict(),
            "sql_generation": sql_result.to_dict(),
            "execution_result": execution_result,
            "follow_up_suggestions": suggestions,
            "execution_safe": sql_result.is_safe_to_execute()
        }

        return APIResponseBuilder.success(
            data=response_data,
            message="AI查询处理完成"
        ).dict()

    except Exception as e:
        logger.error(f"交互式AI查询失败: {str(e)}")
        raise BusinessException(f"交互式AI查询失败: {str(e)}")

def _detect_ai_intent(nlp_result) -> Optional[Dict[str, Any]]:
    """检测AI功能意图"""
    intents = nlp_result.intents

    for intent in intents:
        if intent.intent_type.value in ['AI_ANALYZE', 'PREDICT', 'ANALYSIS', 'PREDICTION']:
            return {
                "type": intent.intent_type.value,
                "confidence": intent.confidence,
                "description": intent.description
            }

    # 检查关键词
    text = nlp_result.processed_text.lower()
    ai_keywords = {
        "分析": "AI_ANALYZE",
        "预测": "PREDICT",
        "预估": "PREDICT",
        "趋势": "AI_ANALYZE",
        "异常": "AI_ANALYZE",
        "模式": "AI_ANALYZE",
        "聚类": "AI_ANALYZE",
        "分类": "AI_CLASSIFY"
    }

    for keyword, ai_type in ai_keywords.items():
        if keyword in text:
            return {
                "type": ai_type,
                "confidence": 0.7,
                "description": f"基于关键词'{keyword}'检测到的AI意图"
            }

    return None

def _check_missing_parameters(ai_intent: Dict[str, Any], nlp_result) -> List[str]:
    """检查缺失的参数"""
    missing = []
    entities = nlp_result.entities

    # 检查表名
    table_entities = [e for e in entities if e.entity_type.value == 'TABLE']
    if not table_entities:
        missing.append("table_name")

    # 检查列名（对于分析类查询）
    if ai_intent["type"] in ["AI_ANALYZE", "AI_CLASSIFY"]:
        column_entities = [e for e in entities if e.entity_type.value == 'COLUMN']
        if not column_entities:
            missing.append("column_name")

    # 检查模型名（对于预测类查询）
    if ai_intent["type"] in ["PREDICT"]:
        model_entities = [e for e in entities if e.entity_type.value == 'MODEL']
        if not model_entities:
            missing.append("model_name")

    return missing

def _generate_follow_up_question(ai_intent: Dict[str, Any], missing_params: List[str]) -> str:
    """生成追问问题"""
    ai_type = ai_intent["type"]

    if "table_name" in missing_params:
        return f"请问您想对哪个数据表进行{_get_action_name(ai_type)}？"

    if "column_name" in missing_params:
        return f"请问您想分析哪个字段或列？"

    if "model_name" in missing_params:
        return f"请问您想使用哪个预测模型？"

    return "请提供更多详细信息以完成您的查询。"

def _get_action_name(ai_type: str) -> str:
    """获取动作名称"""
    action_map = {
        "AI_ANALYZE": "分析",
        "PREDICT": "预测",
        "AI_CLASSIFY": "分类",
        "AI_SEARCH": "搜索"
    }
    return action_map.get(ai_type, "处理")

def _generate_follow_up_suggestions(ai_intent: Dict[str, Any], execution_result) -> List[str]:
    """生成后续建议"""
    suggestions = []
    ai_type = ai_intent["type"]

    if ai_type == "AI_ANALYZE":
        suggestions.extend([
            "您可以询问：'为什么会出现这些结果？'",
            "您可以询问：'能否预测这个趋势的发展？'",
            "您可以询问：'有什么异常需要关注吗？'"
        ])
    elif ai_type == "PREDICT":
        suggestions.extend([
            "您可以询问：'这个预测的准确度如何？'",
            "您可以询问：'影响预测结果的主要因素是什么？'",
            "您可以询问：'能否分析历史数据的趋势？'"
        ])

    # 基于执行结果添加建议
    if execution_result and execution_result.get("data"):
        suggestions.append("您可以询问：'能否用图表展示这些结果？'")
        suggestions.append("您可以询问：'能否导出这些数据？'")

    return suggestions

@router.post("/test")
async def test_ai_functions():
    """测试AI功能"""
    test_cases = [
        {
            "type": "ai_analyze",
            "query": "分析销售额的趋势",
            "expected_function": "AI_ANALYZE"
        },
        {
            "type": "predict",
            "query": "预测下个月的销售额",
            "expected_function": "PREDICT"
        },
        {
            "type": "anomaly_detection",
            "query": "检测价格异常",
            "expected_function": "AI_ANALYZE"
        },
        {
            "type": "behavior_analysis",
            "query": "分析用户购买行为",
            "expected_function": "AI_ANALYZE"
        }
    ]

    try:
        sql_generator = get_sql_generator()
        schema_manager = get_schema_manager()
        context = await schema_manager.create_generation_context()

        results = []
        
        for i, test_case in enumerate(test_cases):
            try:
                # NLP理解
                nlp_result = await chinese_nlp_engine.understand_text(
                    text=test_case["query"],
                    session_id="ai_test_session"
                )
                
                # 生成SQL
                sql_result = await sql_generator.generate_sql(nlp_result, context)
                
                # 检查是否包含期望的AI功能
                contains_expected = test_case["expected_function"] in sql_result.generated_sql.upper()
                
                results.append({
                    "test_case": i + 1,
                    "type": test_case["type"],
                    "query": test_case["query"],
                    "generated_sql": sql_result.generated_sql,
                    "sql_type": sql_result.sql_type.value,
                    "contains_expected_function": contains_expected,
                    "confidence": sql_result.confidence,
                    "success": True
                })
                
            except Exception as e:
                results.append({
                    "test_case": i + 1,
                    "type": test_case["type"],
                    "query": test_case["query"],
                    "success": False,
                    "error": str(e)
                })
        
        # 统计测试结果
        successful_tests = sum(1 for r in results if r["success"])
        ai_function_tests = sum(1 for r in results if r.get("contains_expected_function", False))
        
        summary = {
            "total_tests": len(results),
            "successful_tests": successful_tests,
            "ai_function_recognition": ai_function_tests,
            "success_rate": successful_tests / len(results) * 100,
            "ai_recognition_rate": ai_function_tests / len(results) * 100
        }
        
        return APIResponseBuilder.success(
            data={
                "test_results": results,
                "summary": summary
            },
            message=f"AI功能测试完成，成功率: {successful_tests}/{len(results)}"
        ).dict()
        
    except Exception as e:
        logger.error(f"AI功能测试失败: {str(e)}")
        raise BusinessException(f"AI功能测试失败: {str(e)}")

@router.post("/follow-up")
async def handle_follow_up_query(request: FollowUpQueryRequest):
    """
    处理基于AI结果的追问查询
    """
    try:
        query = request.query
        session_id = request.session_id
        previous_result = request.previous_result
        context = request.context

        if not query:
            return APIResponseBuilder.error(
                message="追问内容不能为空",
                error_code="EMPTY_FOLLOW_UP"
            ).dict()

        # 1. 获取对话上下文
        conversation_context = chinese_nlp_engine.get_conversation_context(session_id)

        # 2. 分析追问意图
        follow_up_intent = await _analyze_follow_up_intent(query, previous_result, conversation_context)

        # 3. 根据追问意图生成响应
        response = await _generate_follow_up_response(follow_up_intent, previous_result, context)

        return APIResponseBuilder.success(
            data=response,
            message="追问处理完成"
        ).dict()

    except Exception as e:
        logger.error(f"追问处理失败: {str(e)}")
        raise BusinessException(f"追问处理失败: {str(e)}")

async def _analyze_follow_up_intent(query: str, previous_result: Dict, conversation_context) -> Dict[str, Any]:
    """分析追问意图"""

    # 常见追问模式
    follow_up_patterns = {
        "原因分析": ["为什么", "原因", "怎么回事", "如何解释"],
        "趋势预测": ["趋势", "未来", "预测", "发展", "走势"],
        "异常检测": ["异常", "问题", "不正常", "奇怪"],
        "详细分析": ["详细", "具体", "更多", "深入"],
        "可视化": ["图表", "可视化", "展示", "图形"],
        "导出数据": ["导出", "下载", "保存", "文件"],
        "相关分析": ["相关", "关联", "影响", "因素"]
    }

    query_lower = query.lower()
    detected_intent = "general"
    confidence = 0.5

    for intent_type, keywords in follow_up_patterns.items():
        for keyword in keywords:
            if keyword in query_lower:
                detected_intent = intent_type
                confidence = 0.8
                break
        if confidence > 0.5:
            break

    return {
        "intent_type": detected_intent,
        "confidence": confidence,
        "original_query": query,
        "context_available": conversation_context is not None
    }

async def _generate_follow_up_response(follow_up_intent: Dict, previous_result: Dict, context: Dict) -> Dict[str, Any]:
    """生成追问响应"""

    intent_type = follow_up_intent["intent_type"]

    if intent_type == "原因分析":
        return await _generate_causal_analysis(previous_result, context)
    elif intent_type == "趋势预测":
        return await _generate_trend_prediction(previous_result, context)
    elif intent_type == "异常检测":
        return await _generate_anomaly_detection(previous_result, context)
    elif intent_type == "详细分析":
        return await _generate_detailed_analysis(previous_result, context)
    elif intent_type == "可视化":
        return _generate_visualization_suggestions(previous_result)
    elif intent_type == "导出数据":
        return _generate_export_options(previous_result)
    elif intent_type == "相关分析":
        return await _generate_correlation_analysis(previous_result, context)
    else:
        return _generate_general_response(previous_result)

async def _generate_causal_analysis(previous_result: Dict, context: Dict) -> Dict[str, Any]:
    """生成原因分析"""

    # 基于之前的结果生成原因分析的SQL查询
    analysis_suggestions = [
        "数据分布分析：检查数据的分布情况",
        "时间序列分析：查看数据随时间的变化",
        "相关性分析：找出影响结果的关键因素",
        "异常值检测：识别可能影响结果的异常数据"
    ]

    return {
        "response_type": "causal_analysis",
        "analysis_suggestions": analysis_suggestions,
        "recommended_queries": [
            "SELECT * FROM table_name WHERE condition ORDER BY date_column",
            "SELECT column_name, COUNT(*) FROM table_name GROUP BY column_name",
            "SELECT AVG(value), STDDEV(value) FROM table_name"
        ],
        "explanation": "基于您的查询结果，我建议从以下几个角度分析原因："
    }

async def _generate_trend_prediction(previous_result: Dict, context: Dict) -> Dict[str, Any]:
    """生成趋势预测"""

    return {
        "response_type": "trend_prediction",
        "prediction_methods": [
            "时间序列预测：基于历史数据预测未来趋势",
            "回归分析：找出影响趋势的关键变量",
            "机器学习模型：使用AI模型进行预测"
        ],
        "recommended_ai_functions": [
            "SELECT PREDICT(target_column) FROM model_name WHERE conditions",
            "SELECT AI_ANALYZE(data_column, 'trend_analysis') FROM table_name"
        ],
        "explanation": "基于当前数据，我可以帮您进行趋势预测分析："
    }

async def _generate_anomaly_detection(previous_result: Dict, context: Dict) -> Dict[str, Any]:
    """生成异常检测"""

    return {
        "response_type": "anomaly_detection",
        "detection_methods": [
            "统计异常检测：基于统计分布识别异常值",
            "时间序列异常：检测时间序列中的异常点",
            "多维异常检测：在多个维度中识别异常模式"
        ],
        "recommended_queries": [
            "SELECT * FROM table_name WHERE value > (SELECT AVG(value) + 3*STDDEV(value) FROM table_name)",
            "SELECT AI_ANALYZE(data_column, 'anomaly_detection') FROM table_name"
        ],
        "explanation": "我可以帮您检测数据中的异常情况："
    }

async def _generate_detailed_analysis(previous_result: Dict, context: Dict) -> Dict[str, Any]:
    """生成详细分析"""

    return {
        "response_type": "detailed_analysis",
        "analysis_dimensions": [
            "数据质量分析：检查数据完整性和准确性",
            "分布分析：了解数据的分布特征",
            "相关性分析：探索变量间的关系",
            "聚类分析：发现数据中的模式"
        ],
        "recommended_ai_functions": [
            "SELECT AI_ANALYZE(column_name, 'detailed_analysis') FROM table_name",
            "SELECT AI_ANALYZE(column_name, 'distribution_analysis') FROM table_name"
        ],
        "explanation": "我可以为您提供更详细的数据分析："
    }

def _generate_visualization_suggestions(previous_result: Dict) -> Dict[str, Any]:
    """生成可视化建议"""

    return {
        "response_type": "visualization",
        "chart_types": [
            {"type": "line_chart", "description": "折线图 - 适合展示趋势变化"},
            {"type": "bar_chart", "description": "柱状图 - 适合比较不同类别"},
            {"type": "scatter_plot", "description": "散点图 - 适合展示相关性"},
            {"type": "heatmap", "description": "热力图 - 适合展示数据密度"}
        ],
        "export_formats": ["PNG", "SVG", "PDF"],
        "explanation": "基于您的数据，我推荐以下可视化方式："
    }

def _generate_export_options(previous_result: Dict) -> Dict[str, Any]:
    """生成导出选项"""

    return {
        "response_type": "export_options",
        "formats": [
            {"format": "CSV", "description": "逗号分隔值文件，适合Excel打开"},
            {"format": "JSON", "description": "JSON格式，适合程序处理"},
            {"format": "Excel", "description": "Excel文件，支持多个工作表"},
            {"format": "PDF", "description": "PDF报告，包含图表和分析"}
        ],
        "explanation": "您可以选择以下格式导出数据："
    }

async def _generate_correlation_analysis(previous_result: Dict, context: Dict) -> Dict[str, Any]:
    """生成相关性分析"""

    return {
        "response_type": "correlation_analysis",
        "analysis_types": [
            "皮尔逊相关性：衡量线性相关关系",
            "斯皮尔曼相关性：衡量单调相关关系",
            "互信息：衡量非线性相关关系"
        ],
        "recommended_queries": [
            "SELECT CORR(column1, column2) FROM table_name",
            "SELECT AI_ANALYZE(column_name, 'correlation_analysis') FROM table_name"
        ],
        "explanation": "我可以帮您分析变量之间的相关关系："
    }

def _generate_general_response(previous_result: Dict) -> Dict[str, Any]:
    """生成通用响应"""

    return {
        "response_type": "general",
        "suggestions": [
            "您可以询问数据的原因分析",
            "您可以要求预测未来趋势",
            "您可以检测数据中的异常",
            "您可以要求更详细的分析",
            "您可以要求可视化展示",
            "您可以导出分析结果"
        ],
        "explanation": "我可以为您提供以下帮助："
    }
