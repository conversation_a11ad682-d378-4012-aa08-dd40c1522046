"""
仪表板API
提供系统概览、状态监控和关键指标数据
"""
from fastapi import APIRouter, HTTPException, Query, Request
from typing import List, Dict, Any, Optional
import logging
import time
import psutil
import os
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.mindsdb_client import get_mindsdb_client

# 尝试导入核心模块
try:
    from core.models import APIResponse
    from core.api_utils import create_success_response, create_error_response
    from core.exceptions import ValidationException
    CORE_MODULES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"核心模块导入失败: {e}")
    CORE_MODULES_AVAILABLE = False

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])

@router.get("/overview", response_model=Dict[str, Any])
async def get_system_overview(request: Request):
    """
    获取系统概览信息
    
    Returns:
        系统概览数据
    """
    try:
        logger.info("获取系统概览信息")
        
        # 获取系统信息
        system_info = get_system_info()
        
        # 获取MindsDB状态
        mindsdb_status = await get_mindsdb_status()
        
        # 获取数据库统计
        database_stats = await get_database_stats()
        
        # 获取Agent统计
        agent_stats = await get_agent_stats()
        
        # 获取模型统计
        model_stats = await get_model_stats()
        
        overview_data = {
            "system": system_info,
            "mindsdb": mindsdb_status,
            "databases": database_stats,
            "agents": agent_stats,
            "models": model_stats,
            "timestamp": datetime.now().isoformat()
        }
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                overview_data,
                "系统概览获取成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "系统概览获取成功",
                "data": overview_data
            }
        
    except Exception as e:
        logger.error(f"获取系统概览失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"获取系统概览失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"获取系统概览失败: {str(e)}",
                "data": None
            }

@router.get("/metrics", response_model=Dict[str, Any])
async def get_system_metrics(request: Request):
    """
    获取系统性能指标
    
    Returns:
        系统性能指标数据
    """
    try:
        logger.info("获取系统性能指标")
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 网络统计
        network = psutil.net_io_counters()
        
        # 进程信息
        process_count = len(psutil.pids())
        
        metrics_data = {
            "cpu": {
                "usage_percent": cpu_percent,
                "core_count": cpu_count,
                "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            },
            "processes": {
                "count": process_count
            },
            "timestamp": datetime.now().isoformat()
        }
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                metrics_data,
                "系统指标获取成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "系统指标获取成功",
                "data": metrics_data
            }
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"获取系统指标失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"获取系统指标失败: {str(e)}",
                "data": None
            }

@router.get("/health", response_model=Dict[str, Any])
async def get_health_status(request: Request):
    """
    获取系统健康状态
    
    Returns:
        系统健康状态数据
    """
    try:
        logger.info("获取系统健康状态")
        
        health_checks = []
        overall_status = "healthy"
        
        # MindsDB连接检查
        try:
            client = get_mindsdb_client()
            if client:
                mindsdb_result = client.execute_query("SELECT 1")
            else:
                mindsdb_result = None
            health_checks.append({
                "service": "MindsDB",
                "status": "healthy",
                "message": "连接正常",
                "response_time": 0.1
            })
        except Exception as e:
            health_checks.append({
                "service": "MindsDB",
                "status": "unhealthy",
                "message": f"连接失败: {str(e)}",
                "response_time": None
            })
            overall_status = "unhealthy"
        
        # 系统资源检查
        memory = psutil.virtual_memory()
        if memory.percent > 90:
            health_checks.append({
                "service": "Memory",
                "status": "warning",
                "message": f"内存使用率过高: {memory.percent}%",
                "response_time": None
            })
            if overall_status == "healthy":
                overall_status = "warning"
        else:
            health_checks.append({
                "service": "Memory",
                "status": "healthy",
                "message": f"内存使用正常: {memory.percent}%",
                "response_time": None
            })
        
        # 磁盘空间检查
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        if disk_percent > 90:
            health_checks.append({
                "service": "Disk",
                "status": "warning",
                "message": f"磁盘空间不足: {disk_percent:.1f}%",
                "response_time": None
            })
            if overall_status == "healthy":
                overall_status = "warning"
        else:
            health_checks.append({
                "service": "Disk",
                "status": "healthy",
                "message": f"磁盘空间充足: {disk_percent:.1f}%",
                "response_time": None
            })
        
        health_data = {
            "overall_status": overall_status,
            "checks": health_checks,
            "timestamp": datetime.now().isoformat()
        }
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                health_data,
                "健康状态获取成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "健康状态获取成功",
                "data": health_data
            }
        
    except Exception as e:
        logger.error(f"获取健康状态失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"获取健康状态失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"获取健康状态失败: {str(e)}",
                "data": None
            }

def get_system_info():
    """获取系统基本信息"""
    try:
        import platform
        
        return {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "architecture": platform.machine(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "hostname": platform.node(),
            "uptime": time.time() - psutil.boot_time()
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {str(e)}")
        return {}

async def get_mindsdb_status():
    """获取MindsDB状态信息"""
    try:
        # 执行状态查询
        client = get_mindsdb_client()
        if not client:
            return {"status": "error", "message": "MindsDB客户端未初始化"}
        result = client.execute_query("SELECT VERSION()")
        
        return {
            "status": "connected",
            "version": result.get('data', [['Unknown']])[0][0] if result.get('data') else "Unknown",
            "connection_time": 0.1,
            "last_check": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取MindsDB状态失败: {str(e)}")
        return {
            "status": "disconnected",
            "version": None,
            "connection_time": None,
            "last_check": datetime.now().isoformat(),
            "error": str(e)
        }

async def get_database_stats():
    """获取数据库统计信息"""
    try:
        # 获取数据库列表
        client = get_mindsdb_client()
        if not client:
            return []
        result = client.execute_query("SHOW DATABASES")
        databases = result.get('data', []) if result else []
        
        return {
            "total_databases": len(databases),
            "databases": [db[0] for db in databases] if databases else [],
            "last_updated": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取数据库统计失败: {str(e)}")
        return {
            "total_databases": 0,
            "databases": [],
            "last_updated": datetime.now().isoformat(),
            "error": str(e)
        }

async def get_agent_stats():
    """获取Agent统计信息"""
    try:
        # 获取Agent列表
        client = get_mindsdb_client()
        if not client:
            return []
        result = client.execute_query("SHOW AGENTS")
        agents = result.get('data', []) if result else []
        
        return {
            "total_agents": len(agents),
            "active_agents": len(agents),  # 简化实现
            "last_updated": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取Agent统计失败: {str(e)}")
        return {
            "total_agents": 0,
            "active_agents": 0,
            "last_updated": datetime.now().isoformat(),
            "error": str(e)
        }

async def get_model_stats():
    """获取模型统计信息"""
    try:
        # 获取模型列表
        client = get_mindsdb_client()
        if not client:
            return []
        result = client.execute_query("SHOW MODELS")
        models = result.get('data', []) if result else []
        
        return {
            "total_models": len(models),
            "trained_models": len(models),  # 简化实现
            "last_updated": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取模型统计失败: {str(e)}")
        return {
            "total_models": 0,
            "trained_models": 0,
            "last_updated": datetime.now().isoformat(),
            "error": str(e)
        }
