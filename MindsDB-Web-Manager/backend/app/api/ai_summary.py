"""
中文AI摘要API - 任务#123
为查询结果生成简洁的中文摘要
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
import json
import logging
from datetime import datetime

from ..core.mindsdb_client import get_mindsdb_client
from ..core.config import get_settings
from ..core.feature_flags import check_feature_enabled

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/ai-summary", tags=["AI摘要"])

# 请求模型
class QueryResultSummaryRequest(BaseModel):
    """查询结果摘要请求"""
    query_results: Union[List[Dict[str, Any]], str, Dict[str, Any]] = Field(..., description="查询结果数据")
    original_query: str = Field(..., description="原始查询语句")
    generated_sql: Optional[str] = Field(None, description="生成的SQL语句")
    agent_response: Optional[str] = Field(None, description="Agent响应")
    database: Optional[str] = Field(None, description="数据库名称")
    table_info: Optional[Dict[str, Any]] = Field(None, description="表信息")
    summary_type: str = Field("comprehensive", description="摘要类型: comprehensive, brief, detailed")
    language: str = Field("zh-CN", description="摘要语言")
    include_insights: bool = Field(True, description="是否包含数据洞察")
    include_recommendations: bool = Field(True, description="是否包含建议")

class DataInsightRequest(BaseModel):
    """数据洞察请求"""
    data: List[Dict[str, Any]] = Field(..., description="数据")
    analysis_type: str = Field("general", description="分析类型: general, statistical, trend, pattern")
    focus_columns: Optional[List[str]] = Field(None, description="重点分析的列")

class SummaryResponse(BaseModel):
    """摘要响应"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    error: Optional[str] = None

# 依赖函数
def check_ai_summary_features():
    """检查AI摘要功能是否启用"""
    if not check_feature_enabled("FEATURE_AI_SUMMARY"):
        raise HTTPException(
            status_code=503,
            detail="AI摘要功能未启用，请联系管理员"
        )

@router.post("/generate-summary", response_model=SummaryResponse)
async def generate_query_result_summary(
    request: QueryResultSummaryRequest,
    _: None = Depends(check_ai_summary_features)
):
    """
    生成查询结果的中文AI摘要
    """
    try:
        logger.info(f"开始生成AI摘要 - 查询: {request.original_query[:50]}...")
        
        # 处理查询结果数据
        processed_data = await process_query_results(request.query_results)
        
        # 构建摘要上下文
        summary_context = await build_summary_context(request, processed_data)
        
        # 生成AI摘要
        summary_result = await generate_ai_summary(summary_context, request.summary_type)
        
        # 生成数据洞察（如果启用）
        insights = None
        if request.include_insights and processed_data.get('has_data'):
            insights = await generate_data_insights(processed_data['data'], request.original_query)
        
        # 生成建议（如果启用）
        recommendations = None
        if request.include_recommendations:
            recommendations = await generate_recommendations(summary_context, processed_data)
        
        # 构建完整响应
        response_data = {
            "summary": summary_result,
            "insights": insights,
            "recommendations": recommendations,
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "summary_type": request.summary_type,
                "language": request.language,
                "data_records": processed_data.get('record_count', 0),
                "processing_time": summary_result.get('processing_time', 0)
            }
        }
        
        logger.info("AI摘要生成成功")
        return SummaryResponse(success=True, data=response_data)
        
    except Exception as e:
        logger.error(f"生成AI摘要失败: {str(e)}")
        return SummaryResponse(
            success=False,
            error=f"生成AI摘要失败: {str(e)}"
        )

@router.post("/generate-insights", response_model=SummaryResponse)
async def generate_data_insights_endpoint(
    request: DataInsightRequest,
    _: None = Depends(check_ai_summary_features)
):
    """
    生成数据洞察分析
    """
    try:
        logger.info(f"开始生成数据洞察 - 分析类型: {request.analysis_type}")
        
        insights = await generate_data_insights(
            request.data, 
            analysis_type=request.analysis_type,
            focus_columns=request.focus_columns
        )
        
        return SummaryResponse(success=True, data=insights)
        
    except Exception as e:
        logger.error(f"生成数据洞察失败: {str(e)}")
        return SummaryResponse(
            success=False,
            error=f"生成数据洞察失败: {str(e)}"
        )

# 核心处理函数
async def process_query_results(query_results: Union[List[Dict], str, Dict]) -> Dict[str, Any]:
    """处理查询结果数据"""
    try:
        # 标准化数据格式
        if isinstance(query_results, str):
            try:
                data = json.loads(query_results)
            except json.JSONDecodeError:
                data = [{"result": query_results}]
        elif isinstance(query_results, dict):
            data = [query_results]
        elif isinstance(query_results, list):
            data = query_results
        else:
            data = []
        
        # 分析数据结构
        record_count = len(data)
        columns = list(data[0].keys()) if data else []
        
        # 数据类型分析
        column_types = {}
        if data:
            for col in columns:
                sample_values = [row.get(col) for row in data[:5] if row.get(col) is not None]
                if sample_values:
                    if all(isinstance(v, (int, float)) for v in sample_values):
                        column_types[col] = "numeric"
                    elif all(isinstance(v, str) for v in sample_values):
                        column_types[col] = "text"
                    else:
                        column_types[col] = "mixed"
        
        return {
            "data": data,
            "record_count": record_count,
            "columns": columns,
            "column_types": column_types,
            "has_data": record_count > 0,
            "is_numeric_heavy": sum(1 for t in column_types.values() if t == "numeric") > len(column_types) / 2
        }
        
    except Exception as e:
        logger.error(f"处理查询结果失败: {str(e)}")
        return {
            "data": [],
            "record_count": 0,
            "columns": [],
            "column_types": {},
            "has_data": False,
            "is_numeric_heavy": False,
            "error": str(e)
        }

async def build_summary_context(request: QueryResultSummaryRequest, processed_data: Dict) -> Dict[str, Any]:
    """构建摘要上下文"""
    context = {
        "original_query": request.original_query,
        "generated_sql": request.generated_sql,
        "agent_response": request.agent_response,
        "database": request.database,
        "data_summary": {
            "record_count": processed_data["record_count"],
            "columns": processed_data["columns"],
            "column_types": processed_data["column_types"],
            "has_data": processed_data["has_data"]
        },
        "query_intent": await analyze_query_intent(request.original_query),
        "data_preview": processed_data["data"][:3] if processed_data["data"] else []
    }
    
    return context

async def analyze_query_intent(query: str) -> str:
    """分析查询意图"""
    query_lower = query.lower()
    
    if any(word in query_lower for word in ["统计", "计算", "总计", "平均", "最大", "最小", "count", "sum", "avg", "max", "min"]):
        return "statistical"
    elif any(word in query_lower for word in ["查询", "查找", "搜索", "显示", "列出", "select", "show", "list"]):
        return "search"
    elif any(word in query_lower for word in ["分析", "趋势", "对比", "比较", "分布", "analyze", "trend", "compare"]):
        return "analysis"
    elif any(word in query_lower for word in ["更新", "修改", "删除", "插入", "update", "modify", "delete", "insert"]):
        return "modification"
    else:
        return "general"

async def generate_ai_summary(context: Dict[str, Any], summary_type: str) -> Dict[str, Any]:
    """生成AI摘要"""
    try:
        # 构建提示词
        prompt = build_summary_prompt(context, summary_type)
        
        # 调用AI模型生成摘要
        # 这里可以使用MindsDB的AI模型或外部AI服务
        summary_text = await call_ai_model_for_summary(prompt)
        
        return {
            "text": summary_text,
            "type": summary_type,
            "confidence": 0.9,
            "processing_time": 200
        }
        
    except Exception as e:
        logger.error(f"生成AI摘要失败: {str(e)}")
        return {
            "text": generate_fallback_summary(context),
            "type": "fallback",
            "confidence": 0.7,
            "processing_time": 50,
            "note": "使用备用摘要生成器"
        }

def build_summary_prompt(context: Dict[str, Any], summary_type: str) -> str:
    """构建AI摘要提示词"""
    data_summary = context["data_summary"]
    
    prompt = f"""请为以下查询结果生成简洁的中文摘要：

原始查询：{context["original_query"]}
查询意图：{context["query_intent"]}
数据库：{context.get("database", "未知")}

查询结果概况：
- 记录数量：{data_summary["record_count"]}
- 数据列：{", ".join(data_summary["columns"])}
- 数据类型：{data_summary["column_types"]}

"""
    
    if context["data_preview"]:
        prompt += f"\n数据预览：\n{json.dumps(context['data_preview'], ensure_ascii=False, indent=2)}\n"
    
    if summary_type == "brief":
        prompt += "\n请生成简短摘要（1-2句话）："
    elif summary_type == "detailed":
        prompt += "\n请生成详细摘要（包含数据特点和关键发现）："
    else:
        prompt += "\n请生成综合摘要（平衡简洁性和信息量）："
    
    return prompt

async def call_ai_model_for_summary(prompt: str) -> str:
    """调用AI模型生成摘要"""
    try:
        # 这里可以集成不同的AI服务
        # 1. MindsDB的AI模型
        # 2. OpenAI API
        # 3. 本地AI模型
        
        # 暂时使用模拟响应，实际实现时替换为真实AI调用
        return await simulate_ai_response(prompt)
        
    except Exception as e:
        logger.error(f"AI模型调用失败: {str(e)}")
        raise

async def simulate_ai_response(prompt: str) -> str:
    """模拟AI响应（开发阶段使用）"""
    # 这是一个简化的模拟响应，实际部署时应替换为真实AI调用
    return "根据您的查询，系统成功检索到相关数据。查询结果显示了数据库中的关键信息，包含多个字段和记录。数据结构完整，可以为后续分析提供有价值的参考。建议您可以基于这些结果进行进一步的数据探索和分析。"

def generate_fallback_summary(context: Dict[str, Any]) -> str:
    """生成备用摘要"""
    data_summary = context["data_summary"]
    record_count = data_summary["record_count"]
    columns = data_summary["columns"]
    
    if record_count == 0:
        return f"查询「{context['original_query']}」未返回任何数据。建议检查查询条件或数据源。"
    elif record_count == 1:
        return f"查询「{context['original_query']}」返回1条记录，包含{len(columns)}个字段：{', '.join(columns[:3])}{'等' if len(columns) > 3 else ''}。"
    else:
        return f"查询「{context['original_query']}」成功返回{record_count}条记录，包含{len(columns)}个字段：{', '.join(columns[:3])}{'等' if len(columns) > 3 else ''}。数据结构完整，可用于进一步分析。"

async def generate_data_insights(data: List[Dict], query: str = "", analysis_type: str = "general", focus_columns: List[str] = None) -> Dict[str, Any]:
    """生成数据洞察"""
    try:
        if not data:
            return {"insights": ["暂无数据可供分析"], "type": "no_data"}
        
        insights = []
        
        # 基础统计洞察
        record_count = len(data)
        insights.append(f"数据集包含 {record_count} 条记录")
        
        # 列分析
        columns = list(data[0].keys()) if data else []
        if columns:
            insights.append(f"数据包含 {len(columns)} 个字段：{', '.join(columns[:5])}")
        
        # 数值字段分析
        numeric_insights = analyze_numeric_columns(data, focus_columns)
        insights.extend(numeric_insights)
        
        # 文本字段分析
        text_insights = analyze_text_columns(data, focus_columns)
        insights.extend(text_insights)
        
        return {
            "insights": insights,
            "type": analysis_type,
            "record_count": record_count,
            "column_count": len(columns)
        }
        
    except Exception as e:
        logger.error(f"生成数据洞察失败: {str(e)}")
        return {
            "insights": ["数据分析过程中出现错误"],
            "type": "error",
            "error": str(e)
        }

def analyze_numeric_columns(data: List[Dict], focus_columns: List[str] = None) -> List[str]:
    """分析数值列"""
    insights = []
    
    if not data:
        return insights
    
    for col in data[0].keys():
        if focus_columns and col not in focus_columns:
            continue
            
        values = [row.get(col) for row in data if isinstance(row.get(col), (int, float))]
        
        if len(values) > 0:
            avg_val = sum(values) / len(values)
            max_val = max(values)
            min_val = min(values)
            
            insights.append(f"{col}字段：平均值 {avg_val:.2f}，最大值 {max_val}，最小值 {min_val}")
    
    return insights

def analyze_text_columns(data: List[Dict], focus_columns: List[str] = None) -> List[str]:
    """分析文本列"""
    insights = []
    
    if not data:
        return insights
    
    for col in data[0].keys():
        if focus_columns and col not in focus_columns:
            continue
            
        values = [str(row.get(col, "")) for row in data if row.get(col) is not None]
        
        if values:
            unique_count = len(set(values))
            total_count = len(values)
            
            if unique_count < total_count:
                insights.append(f"{col}字段：{total_count}个值中有{unique_count}个唯一值")
    
    return insights

async def generate_recommendations(context: Dict[str, Any], processed_data: Dict) -> List[str]:
    """生成建议"""
    recommendations = []
    
    data_summary = context["data_summary"]
    record_count = data_summary["record_count"]
    
    if record_count == 0:
        recommendations.extend([
            "建议检查查询条件是否正确",
            "确认数据源中是否存在相关数据",
            "尝试调整查询范围或条件"
        ])
    elif record_count > 1000:
        recommendations.extend([
            "数据量较大，建议添加分页或限制条件",
            "考虑使用聚合查询来获取概览信息",
            "可以按特定条件筛选数据以提高查询效率"
        ])
    elif processed_data.get("is_numeric_heavy"):
        recommendations.extend([
            "数据包含大量数值字段，适合进行统计分析",
            "建议使用图表可视化数据趋势",
            "可以计算相关性和分布特征"
        ])
    
    # 基于查询意图的建议
    query_intent = context.get("query_intent", "general")
    if query_intent == "search":
        recommendations.append("可以尝试添加更多筛选条件来精确查找")
    elif query_intent == "statistical":
        recommendations.append("建议进一步分析数据的分布和趋势")
    
    return recommendations[:5]  # 限制建议数量
