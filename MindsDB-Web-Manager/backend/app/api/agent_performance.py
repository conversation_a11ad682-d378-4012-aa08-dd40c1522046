"""
Agent性能指标收集API
提供Agent测试性能数据的收集、存储和查询功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta
import logging
import uuid
import json

from core.auth_middleware import get_current_active_user
from core.auth_models import User
from core.database import get_db
from core.api_response import APIResponseBuilder
from core.agent_test_models import (
    AgentTestSuite, AgentTestCase, AgentTestRun, AgentTestExecution,
    AgentPerformanceMetric, AgentTestRepository, TestStatus, TestType
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/agent-performance", tags=["agent-performance"])

# 请求模型
class CreateTestSuiteRequest(BaseModel):
    """创建测试套件请求"""
    name: str = Field(..., min_length=1, max_length=200, description="测试套件名称")
    description: Optional[str] = Field(None, max_length=1000, description="测试套件描述")
    version: str = Field("1.0.0", max_length=50, description="版本号")
    config: Optional[Dict[str, Any]] = Field(None, description="测试配置")
    tags: Optional[List[str]] = Field(None, description="标签列表")

class CreateTestCaseRequest(BaseModel):
    """创建测试用例请求"""
    test_suite_id: int = Field(..., description="测试套件ID")
    name: str = Field(..., min_length=1, max_length=200, description="测试用例名称")
    description: Optional[str] = Field(None, max_length=1000, description="测试用例描述")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    expected_output: Optional[Dict[str, Any]] = Field(None, description="期望输出")
    timeout_seconds: int = Field(30, ge=1, le=3600, description="超时时间（秒）")
    max_retries: int = Field(3, ge=0, le=10, description="最大重试次数")
    priority: int = Field(1, ge=1, le=3, description="优先级（1=低，2=中，3=高）")
    tags: Optional[List[str]] = Field(None, description="标签列表")

class StartTestRunRequest(BaseModel):
    """开始测试运行请求"""
    test_suite_id: int = Field(..., description="测试套件ID")
    name: Optional[str] = Field(None, max_length=200, description="测试运行名称")
    test_type: str = Field("batch", description="测试类型")
    agent_id: str = Field(..., min_length=1, max_length=100, description="Agent ID")
    agent_name: Optional[str] = Field(None, max_length=200, description="Agent名称")
    agent_version: Optional[str] = Field(None, max_length=50, description="Agent版本")
    config: Optional[Dict[str, Any]] = Field(None, description="运行配置")
    environment: Optional[Dict[str, Any]] = Field(None, description="环境信息")

    @validator('test_type')
    def validate_test_type(cls, v):
        valid_types = [t.value for t in TestType]
        if v not in valid_types:
            raise ValueError(f"测试类型必须是: {', '.join(valid_types)}")
        return v

class RecordExecutionRequest(BaseModel):
    """记录测试执行请求"""
    test_run_id: int = Field(..., description="测试运行ID")
    test_case_id: int = Field(..., description="测试用例ID")
    execution_id: Optional[str] = Field(None, description="执行ID")
    status: str = Field(..., description="执行状态")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    actual_output: Optional[Dict[str, Any]] = Field(None, description="实际输出")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_type: Optional[str] = Field(None, description="错误类型")
    response_time_ms: Optional[float] = Field(None, ge=0, description="响应时间（毫秒）")
    cpu_usage_percent: Optional[float] = Field(None, ge=0, le=100, description="CPU使用率")
    memory_usage_mb: Optional[float] = Field(None, ge=0, description="内存使用量（MB）")
    accuracy_score: Optional[float] = Field(None, ge=0, le=1, description="准确率分数")
    similarity_score: Optional[float] = Field(None, ge=0, le=1, description="相似度分数")
    execution_context: Optional[Dict[str, Any]] = Field(None, description="执行上下文")

    @validator('status')
    def validate_status(cls, v):
        valid_statuses = [s.value for s in TestStatus]
        if v not in valid_statuses:
            raise ValueError(f"状态必须是: {', '.join(valid_statuses)}")
        return v

class RecordMetricRequest(BaseModel):
    """记录性能指标请求"""
    test_run_id: int = Field(..., description="测试运行ID")
    metric_name: str = Field(..., min_length=1, max_length=100, description="指标名称")
    metric_value: float = Field(..., description="指标值")
    metric_unit: Optional[str] = Field(None, max_length=50, description="指标单位")
    recorded_at: Optional[datetime] = Field(None, description="记录时间")
    metric_metadata: Optional[Dict[str, Any]] = Field(None, description="指标元数据")

class BatchMetricsRequest(BaseModel):
    """批量记录指标请求"""
    test_run_id: int = Field(..., description="测试运行ID")
    metrics: List[Dict[str, Any]] = Field(..., min_items=1, description="指标列表")

# 响应模型
class TestSuiteResponse(BaseModel):
    """测试套件响应"""
    id: int
    name: str
    description: Optional[str]
    version: str
    created_by: Optional[str]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    config: Optional[Dict[str, Any]]
    tags: Optional[List[str]]
    test_cases_count: int

class TestRunResponse(BaseModel):
    """测试运行响应"""
    id: int
    run_id: str
    test_suite_id: int
    name: Optional[str]
    test_type: str
    status: str
    agent_id: str
    agent_name: Optional[str]
    agent_version: Optional[str]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    total_cases: int
    passed_cases: int
    failed_cases: int
    skipped_cases: int
    avg_response_time: Optional[float]
    success_rate: Optional[float]
    duration_seconds: Optional[float]

# API端点实现
@router.post("/test-suites", response_model=dict)
async def create_test_suite(
    suite_data: CreateTestSuiteRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建测试套件"""
    try:
        repo = AgentTestRepository(db)
        
        # 检查名称是否已存在
        existing_suites = repo.list_test_suites()
        if any(suite.name == suite_data.name for suite in existing_suites):
            return APIResponseBuilder.error(
                message="测试套件名称已存在",
                error_code="SUITE_NAME_EXISTS"
            ).dict()
        
        # 创建测试套件
        suite_dict = suite_data.dict()
        suite_dict["created_by"] = current_user.username
        
        suite = repo.create_test_suite(suite_dict)
        
        return APIResponseBuilder.success(
            data={
                "id": suite.id,
                "name": suite.name,
                "description": suite.description,
                "version": suite.version,
                "created_by": suite.created_by,
                "created_at": suite.created_at.isoformat(),
                "is_active": suite.is_active
            },
            message="测试套件创建成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"创建测试套件失败: {str(e)}")
        return APIResponseBuilder.error(
            message="创建测试套件失败",
            error_code="CREATE_SUITE_ERROR"
        ).dict()

@router.get("/test-suites", response_model=dict)
async def list_test_suites(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取测试套件列表"""
    try:
        repo = AgentTestRepository(db)
        suites = repo.list_test_suites(skip=skip, limit=limit)
        
        suites_data = []
        for suite in suites:
            suites_data.append({
                "id": suite.id,
                "name": suite.name,
                "description": suite.description,
                "version": suite.version,
                "created_by": suite.created_by,
                "created_at": suite.created_at.isoformat() if suite.created_at else None,
                "updated_at": suite.updated_at.isoformat() if suite.updated_at else None,
                "is_active": suite.is_active,
                "config": suite.config,
                "tags": suite.tags,
                "test_cases_count": len(suite.test_cases) if suite.test_cases else 0
            })
        
        return APIResponseBuilder.success(
            data={
                "test_suites": suites_data,
                "total": len(suites_data),
                "skip": skip,
                "limit": limit
            },
            message="获取测试套件列表成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取测试套件列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取测试套件列表失败",
            error_code="LIST_SUITES_ERROR"
        ).dict()

@router.post("/test-cases", response_model=dict)
async def create_test_case(
    case_data: CreateTestCaseRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建测试用例"""
    try:
        repo = AgentTestRepository(db)
        
        # 验证测试套件是否存在
        suite = repo.get_test_suite(case_data.test_suite_id)
        if not suite:
            return APIResponseBuilder.error(
                message="测试套件不存在",
                error_code="SUITE_NOT_FOUND"
            ).dict()
        
        # 创建测试用例
        test_case = AgentTestCase(**case_data.dict())
        db.add(test_case)
        db.commit()
        db.refresh(test_case)
        
        return APIResponseBuilder.success(
            data={
                "id": test_case.id,
                "test_suite_id": test_case.test_suite_id,
                "name": test_case.name,
                "description": test_case.description,
                "priority": test_case.priority,
                "timeout_seconds": test_case.timeout_seconds,
                "created_at": test_case.created_at.isoformat()
            },
            message="测试用例创建成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"创建测试用例失败: {str(e)}")
        return APIResponseBuilder.error(
            message="创建测试用例失败",
            error_code="CREATE_CASE_ERROR"
        ).dict()

@router.post("/test-runs", response_model=dict)
async def start_test_run(
    run_data: StartTestRunRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """开始测试运行"""
    try:
        repo = AgentTestRepository(db)
        
        # 验证测试套件是否存在
        suite = repo.get_test_suite(run_data.test_suite_id)
        if not suite:
            return APIResponseBuilder.error(
                message="测试套件不存在",
                error_code="SUITE_NOT_FOUND"
            ).dict()
        
        # 创建测试运行
        run_dict = run_data.dict()
        run_dict["run_id"] = str(uuid.uuid4())
        run_dict["status"] = TestStatus.PENDING.value
        run_dict["total_cases"] = len(suite.test_cases) if suite.test_cases else 0
        
        if not run_dict.get("name"):
            run_dict["name"] = f"{suite.name} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        test_run = repo.create_test_run(run_dict)
        
        return APIResponseBuilder.success(
            data={
                "id": test_run.id,
                "run_id": test_run.run_id,
                "test_suite_id": test_run.test_suite_id,
                "name": test_run.name,
                "test_type": test_run.test_type,
                "status": test_run.status,
                "agent_id": test_run.agent_id,
                "total_cases": test_run.total_cases,
                "created_at": test_run.created_at.isoformat()
            },
            message="测试运行创建成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"创建测试运行失败: {str(e)}")
        return APIResponseBuilder.error(
            message="创建测试运行失败",
            error_code="CREATE_RUN_ERROR"
        ).dict()

@router.post("/test-executions", response_model=dict)
async def record_test_execution(
    execution_data: RecordExecutionRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """记录测试执行结果"""
    try:
        # 验证测试运行和测试用例是否存在
        test_run = db.query(AgentTestRun).filter(AgentTestRun.id == execution_data.test_run_id).first()
        if not test_run:
            return APIResponseBuilder.error(
                message="测试运行不存在",
                error_code="RUN_NOT_FOUND"
            ).dict()

        test_case = db.query(AgentTestCase).filter(AgentTestCase.id == execution_data.test_case_id).first()
        if not test_case:
            return APIResponseBuilder.error(
                message="测试用例不存在",
                error_code="CASE_NOT_FOUND"
            ).dict()

        # 创建测试执行记录
        execution_dict = execution_data.dict()
        if not execution_dict.get("execution_id"):
            execution_dict["execution_id"] = str(uuid.uuid4())

        execution = AgentTestExecution(**execution_dict)
        db.add(execution)
        db.commit()
        db.refresh(execution)

        return APIResponseBuilder.success(
            data={
                "id": execution.id,
                "execution_id": execution.execution_id,
                "test_run_id": execution.test_run_id,
                "test_case_id": execution.test_case_id,
                "status": execution.status,
                "response_time_ms": execution.response_time_ms,
                "accuracy_score": execution.accuracy_score,
                "created_at": execution.created_at.isoformat()
            },
            message="测试执行记录成功"
        ).dict()

    except Exception as e:
        logger.error(f"记录测试执行失败: {str(e)}")
        return APIResponseBuilder.error(
            message="记录测试执行失败",
            error_code="RECORD_EXECUTION_ERROR"
        ).dict()

@router.post("/metrics", response_model=dict)
async def record_performance_metric(
    metric_data: RecordMetricRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """记录性能指标"""
    try:
        repo = AgentTestRepository(db)

        # 验证测试运行是否存在
        test_run = db.query(AgentTestRun).filter(AgentTestRun.id == metric_data.test_run_id).first()
        if not test_run:
            return APIResponseBuilder.error(
                message="测试运行不存在",
                error_code="RUN_NOT_FOUND"
            ).dict()

        # 记录性能指标
        metric_dict = metric_data.dict()
        if not metric_dict.get("recorded_at"):
            metric_dict["recorded_at"] = datetime.now()

        metric = repo.record_performance_metric(metric_dict)

        return APIResponseBuilder.success(
            data={
                "id": metric.id,
                "test_run_id": metric.test_run_id,
                "metric_name": metric.metric_name,
                "metric_value": metric.metric_value,
                "metric_unit": metric.metric_unit,
                "recorded_at": metric.recorded_at.isoformat()
            },
            message="性能指标记录成功"
        ).dict()

    except Exception as e:
        logger.error(f"记录性能指标失败: {str(e)}")
        return APIResponseBuilder.error(
            message="记录性能指标失败",
            error_code="RECORD_METRIC_ERROR"
        ).dict()

@router.post("/metrics/batch", response_model=dict)
async def record_batch_metrics(
    batch_data: BatchMetricsRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """批量记录性能指标"""
    try:
        repo = AgentTestRepository(db)

        # 验证测试运行是否存在
        test_run = db.query(AgentTestRun).filter(AgentTestRun.id == batch_data.test_run_id).first()
        if not test_run:
            return APIResponseBuilder.error(
                message="测试运行不存在",
                error_code="RUN_NOT_FOUND"
            ).dict()

        # 批量记录指标
        recorded_metrics = []
        for metric_data in batch_data.metrics:
            metric_data["test_run_id"] = batch_data.test_run_id
            if not metric_data.get("recorded_at"):
                metric_data["recorded_at"] = datetime.now()

            metric = repo.record_performance_metric(metric_data)
            recorded_metrics.append({
                "id": metric.id,
                "metric_name": metric.metric_name,
                "metric_value": metric.metric_value,
                "metric_unit": metric.metric_unit
            })

        return APIResponseBuilder.success(
            data={
                "test_run_id": batch_data.test_run_id,
                "recorded_count": len(recorded_metrics),
                "metrics": recorded_metrics
            },
            message=f"批量记录 {len(recorded_metrics)} 个性能指标成功"
        ).dict()

    except Exception as e:
        logger.error(f"批量记录性能指标失败: {str(e)}")
        return APIResponseBuilder.error(
            message="批量记录性能指标失败",
            error_code="BATCH_RECORD_ERROR"
        ).dict()

@router.get("/test-runs/{run_id}", response_model=dict)
async def get_test_run(
    run_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取测试运行详情"""
    try:
        repo = AgentTestRepository(db)
        test_run = repo.get_test_run(run_id)

        if not test_run:
            return APIResponseBuilder.error(
                message="测试运行不存在",
                error_code="RUN_NOT_FOUND"
            ).dict()

        # 获取执行记录
        executions = db.query(AgentTestExecution).filter(
            AgentTestExecution.test_run_id == test_run.id
        ).all()

        # 获取性能指标
        metrics = repo.get_performance_metrics(test_run.id)

        return APIResponseBuilder.success(
            data={
                "id": test_run.id,
                "run_id": test_run.run_id,
                "test_suite_id": test_run.test_suite_id,
                "name": test_run.name,
                "test_type": test_run.test_type,
                "status": test_run.status,
                "agent_id": test_run.agent_id,
                "agent_name": test_run.agent_name,
                "agent_version": test_run.agent_version,
                "started_at": test_run.started_at.isoformat() if test_run.started_at else None,
                "completed_at": test_run.completed_at.isoformat() if test_run.completed_at else None,
                "created_at": test_run.created_at.isoformat(),
                "total_cases": test_run.total_cases,
                "passed_cases": test_run.passed_cases,
                "failed_cases": test_run.failed_cases,
                "skipped_cases": test_run.skipped_cases,
                "avg_response_time": test_run.avg_response_time,
                "success_rate": test_run.success_rate,
                "duration_seconds": test_run.duration_seconds,
                "executions_count": len(executions),
                "metrics_count": len(metrics)
            },
            message="获取测试运行详情成功"
        ).dict()

    except Exception as e:
        logger.error(f"获取测试运行详情失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取测试运行详情失败",
            error_code="GET_RUN_ERROR"
        ).dict()

@router.put("/test-runs/{run_id}/status", response_model=dict)
async def update_test_run_status(
    run_id: str,
    status: str = Query(..., description="新状态"),
    completed_at: Optional[datetime] = Query(None, description="完成时间"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新测试运行状态"""
    try:
        # 验证状态值
        valid_statuses = [s.value for s in TestStatus]
        if status not in valid_statuses:
            return APIResponseBuilder.error(
                message=f"无效的状态值，必须是: {', '.join(valid_statuses)}",
                error_code="INVALID_STATUS"
            ).dict()

        repo = AgentTestRepository(db)

        # 更新状态
        update_data = {}
        if completed_at:
            update_data["completed_at"] = completed_at
        elif status == TestStatus.COMPLETED.value:
            update_data["completed_at"] = datetime.now()

        success = repo.update_test_run_status(run_id, TestStatus(status), **update_data)

        if not success:
            return APIResponseBuilder.error(
                message="测试运行不存在",
                error_code="RUN_NOT_FOUND"
            ).dict()

        return APIResponseBuilder.success(
            data={
                "run_id": run_id,
                "status": status,
                "updated_at": datetime.now().isoformat()
            },
            message="测试运行状态更新成功"
        ).dict()

    except Exception as e:
        logger.error(f"更新测试运行状态失败: {str(e)}")
        return APIResponseBuilder.error(
            message="更新测试运行状态失败",
            error_code="UPDATE_STATUS_ERROR"
        ).dict()

@router.post("/batch-test/start", response_model=dict)
async def start_batch_test(
    test_suite_id: int = Query(..., description="测试套件ID"),
    agent_id: str = Query(..., description="Agent ID"),
    agent_name: Optional[str] = Query(None, description="Agent名称"),
    config: Optional[str] = Query(None, description="配置JSON字符串"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """启动批量测试"""
    try:
        from core.batch_test_manager import get_batch_test_manager

        # 解析配置
        test_config = {}
        if config:
            try:
                test_config = json.loads(config)
            except json.JSONDecodeError:
                return APIResponseBuilder.error(
                    message="配置JSON格式错误",
                    error_code="INVALID_CONFIG_JSON"
                ).dict()

        # 启动批量测试
        manager = get_batch_test_manager()
        run_id = manager.start_batch_test(
            test_suite_id=test_suite_id,
            agent_id=agent_id,
            agent_name=agent_name,
            config=test_config
        )

        return APIResponseBuilder.success(
            data={
                "run_id": run_id,
                "test_suite_id": test_suite_id,
                "agent_id": agent_id,
                "agent_name": agent_name,
                "config": test_config,
                "started_at": datetime.now().isoformat()
            },
            message="批量测试已启动"
        ).dict()

    except ValueError as e:
        return APIResponseBuilder.error(
            message=str(e),
            error_code="VALIDATION_ERROR"
        ).dict()
    except Exception as e:
        logger.error(f"启动批量测试失败: {str(e)}")
        return APIResponseBuilder.error(
            message="启动批量测试失败",
            error_code="START_BATCH_TEST_ERROR"
        ).dict()

@router.get("/batch-test/status/{run_id}", response_model=dict)
async def get_batch_test_status(
    run_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """获取批量测试状态"""
    try:
        from core.batch_test_manager import get_batch_test_manager

        manager = get_batch_test_manager()
        status = manager.get_run_status(run_id)

        if not status:
            return APIResponseBuilder.error(
                message="测试运行不存在或已完成",
                error_code="RUN_NOT_FOUND"
            ).dict()

        return APIResponseBuilder.success(
            data=status,
            message="获取批量测试状态成功"
        ).dict()

    except Exception as e:
        logger.error(f"获取批量测试状态失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取批量测试状态失败",
            error_code="GET_STATUS_ERROR"
        ).dict()

@router.get("/batch-test/active-runs", response_model=dict)
async def get_active_batch_tests(
    current_user: User = Depends(get_current_active_user)
):
    """获取所有活跃的批量测试"""
    try:
        from core.batch_test_manager import get_batch_test_manager

        manager = get_batch_test_manager()
        active_runs = manager.get_all_active_runs()

        return APIResponseBuilder.success(
            data={
                "active_runs": active_runs,
                "total_count": len(active_runs)
            },
            message="获取活跃批量测试成功"
        ).dict()

    except Exception as e:
        logger.error(f"获取活跃批量测试失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取活跃批量测试失败",
            error_code="GET_ACTIVE_RUNS_ERROR"
        ).dict()

@router.post("/batch-test/cancel/{run_id}", response_model=dict)
async def cancel_batch_test(
    run_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """取消批量测试"""
    try:
        from core.batch_test_manager import get_batch_test_manager

        manager = get_batch_test_manager()
        success = manager.cancel_run(run_id)

        if not success:
            return APIResponseBuilder.error(
                message="测试运行不存在或无法取消",
                error_code="CANCEL_FAILED"
            ).dict()

        return APIResponseBuilder.success(
            data={
                "run_id": run_id,
                "cancelled_at": datetime.now().isoformat()
            },
            message="批量测试已取消"
        ).dict()

    except Exception as e:
        logger.error(f"取消批量测试失败: {str(e)}")
        return APIResponseBuilder.error(
            message="取消批量测试失败",
            error_code="CANCEL_ERROR"
        ).dict()

@router.get("/batch-test/executor-status", response_model=dict)
async def get_executor_status(
    current_user: User = Depends(get_current_active_user)
):
    """获取执行器状态"""
    try:
        from core.batch_test_executor import get_batch_executor

        executor = get_batch_executor()
        status = executor.get_queue_status()

        return APIResponseBuilder.success(
            data=status,
            message="获取执行器状态成功"
        ).dict()

    except Exception as e:
        logger.error(f"获取执行器状态失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取执行器状态失败",
            error_code="GET_EXECUTOR_STATUS_ERROR"
        ).dict()
