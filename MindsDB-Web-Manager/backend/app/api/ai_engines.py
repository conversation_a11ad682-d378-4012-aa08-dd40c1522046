"""
AI引擎管理API
提供AI引擎的配置、状态查询和调用功能
"""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import logging
from core.ai_engine_manager import ai_engine_manager
from core.config import app_config

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ai-engines", tags=["ai-engines"])

class TextAnalysisRequest(BaseModel):
    text: str
    task_type: str = "text_analysis"  # text_analysis, sentiment_analysis
    model_id: Optional[str] = None
    categories: Optional[List[str]] = None

class TextGenerationRequest(BaseModel):
    prompt: str
    model_id: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None

class PredictionRequest(BaseModel):
    features: Dict[str, Any]
    model_id: Optional[str] = None

@router.get("/status")
async def get_engines_status() -> Dict[str, Any]:
    """
    获取AI引擎状态
    
    Returns:
        AI引擎状态信息
    """
    try:
        status = ai_engine_manager.get_engine_status()
        return {
            "status": "success",
            "data": status
        }
    except Exception as e:
        logger.error(f"获取AI引擎状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取AI引擎状态失败: {str(e)}")

@router.get("/list")
async def list_available_engines() -> Dict[str, Any]:
    """
    列出所有可用的AI引擎
    
    Returns:
        可用AI引擎列表
    """
    try:
        engines = ai_engine_manager.list_available_engines()
        return {
            "status": "success",
            "data": engines
        }
    except Exception as e:
        logger.error(f"列出AI引擎失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出AI引擎失败: {str(e)}")

@router.post("/analyze")
async def analyze_text(request: TextAnalysisRequest) -> Dict[str, Any]:
    """
    文本分析
    
    Args:
        request: 文本分析请求
        
    Returns:
        分析结果
    """
    try:
        kwargs = {}
        if request.categories:
            kwargs['categories'] = request.categories
        
        result = await ai_engine_manager.analyze_text(
            text=request.text,
            task_type=request.task_type,
            model_id=request.model_id,
            **kwargs
        )
        
        return {
            "status": "success",
            "data": {
                "result": result.result,
                "confidence": result.confidence,
                "processing_time": result.processing_time,
                "model_info": result.model_info,
                "success": result.success,
                "error_message": result.error_message
            }
        }
    except Exception as e:
        logger.error(f"文本分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文本分析失败: {str(e)}")

@router.post("/generate")
async def generate_text(request: TextGenerationRequest) -> Dict[str, Any]:
    """
    文本生成
    
    Args:
        request: 文本生成请求
        
    Returns:
        生成结果
    """
    try:
        kwargs = {}
        if request.max_tokens:
            kwargs['max_tokens'] = request.max_tokens
        if request.temperature:
            kwargs['temperature'] = request.temperature
        
        result = await ai_engine_manager.generate_text(
            prompt=request.prompt,
            model_id=request.model_id,
            **kwargs
        )
        
        return {
            "status": "success",
            "data": {
                "result": result.result,
                "confidence": result.confidence,
                "processing_time": result.processing_time,
                "model_info": result.model_info,
                "success": result.success,
                "error_message": result.error_message
            }
        }
    except Exception as e:
        logger.error(f"文本生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文本生成失败: {str(e)}")

@router.post("/predict")
async def predict_value(request: PredictionRequest) -> Dict[str, Any]:
    """
    预测
    
    Args:
        request: 预测请求
        
    Returns:
        预测结果
    """
    try:
        result = await ai_engine_manager.predict_value(
            features=request.features,
            model_id=request.model_id
        )
        
        return {
            "status": "success",
            "data": {
                "result": result.result,
                "confidence": result.confidence,
                "processing_time": result.processing_time,
                "model_info": result.model_info,
                "success": result.success,
                "error_message": result.error_message
            }
        }
    except Exception as e:
        logger.error(f"预测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")

@router.post("/test/{engine_name}")
async def test_engine(engine_name: str) -> Dict[str, Any]:
    """
    测试特定AI引擎
    
    Args:
        engine_name: 引擎名称 (openai, gemini, huggingface, local)
        
    Returns:
        测试结果
    """
    try:
        test_text = "这是一个测试文本，用于验证AI引擎是否正常工作。"
        model_id = f"{engine_name}_text_analysis"
        
        result = await ai_engine_manager.analyze_text(
            text=test_text,
            task_type="text_analysis",
            model_id=model_id
        )
        
        return {
            "status": "success",
            "engine": engine_name,
            "test_input": test_text,
            "data": {
                "result": result.result,
                "confidence": result.confidence,
                "processing_time": result.processing_time,
                "model_info": result.model_info,
                "success": result.success,
                "error_message": result.error_message
            }
        }
    except Exception as e:
        logger.error(f"测试AI引擎失败: {engine_name}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试AI引擎失败: {str(e)}")

@router.get("/models/{model_id}/status")
async def get_model_status(model_id: str) -> Dict[str, Any]:
    """
    获取特定模型状态
    
    Args:
        model_id: 模型ID
        
    Returns:
        模型状态
    """
    try:
        status = ai_engine_manager.model_manager.get_model_status(model_id)
        return {
            "status": "success",
            "model_id": model_id,
            "data": status
        }
    except Exception as e:
        logger.error(f"获取模型状态失败: {model_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型状态失败: {str(e)}")

@router.get("/config")
async def get_ai_config() -> Dict[str, Any]:
    """
    获取AI引擎配置信息
    
    Returns:
        配置信息
    """
    try:
        config_info = {
            "default_engines": {
                "text_analysis": app_config.default_text_analysis_engine,
                "text_generation": app_config.default_text_generation_engine,
                "embedding": app_config.default_embedding_engine
            },
            "engine_configs": {
                "openai": {
                    "base_url": app_config.openai_base_url,
                    "model": app_config.openai_model,
                    "available": bool(app_config.openai_api_key)
                },
                "gemini": {
                    "base_url": app_config.gemini_base_url,
                    "model": app_config.gemini_model,
                    "available": bool(app_config.gemini_api_key)
                },
                "huggingface": {
                    "base_url": app_config.huggingface_base_url,
                    "model": app_config.huggingface_model,
                    "available": bool(app_config.huggingface_api_key)
                }
            }
        }
        
        return {
            "status": "success",
            "data": config_info
        }
    except Exception as e:
        logger.error(f"获取AI配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取AI配置失败: {str(e)}")

@router.post("/benchmark")
async def benchmark_engines() -> Dict[str, Any]:
    """
    对所有可用引擎进行基准测试
    
    Returns:
        基准测试结果
    """
    try:
        test_cases = [
            {
                "text": "我今天心情很好，天气也很棒！",
                "task_type": "sentiment_analysis",
                "expected": "positive"
            },
            {
                "text": "这个产品质量很差，我很失望。",
                "task_type": "sentiment_analysis", 
                "expected": "negative"
            },
            {
                "text": "今天是星期三。",
                "task_type": "sentiment_analysis",
                "expected": "neutral"
            }
        ]
        
        engines = ["openai", "gemini", "huggingface", "local"]
        results = {}
        
        for engine in engines:
            engine_results = []
            for test_case in test_cases:
                model_id = f"{engine}_sentiment"
                
                try:
                    result = await ai_engine_manager.analyze_text(
                        text=test_case["text"],
                        task_type=test_case["task_type"],
                        model_id=model_id
                    )
                    
                    engine_results.append({
                        "input": test_case["text"],
                        "expected": test_case["expected"],
                        "result": result.result,
                        "confidence": result.confidence,
                        "processing_time": result.processing_time,
                        "success": result.success,
                        "error": result.error_message
                    })
                except Exception as e:
                    engine_results.append({
                        "input": test_case["text"],
                        "expected": test_case["expected"],
                        "result": None,
                        "confidence": 0.0,
                        "processing_time": 0.0,
                        "success": False,
                        "error": str(e)
                    })
            
            results[engine] = engine_results
        
        return {
            "status": "success",
            "data": {
                "benchmark_results": results,
                "test_cases_count": len(test_cases),
                "engines_tested": len(engines)
            }
        }
    except Exception as e:
        logger.error(f"基准测试失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"基准测试失败: {str(e)}")
