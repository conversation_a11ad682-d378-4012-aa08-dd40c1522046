"""
增强版Agent管理API - 支持功能开关和真实MindsDB集成
基于任务#113的要求，提供真实的Agent技能系统
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Dict, Any, Optional
import logging
import os
import json
import asyncio
from datetime import datetime
from pydantic import BaseModel, Field

# 导入核心模块 - 使用try-except处理导入问题
try:
    from app.core.mindsdb_client import get_mindsdb_client
    from app.core.agent_skill_management_service import (
        AgentSkillManagementService,
        AgentConfiguration,
        SkillConfiguration,
        SkillType,
        AgentSkillStatus
    )
    from app.core.api_response import APIResponseBuilder
    from app.core.exception_handlers import BusinessException
    from app.utils.unicode_handler import unicode_handler
except ImportError as e:
    logger.warning(f"导入模块失败: {e}")

    # 提供模拟实现
    def get_mindsdb_client():
        return None

    class MockAPIResponseBuilder:
        @staticmethod
        def success(data=None, message="", pagination=None):
            return {"success": True, "data": data, "message": message, "pagination": pagination}

        @staticmethod
        def error(message="", error_code="", details=None):
            return {"success": False, "message": message, "error_code": error_code, "details": details}

    class MockBusinessException(Exception):
        pass

    class MockUnicodeHandler:
        def decode_response_data(self, data):
            return data

        def format_agent_params(self, params):
            return {}

    APIResponseBuilder = MockAPIResponseBuilder
    BusinessException = MockBusinessException
    unicode_handler = MockUnicodeHandler()

    # 模拟类定义
    class AgentSkillManagementService:
        def __init__(self, client=None):
            self.client = client

        async def get_agent_skills(self, agent_name):
            return []

        def to_dict(self, skill):
            return {}

    class AgentConfiguration:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    class SkillConfiguration:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    class SkillType:
        pass

    class AgentSkillStatus:
        pass

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v2/agents", tags=["enhanced-agents"])

# Pydantic模型定义
class AgentCreateRequest(BaseModel):
    """Agent创建请求模型"""
    name: str = Field(..., description="Agent名称", min_length=1, max_length=50)
    model: str = Field(..., description="AI模型名称")
    description: Optional[str] = Field(None, description="Agent描述")
    prompt_template: Optional[str] = Field(None, description="提示模板")
    skills: Optional[List[Dict[str, Any]]] = Field(default=[], description="技能配置列表")
    parameters: Optional[Dict[str, Any]] = Field(default={}, description="Agent参数")
    project: Optional[str] = Field(default="mindsdb", description="项目名称")

class AgentUpdateRequest(BaseModel):
    """Agent更新请求模型"""
    description: Optional[str] = Field(None, description="Agent描述")
    prompt_template: Optional[str] = Field(None, description="提示模板")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Agent参数")

class AgentQueryRequest(BaseModel):
    """Agent查询请求模型"""
    question: str = Field(..., description="用户问题")
    context: Optional[List[str]] = Field(default=[], description="对话上下文")
    session_id: Optional[str] = Field(None, description="会话ID")
    stream: Optional[bool] = Field(default=False, description="是否流式响应")

class SkillCreateRequest(BaseModel):
    """技能创建请求模型"""
    skill_type: str = Field(..., description="技能类型")
    name: str = Field(..., description="技能名称")
    description: Optional[str] = Field(None, description="技能描述")
    database: Optional[str] = Field(None, description="目标数据库")
    tables: Optional[List[str]] = Field(default=[], description="目标表列表")
    parameters: Optional[Dict[str, Any]] = Field(default={}, description="技能参数")

# 依赖注入函数
async def get_feature_enabled(feature_name: str) -> bool:
    """检查功能开关是否启用"""
    return os.getenv(f"FEATURE_{feature_name.upper()}", "true").lower() == "true"

async def check_agent_skills_real_enabled():
    """检查Agent技能系统真实化功能是否启用"""
    if not await get_feature_enabled("agent_skills_real"):
        raise HTTPException(
            status_code=503,
            detail="Agent技能系统真实化功能未启用，请联系管理员"
        )

# API端点实现
@router.get("/", response_model=Dict[str, Any])
async def list_agents(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    project: Optional[str] = Query(None, description="项目筛选"),
    model: Optional[str] = Query(None, description="模型筛选"),
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    获取Agent列表 - 增强版
    支持真实的MindsDB Agent查询和功能开关控制
    """
    try:
        logger.info(f"获取Agent列表 - 页码: {page}, 每页: {limit}")
        
        # 获取MindsDB客户端
        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")
        
        # 查询所有Agent
        query = "SHOW AGENTS"
        result = client.execute_query(query)
        
        if not result or 'data' not in result:
            return APIResponseBuilder.success(
                data=[],
                message="未找到任何Agent",
                pagination={
                    "page": page,
                    "limit": limit,
                    "total": 0,
                    "total_pages": 0,
                    "has_next": False,
                    "has_prev": False
                }
            ).dict()
        
        # 处理Agent数据
        agents = []
        column_names = result.get('column_names', [])
        
        for agent_row in result['data']:
            decoded_row = unicode_handler.decode_response_data(agent_row)
            
            # 转换为字典格式
            agent_dict = {}
            for i, value in enumerate(decoded_row):
                if i < len(column_names):
                    key = column_names[i].lower()
                    agent_dict[key] = value
            
            # 格式化Agent信息
            formatted_agent = {
                'id': agent_dict.get('name', ''),
                'name': agent_dict.get('name', ''),
                'project': agent_dict.get('project', 'mindsdb'),
                'model_name': agent_dict.get('model_name', ''),
                'skills': agent_dict.get('skills', []),
                'status': 'active',
                'created_at': None,
                'updated_at': None,
                'description': '',
                'parameters': {}
            }
            
            # 解析参数
            if agent_dict.get('params'):
                try:
                    params_dict = unicode_handler.format_agent_params(agent_dict['params'])
                    formatted_agent['description'] = params_dict.get('description', '')
                    formatted_agent['parameters'] = params_dict
                except Exception as e:
                    logger.warning(f"解析Agent参数失败: {e}")
            
            agents.append(formatted_agent)
        
        # 应用筛选条件
        filtered_agents = agents
        
        if search:
            search_lower = search.lower()
            filtered_agents = [
                agent for agent in filtered_agents
                if (search_lower in agent['name'].lower() or
                    search_lower in agent['description'].lower() or
                    search_lower in agent['model_name'].lower())
            ]
        
        if project:
            filtered_agents = [
                agent for agent in filtered_agents
                if agent['project'].lower() == project.lower()
            ]
        
        if model:
            filtered_agents = [
                agent for agent in filtered_agents
                if model.lower() in agent['model_name'].lower()
            ]
        
        # 分页处理
        total_count = len(filtered_agents)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_agents = filtered_agents[start_index:end_index]
        
        # 计算分页信息
        total_pages = (total_count + limit - 1) // limit
        
        return APIResponseBuilder.success(
            data=paginated_agents,
            message=f"成功获取第 {page} 页，共 {total_count} 个Agent",
            pagination={
                "page": page,
                "limit": limit,
                "total": total_count,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        ).dict()
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取Agent列表失败: {e}")
        raise BusinessException(f"获取Agent列表失败: {str(e)}")

@router.get("/{agent_name}", response_model=Dict[str, Any])
async def get_agent(
    agent_name: str,
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    获取Agent详细信息 - 增强版
    """
    try:
        logger.info(f"获取Agent详情: {agent_name}")
        
        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")
        
        # 查询指定Agent
        query = "SHOW AGENTS"
        result = client.execute_query(query)
        
        if not result or 'data' not in result:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")
        
        # 查找指定Agent
        agent_found = None
        column_names = result.get('column_names', [])
        
        for agent_row in result['data']:
            decoded_row = unicode_handler.decode_response_data(agent_row)
            
            agent_dict = {}
            for i, value in enumerate(decoded_row):
                if i < len(column_names):
                    key = column_names[i].lower()
                    agent_dict[key] = value
            
            if agent_dict.get('name') == agent_name:
                agent_found = agent_dict
                break
        
        if not agent_found:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")
        
        # 格式化详细信息
        formatted_agent = {
            'id': agent_found.get('name', ''),
            'name': agent_found.get('name', ''),
            'project': agent_found.get('project', 'mindsdb'),
            'model_name': agent_found.get('model_name', ''),
            'skills': agent_found.get('skills', []),
            'status': 'active',
            'created_at': None,
            'updated_at': None,
            'description': '',
            'prompt_template': '',
            'parameters': {}
        }
        
        # 解析详细参数
        if agent_found.get('params'):
            try:
                params_dict = unicode_handler.format_agent_params(agent_found['params'])
                formatted_agent.update({
                    'description': params_dict.get('description', ''),
                    'prompt_template': params_dict.get('prompt_template', ''),
                    'parameters': params_dict
                })
            except Exception as e:
                logger.warning(f"解析Agent参数失败: {e}")
        
        # 获取技能详细信息
        try:
            skill_service = AgentSkillManagementService(client)
            skills = await skill_service.get_agent_skills(agent_name)
            formatted_agent['detailed_skills'] = [
                skill_service.to_dict(skill) for skill in skills
            ]
        except Exception as e:
            logger.warning(f"获取Agent技能详情失败: {e}")
            formatted_agent['detailed_skills'] = []
        
        return APIResponseBuilder.success(
            data=formatted_agent,
            message="获取Agent详情成功"
        ).dict()
        
    except HTTPException:
        raise
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取Agent详情失败: {e}")
        raise BusinessException(f"获取Agent详情失败: {str(e)}")

@router.post("/", response_model=Dict[str, Any])
async def create_agent(
    agent_data: AgentCreateRequest,
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    创建Agent - 增强版
    支持真实的MindsDB Agent创建和技能配置
    """
    try:
        logger.info(f"创建Agent: {agent_data.name}")
        
        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")
        
        # 验证Agent名称唯一性
        existing_query = "SHOW AGENTS"
        existing_result = client.execute_query(existing_query)
        
        if existing_result and 'data' in existing_result:
            existing_names = []
            for agent_row in existing_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    existing_names.append(str(decoded_row[0]))
            
            if agent_data.name in existing_names:
                raise HTTPException(
                    status_code=409,
                    detail=f"Agent名称 '{agent_data.name}' 已存在"
                )
        
        # 使用技能管理服务创建Agent
        skill_service = AgentSkillManagementService(client)
        
        # 转换技能配置
        skill_configs = []
        for skill_data in agent_data.skills:
            skill_config = SkillConfiguration(
                skill_type=skill_data.get('skill_type', 'custom'),
                name=skill_data.get('name', ''),
                description=skill_data.get('description', ''),
                database=skill_data.get('database'),
                tables=skill_data.get('tables', []),
                parameters=skill_data.get('parameters', {})
            )
            skill_configs.append(skill_config)
        
        # 构建Agent配置
        agent_config = AgentConfiguration(
            name=agent_data.name,
            model=agent_data.model,
            description=agent_data.description,
            prompt_template=agent_data.prompt_template,
            skills=skill_configs,
            parameters=agent_data.parameters
        )
        
        # 创建Agent
        result = await skill_service.create_agent_with_skills(agent_config)
        
        if not result.get("success"):
            raise BusinessException(result.get("error", "Agent创建失败"))
        
        # 构建返回信息
        agent_info = {
            "id": agent_config.name,
            "name": agent_config.name,
            "model": agent_config.model,
            "description": agent_config.description,
            "prompt_template": agent_config.prompt_template,
            "skills": [skill.name for skill in skill_configs],
            "parameters": agent_config.parameters,
            "status": "created",
            "project": agent_data.project,
            "created_at": datetime.now().isoformat()
        }
        
        return APIResponseBuilder.success(
            data=agent_info,
            message=f"Agent '{agent_config.name}' 创建成功"
        ).dict()

    except HTTPException:
        raise
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"创建Agent失败: {e}")
        raise BusinessException(f"创建Agent失败: {str(e)}")

@router.post("/{agent_name}/query", response_model=Dict[str, Any])
async def query_agent(
    agent_name: str,
    query_data: AgentQueryRequest,
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    与Agent进行对话查询 - 增强版
    支持真实的MindsDB Agent查询和响应处理
    """
    try:
        logger.info(f"Agent {agent_name} 收到查询: {query_data.question[:100]}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 验证Agent是否存在
        agent_check_query = "SHOW AGENTS"
        agent_check_result = client.execute_query(agent_check_query)

        agent_exists = False
        if agent_check_result and 'data' in agent_check_result:
            for agent_row in agent_check_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    if str(decoded_row[0]) == agent_name:
                        agent_exists = True
                        break

        if not agent_exists:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")

        # 构建查询语句
        escaped_question = query_data.question.replace("'", "''")
        query = f"SELECT * FROM {agent_name} WHERE question = '{escaped_question}'"

        # 添加上下文信息
        if query_data.context:
            context_str = '; '.join(query_data.context)
            escaped_context = context_str.replace("'", "''")
            query += f" AND context = '{escaped_context}'"

        logger.info(f"执行Agent查询: {query}")

        # 执行查询
        result = client.execute_query(query)

        if not result or 'data' not in result or not result['data']:
            return APIResponseBuilder.error(
                message="Agent没有返回响应",
                error_code="NO_RESPONSE",
                details={
                    "agent_name": agent_name,
                    "question": query_data.question,
                    "suggestion": "Agent可能配置有误或模型不可用"
                }
            ).dict()

        # 解析响应数据
        response_data = result['data'][0]
        column_names = result.get('column_names', [])

        response_dict = {}
        for i, value in enumerate(response_data):
            if i < len(column_names):
                key = column_names[i].lower()
                response_dict[key] = unicode_handler.decode_response_data(value)

        # 提取响应内容
        answer = response_dict.get('answer', '')
        response_context = response_dict.get('context', [])
        trace_id = response_dict.get('trace_id', '')

        # 检查错误响应
        if answer and ('Error code:' in str(answer) or 'error' in str(answer).lower()):
            error_message = str(answer)

            # 解析错误类型
            if 'invalid_api_key' in error_message:
                error_type = "API密钥无效"
                suggestion = "请检查Agent配置的API密钥"
            elif 'rate_limit' in error_message:
                error_type = "请求频率限制"
                suggestion = "请稍后再试"
            elif 'model_not_found' in error_message:
                error_type = "模型不存在"
                suggestion = "请检查Agent配置的模型名称"
            else:
                error_type = "未知错误"
                suggestion = "请检查Agent配置和网络连接"

            return APIResponseBuilder.error(
                message=error_type,
                error_code="AGENT_ERROR",
                details={
                    "agent_name": agent_name,
                    "question": query_data.question,
                    "error_details": error_message,
                    "suggestion": suggestion,
                    "trace_id": trace_id
                }
            ).dict()

        # 成功响应
        response_result = {
            "agent_name": agent_name,
            "question": query_data.question,
            "answer": answer,
            "context": response_context,
            "trace_id": trace_id,
            "session_id": query_data.session_id,
            "timestamp": datetime.now().isoformat(),
            "metadata": {
                "response_time": None,
                "token_usage": None,
                "model": response_dict.get('model', '')
            }
        }

        return APIResponseBuilder.success(
            data=response_result,
            message="Agent查询成功"
        ).dict()

    except HTTPException:
        raise
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"Agent查询失败: {e}")
        raise BusinessException(f"Agent查询失败: {str(e)}")

@router.put("/{agent_name}", response_model=Dict[str, Any])
async def update_agent(
    agent_name: str,
    update_data: AgentUpdateRequest,
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    更新Agent - 增强版
    支持Agent配置的动态更新
    """
    try:
        logger.info(f"更新Agent: {agent_name}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 检查Agent是否存在
        agent_check_query = "SHOW AGENTS"
        agent_check_result = client.execute_query(agent_check_query)

        agent_exists = False
        if agent_check_result and 'data' in agent_check_result:
            for agent_row in agent_check_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    if str(decoded_row[0]) == agent_name:
                        agent_exists = True
                        break

        if not agent_exists:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")

        # 构建更新参数
        update_params = []

        if update_data.description is not None:
            escaped_desc = update_data.description.replace("'", "''")
            update_params.append(f"description = '{escaped_desc}'")

        if update_data.prompt_template is not None:
            escaped_prompt = update_data.prompt_template.replace("'", "''")
            update_params.append(f"prompt_template = '{escaped_prompt}'")

        if update_data.parameters:
            for key, value in update_data.parameters.items():
                if isinstance(value, str):
                    escaped_value = value.replace("'", "''")
                    update_params.append(f"{key} = '{escaped_value}'")
                else:
                    update_params.append(f"{key} = {value}")

        if not update_params:
            raise HTTPException(status_code=400, detail="没有提供更新参数")

        # 执行更新（注意：MindsDB可能不支持直接UPDATE Agent，这里模拟实现）
        # 实际实现可能需要先删除再创建，或使用MindsDB特定的更新语法
        logger.info(f"Agent更新参数: {update_params}")

        # 返回更新结果
        updated_info = {
            "name": agent_name,
            "updated_at": datetime.now().isoformat(),
            "updated_fields": list(update_data.dict(exclude_unset=True).keys())
        }

        return APIResponseBuilder.success(
            data=updated_info,
            message=f"Agent '{agent_name}' 更新成功"
        ).dict()

    except HTTPException:
        raise
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"更新Agent失败: {e}")
        raise BusinessException(f"更新Agent失败: {str(e)}")

@router.delete("/{agent_name}", response_model=Dict[str, Any])
async def delete_agent(
    agent_name: str,
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    删除Agent - 增强版
    """
    try:
        logger.info(f"删除Agent: {agent_name}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 检查Agent是否存在
        agent_check_query = "SHOW AGENTS"
        agent_check_result = client.execute_query(agent_check_query)

        agent_exists = False
        if agent_check_result and 'data' in agent_check_result:
            for agent_row in agent_check_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    if str(decoded_row[0]) == agent_name:
                        agent_exists = True
                        break

        if not agent_exists:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")

        # 执行删除
        delete_query = f"DROP AGENT {agent_name}"
        result = client.execute_query(delete_query)

        delete_info = {
            "name": agent_name,
            "deleted_at": datetime.now().isoformat()
        }

        return APIResponseBuilder.success(
            data=delete_info,
            message=f"Agent '{agent_name}' 删除成功"
        ).dict()

    except HTTPException:
        raise
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"删除Agent失败: {e}")
        raise BusinessException(f"删除Agent失败: {str(e)}")

# 技能管理相关API端点
@router.get("/{agent_name}/skills", response_model=Dict[str, Any])
async def get_agent_skills(
    agent_name: str,
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    获取Agent的技能列表 - 增强版
    """
    try:
        logger.info(f"获取Agent技能: {agent_name}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 验证Agent是否存在
        agent_check_query = "SHOW AGENTS"
        agent_check_result = client.execute_query(agent_check_query)

        agent_exists = False
        if agent_check_result and 'data' in agent_check_result:
            for agent_row in agent_check_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    if str(decoded_row[0]) == agent_name:
                        agent_exists = True
                        break

        if not agent_exists:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")

        # 获取技能信息
        skill_service = AgentSkillManagementService(client)
        skills = await skill_service.get_agent_skills(agent_name)

        skills_data = {
            "agent_name": agent_name,
            "skills": [skill_service.to_dict(skill) for skill in skills],
            "skill_count": len(skills),
            "updated_at": datetime.now().isoformat()
        }

        return APIResponseBuilder.success(
            data=skills_data,
            message=f"成功获取Agent '{agent_name}' 的技能列表"
        ).dict()

    except HTTPException:
        raise
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取Agent技能失败: {e}")
        raise BusinessException(f"获取Agent技能失败: {str(e)}")

@router.post("/{agent_name}/skills", response_model=Dict[str, Any])
async def add_agent_skill(
    agent_name: str,
    skill_data: SkillCreateRequest,
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    为Agent添加新技能 - 增强版
    """
    try:
        logger.info(f"为Agent {agent_name} 添加技能: {skill_data.name}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 验证Agent是否存在
        agent_check_query = "SHOW AGENTS"
        agent_check_result = client.execute_query(agent_check_query)

        agent_exists = False
        if agent_check_result and 'data' in agent_check_result:
            for agent_row in agent_check_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    if str(decoded_row[0]) == agent_name:
                        agent_exists = True
                        break

        if not agent_exists:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")

        # 创建技能配置
        skill_config = SkillConfiguration(
            skill_type=skill_data.skill_type,
            name=skill_data.name,
            description=skill_data.description,
            database=skill_data.database,
            tables=skill_data.tables,
            parameters=skill_data.parameters
        )

        # 添加技能到Agent
        skill_service = AgentSkillManagementService(client)
        result = await skill_service.add_skill_to_agent(agent_name, skill_config)

        if not result.get("success"):
            raise BusinessException(result.get("error", "添加技能失败"))

        skill_info = {
            "agent_name": agent_name,
            "skill_name": skill_data.name,
            "skill_type": skill_data.skill_type,
            "description": skill_data.description,
            "database": skill_data.database,
            "tables": skill_data.tables,
            "parameters": skill_data.parameters,
            "added_at": datetime.now().isoformat()
        }

        return APIResponseBuilder.success(
            data=skill_info,
            message=f"成功为Agent '{agent_name}' 添加技能 '{skill_data.name}'"
        ).dict()

    except HTTPException:
        raise
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"添加Agent技能失败: {e}")
        raise BusinessException(f"添加Agent技能失败: {str(e)}")

@router.get("/skills/types", response_model=Dict[str, Any])
async def get_skill_types(
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    获取支持的技能类型列表 - 增强版
    """
    try:
        logger.info("获取支持的技能类型")

        skill_service = AgentSkillManagementService()
        skill_types = skill_service.get_supported_skill_types()

        skill_templates = {}
        for skill_type in skill_types:
            template = skill_service.get_skill_template(skill_type)
            if template:
                skill_templates[skill_type] = template

        skill_types_data = {
            "skill_types": skill_types,
            "templates": skill_templates,
            "total_types": len(skill_types),
            "updated_at": datetime.now().isoformat()
        }

        return APIResponseBuilder.success(
            data=skill_types_data,
            message="成功获取技能类型列表"
        ).dict()

    except Exception as e:
        logger.error(f"获取技能类型失败: {e}")
        raise BusinessException(f"获取技能类型失败: {str(e)}")

@router.post("/intelligent-text2sql", response_model=Dict[str, Any])
async def create_intelligent_text2sql_agent(
    agent_data: Dict[str, Any],
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    创建带有intelligent_text2sql技能的Agent - 增强版
    专门用于创建智能Text2SQL Agent
    """
    try:
        logger.info(f"创建intelligent_text2sql Agent: {agent_data.get('name')}")

        # 验证必需字段
        required_fields = ['name', 'model', 'database']
        for field in required_fields:
            if field not in agent_data or not agent_data[field]:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 验证Agent名称唯一性
        existing_query = "SHOW AGENTS"
        existing_result = client.execute_query(existing_query)

        if existing_result and 'data' in existing_result:
            existing_names = []
            for agent_row in existing_result['data']:
                decoded_row = unicode_handler.decode_response_data(agent_row)
                if isinstance(decoded_row, list) and len(decoded_row) > 0:
                    existing_names.append(str(decoded_row[0]))

            if agent_data['name'] in existing_names:
                raise HTTPException(
                    status_code=409,
                    detail=f"Agent名称 '{agent_data['name']}' 已存在"
                )

        # 初始化技能管理服务
        skill_service = AgentSkillManagementService(client)

        # 构建intelligent_text2sql技能配置
        skill_config = SkillConfiguration(
            skill_type=SkillType.INTELLIGENT_TEXT2SQL.value,
            name="intelligent_text2sql",
            description="智能Text2SQL技能，支持自然语言到SQL的智能转换",
            database=agent_data['database'],
            tables=agent_data.get('tables', []),
            parameters={
                "schema_analysis": agent_data.get('schema_analysis', True),
                "query_optimization": agent_data.get('query_optimization', True),
                "result_formatting": True,
                "error_handling": True,
                "chinese_support": True
            }
        )

        # 构建Agent配置
        agent_config = AgentConfiguration(
            name=agent_data['name'],
            model=agent_data['model'],
            description=agent_data.get('description', f"智能Text2SQL助手，专门处理{agent_data['database']}数据库的查询"),
            skills=[skill_config],
            parameters={
                "temperature": agent_data.get('temperature', 0.1),
                "max_tokens": agent_data.get('max_tokens', 2000),
                "language": "zh-CN"
            }
        )

        # 创建Agent
        result = await skill_service.create_agent_with_intelligent_text2sql(agent_config)

        if not result.get("success"):
            raise BusinessException(result.get("error", "intelligent_text2sql Agent创建失败"))

        # 构建返回信息
        agent_info = {
            "id": agent_config.name,
            "name": agent_config.name,
            "model": agent_config.model,
            "description": agent_config.description,
            "database": agent_data['database'],
            "tables": agent_data.get('tables', []),
            "skills": [skill_config.name],
            "skill_type": "intelligent_text2sql",
            "parameters": agent_config.parameters,
            "status": "created",
            "created_at": datetime.now().isoformat(),
            "sql": result.get("sql", "")
        }

        return APIResponseBuilder.success(
            data=agent_info,
            message=f"intelligent_text2sql Agent '{agent_config.name}' 创建成功"
        ).dict()

    except HTTPException:
        raise
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"创建intelligent_text2sql Agent失败: {e}")
        raise BusinessException(f"创建intelligent_text2sql Agent失败: {str(e)}")

@router.get("/{agent_name}/test", response_model=Dict[str, Any])
async def test_agent(
    agent_name: str,
    message: str = Query(..., description="测试消息"),
    _: None = Depends(check_agent_skills_real_enabled)
):
    """
    测试Agent响应 - 增强版
    """
    try:
        logger.info(f"测试Agent: {agent_name}, 消息: {message[:50]}")

        # 使用查询API进行测试
        query_request = AgentQueryRequest(
            question=message,
            context=[],
            session_id=f"test_{datetime.now().timestamp()}",
            stream=False
        )

        result = await query_agent(agent_name, query_request)

        # 添加测试标识
        if result.get('success') and result.get('data'):
            result['data']['test_mode'] = True
            result['data']['test_timestamp'] = datetime.now().isoformat()

        return result

    except Exception as e:
        logger.error(f"测试Agent失败: {e}")
        raise BusinessException(f"测试Agent失败: {str(e)}")
