"""
Text2SQL权限管理API
提供Text2SQL Agent权限配置和管理的REST接口
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
import logging

try:
    from ..core.text2sql_permission_service import Text2SQLPermissionService, AgentPermissionProfile
    from ..core.sql_permission_validator import QueryComplexityLevel, PermissionLevel, ValidationResult
    from ..core.mindsdb_client import MindsDBClient
    from ..core.api_utils import create_success_response, create_error_response
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.text2sql_permission_service import Text2SQLPermissionService, AgentPermissionProfile
    from core.sql_permission_validator import QueryComplexityLevel, PermissionLevel, ValidationResult
    from core.mindsdb_client import MindsDBClient
    from core.api_utils import create_success_response, create_error_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/text2sql-permissions", tags=["Text2SQL权限管理"])

# Pydantic模型定义
class CreatePermissionProfileRequest(BaseModel):
    """创建权限配置文件请求"""
    agent_name: str = Field(..., description="Agent名称")
    database_name: str = Field(..., description="数据库名称")
    template_name: Optional[str] = Field(None, description="权限模板名称")
    allowed_tables: Optional[List[str]] = Field(default=[], description="允许访问的表")
    denied_tables: Optional[List[str]] = Field(default=[], description="禁止访问的表")
    permission_level: Optional[str] = Field("read_only", description="权限级别")
    max_complexity_level: Optional[str] = Field("moderate", description="最大复杂度级别")
    max_join_count: Optional[int] = Field(3, description="最大JOIN数量")
    max_subquery_depth: Optional[int] = Field(2, description="最大子查询深度")
    allow_aggregations: Optional[bool] = Field(True, description="是否允许聚合函数")
    allow_window_functions: Optional[bool] = Field(False, description="是否允许窗口函数")
    allow_stored_procedures: Optional[bool] = Field(False, description="是否允许存储过程")
    max_result_rows: Optional[int] = Field(10000, description="最大结果行数")
    created_by: Optional[str] = Field(None, description="创建者")
    description: Optional[str] = Field(None, description="描述")

class UpdatePermissionProfileRequest(BaseModel):
    """更新权限配置文件请求"""
    allowed_tables: Optional[List[str]] = Field(None, description="允许访问的表")
    denied_tables: Optional[List[str]] = Field(None, description="禁止访问的表")
    permission_level: Optional[str] = Field(None, description="权限级别")
    max_complexity_level: Optional[str] = Field(None, description="最大复杂度级别")
    max_join_count: Optional[int] = Field(None, description="最大JOIN数量")
    max_subquery_depth: Optional[int] = Field(None, description="最大子查询深度")
    allow_aggregations: Optional[bool] = Field(None, description="是否允许聚合函数")
    allow_window_functions: Optional[bool] = Field(None, description="是否允许窗口函数")
    allow_stored_procedures: Optional[bool] = Field(None, description="是否允许存储过程")
    max_result_rows: Optional[int] = Field(None, description="最大结果行数")
    description: Optional[str] = Field(None, description="描述")

class ValidateSQLRequest(BaseModel):
    """SQL验证请求"""
    agent_name: str = Field(..., description="Agent名称")
    sql_query: str = Field(..., description="SQL查询语句")

class PermissionRecommendationRequest(BaseModel):
    """权限建议请求"""
    agent_name: str = Field(..., description="Agent名称")
    database_name: str = Field(..., description="数据库名称")
    use_case: str = Field("data_analysis", description="使用场景")

# 依赖注入
def get_permission_service() -> Text2SQLPermissionService:
    """获取权限管理服务实例"""
    mindsdb_client = MindsDBClient()
    return Text2SQLPermissionService(mindsdb_client)

@router.post("/profiles")
async def create_permission_profile(
    request: CreatePermissionProfileRequest,
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """创建Agent权限配置文件"""
    try:
        logger.info(f"创建权限配置文件: {request.agent_name}")
        
        # 构建自定义配置
        custom_config = {}
        if request.permission_level:
            custom_config["permission_level"] = PermissionLevel(request.permission_level)
        if request.max_complexity_level:
            custom_config["max_complexity_level"] = QueryComplexityLevel(request.max_complexity_level)
        if request.max_join_count is not None:
            custom_config["max_join_count"] = request.max_join_count
        if request.max_subquery_depth is not None:
            custom_config["max_subquery_depth"] = request.max_subquery_depth
        if request.allow_aggregations is not None:
            custom_config["allow_aggregations"] = request.allow_aggregations
        if request.allow_window_functions is not None:
            custom_config["allow_window_functions"] = request.allow_window_functions
        if request.allow_stored_procedures is not None:
            custom_config["allow_stored_procedures"] = request.allow_stored_procedures
        if request.max_result_rows is not None:
            custom_config["max_result_rows"] = request.max_result_rows
        
        # 创建权限配置文件
        profile = service.create_agent_permission_profile(
            agent_name=request.agent_name,
            database_name=request.database_name,
            template_name=request.template_name,
            custom_config=custom_config,
            allowed_tables=request.allowed_tables,
            denied_tables=request.denied_tables,
            created_by=request.created_by,
            description=request.description
        )
        
        return create_success_response({
            "profile": profile.to_dict(),
            "message": f"权限配置文件创建成功: {request.agent_name}"
        })
        
    except Exception as e:
        logger.error(f"创建权限配置文件失败: {e}")
        return create_error_response(f"创建权限配置文件失败: {str(e)}", 500)

@router.get("/profiles/{agent_name}")
async def get_permission_profile(
    agent_name: str,
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """获取Agent权限配置文件"""
    try:
        profile = service.get_agent_permission_profile(agent_name)
        
        if not profile:
            return create_error_response(f"权限配置文件不存在: {agent_name}", 404)
        
        return create_success_response({
            "profile": profile.to_dict()
        })
        
    except Exception as e:
        logger.error(f"获取权限配置文件失败: {e}")
        return create_error_response(f"获取权限配置文件失败: {str(e)}", 500)

@router.put("/profiles/{agent_name}")
async def update_permission_profile(
    agent_name: str,
    request: UpdatePermissionProfileRequest,
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """更新Agent权限配置文件"""
    try:
        logger.info(f"更新权限配置文件: {agent_name}")
        
        # 构建更新数据
        updates = {}
        if request.allowed_tables is not None:
            updates["allowed_tables"] = request.allowed_tables
        if request.denied_tables is not None:
            updates["denied_tables"] = request.denied_tables
        if request.permission_level:
            updates["permission_level"] = request.permission_level
        if request.max_complexity_level:
            updates["max_complexity_level"] = request.max_complexity_level
        if request.max_join_count is not None:
            updates["max_join_count"] = request.max_join_count
        if request.max_subquery_depth is not None:
            updates["max_subquery_depth"] = request.max_subquery_depth
        if request.allow_aggregations is not None:
            updates["allow_aggregations"] = request.allow_aggregations
        if request.allow_window_functions is not None:
            updates["allow_window_functions"] = request.allow_window_functions
        if request.allow_stored_procedures is not None:
            updates["allow_stored_procedures"] = request.allow_stored_procedures
        if request.max_result_rows is not None:
            updates["max_result_rows"] = request.max_result_rows
        if request.description is not None:
            updates["description"] = request.description
        
        # 更新权限配置文件
        profile = service.update_agent_permission_profile(agent_name, updates)
        
        return create_success_response({
            "profile": profile.to_dict(),
            "message": f"权限配置文件更新成功: {agent_name}"
        })
        
    except ValueError as e:
        return create_error_response(str(e), 404)
    except Exception as e:
        logger.error(f"更新权限配置文件失败: {e}")
        return create_error_response(f"更新权限配置文件失败: {str(e)}", 500)

@router.delete("/profiles/{agent_name}")
async def delete_permission_profile(
    agent_name: str,
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """删除Agent权限配置文件"""
    try:
        success = service.delete_agent_permission_profile(agent_name)
        
        if success:
            return create_success_response({
                "message": f"权限配置文件删除成功: {agent_name}"
            })
        else:
            return create_error_response(f"权限配置文件不存在: {agent_name}", 404)
        
    except Exception as e:
        logger.error(f"删除权限配置文件失败: {e}")
        return create_error_response(f"删除权限配置文件失败: {str(e)}", 500)

@router.get("/profiles")
async def list_permission_profiles(
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """列出所有权限配置文件"""
    try:
        profiles = service.list_agent_permission_profiles()
        
        return create_success_response({
            "profiles": [profile.to_dict() for profile in profiles],
            "total": len(profiles)
        })
        
    except Exception as e:
        logger.error(f"列出权限配置文件失败: {e}")
        return create_error_response(f"列出权限配置文件失败: {str(e)}", 500)

@router.post("/validate-sql")
async def validate_sql_query(
    request: ValidateSQLRequest,
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """验证SQL查询权限"""
    try:
        logger.info(f"验证SQL查询权限: {request.agent_name}")
        
        result = service.validate_agent_sql_query(request.agent_name, request.sql_query)
        
        return create_success_response({
            "validation_result": {
                "is_valid": result.is_valid,
                "error_message": result.error_message,
                "warnings": result.warnings,
                "complexity_score": result.complexity_score,
                "detected_tables": result.detected_tables,
                "detected_operations": result.detected_operations
            }
        })
        
    except Exception as e:
        logger.error(f"验证SQL查询权限失败: {e}")
        return create_error_response(f"验证SQL查询权限失败: {str(e)}", 500)

@router.get("/templates")
async def get_permission_templates(
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """获取权限模板列表"""
    try:
        templates = service.get_permission_templates()
        
        return create_success_response({
            "templates": templates
        })
        
    except Exception as e:
        logger.error(f"获取权限模板失败: {e}")
        return create_error_response(f"获取权限模板失败: {str(e)}", 500)

@router.post("/recommendations")
async def get_permission_recommendations(
    request: PermissionRecommendationRequest,
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """获取权限配置建议"""
    try:
        logger.info(f"获取权限配置建议: {request.agent_name}")
        
        recommendations = service.generate_permission_recommendations(
            agent_name=request.agent_name,
            database_name=request.database_name,
            use_case=request.use_case
        )
        
        return create_success_response({
            "recommendations": recommendations
        })
        
    except Exception as e:
        logger.error(f"获取权限配置建议失败: {e}")
        return create_error_response(f"获取权限配置建议失败: {str(e)}", 500)

@router.get("/database/{database_name}/tables")
async def analyze_database_tables(
    database_name: str,
    service: Text2SQLPermissionService = Depends(get_permission_service)
):
    """分析数据库表结构"""
    try:
        logger.info(f"分析数据库表结构: {database_name}")
        
        tables_info = service.analyze_database_tables(database_name)
        
        return create_success_response({
            "database_name": database_name,
            "tables": tables_info,
            "total_tables": len(tables_info)
        })
        
    except Exception as e:
        logger.error(f"分析数据库表结构失败: {e}")
        return create_error_response(f"分析数据库表结构失败: {str(e)}", 500)

@router.get("/complexity-levels")
async def get_complexity_levels():
    """获取查询复杂度级别列表"""
    try:
        levels = {
            level.value: {
                "name": level.value,
                "description": {
                    "simple": "简单查询：单表，基本WHERE条件",
                    "moderate": "中等查询：多表JOIN，聚合函数",
                    "complex": "复杂查询：子查询，窗口函数，复杂JOIN",
                    "unlimited": "无限制：允许所有类型的查询"
                }.get(level.value, "")
            }
            for level in QueryComplexityLevel
        }
        
        return create_success_response({
            "complexity_levels": levels
        })
        
    except Exception as e:
        logger.error(f"获取复杂度级别失败: {e}")
        return create_error_response(f"获取复杂度级别失败: {str(e)}", 500)

@router.get("/permission-levels")
async def get_permission_levels():
    """获取权限级别列表"""
    try:
        levels = {
            level.value: {
                "name": level.value,
                "description": {
                    "read_only": "只读权限：仅允许SELECT查询",
                    "read_write": "读写权限：允许SELECT、INSERT、UPDATE、DELETE",
                    "admin": "管理员权限：允许所有操作"
                }.get(level.value, "")
            }
            for level in PermissionLevel
        }
        
        return create_success_response({
            "permission_levels": levels
        })
        
    except Exception as e:
        logger.error(f"获取权限级别失败: {e}")
        return create_error_response(f"获取权限级别失败: {str(e)}", 500)
