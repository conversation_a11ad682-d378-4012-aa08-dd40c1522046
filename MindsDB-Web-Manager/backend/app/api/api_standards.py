"""
API标准化测试端点
用于测试和演示统一的API响应格式和错误处理
"""
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import logging
import time
import asyncio

from core.api_response import APIResponseBuilder, ResponseCode
from core.exception_handlers import (
    BusinessException, MindsDBException, AIEngineException,
    SQLExecutionException, AgentException, PermissionException
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/standards", tags=["api-standards"])

class TestRequest(BaseModel):
    """测试请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="测试名称")
    value: int = Field(..., ge=0, le=1000, description="测试值")
    optional_field: Optional[str] = Field(None, description="可选字段")

class TestResponse(BaseModel):
    """测试响应模型"""
    processed_name: str
    doubled_value: int
    timestamp: float

@router.get("/success")
async def test_success_response():
    """测试成功响应格式"""
    data = {
        "message": "这是一个成功的响应示例",
        "timestamp": time.time(),
        "server_info": {
            "version": "1.0.0",
            "environment": "development"
        }
    }
    
    return APIResponseBuilder.success(
        data=data,
        message="操作成功完成"
    ).dict()

@router.get("/partial-success")
async def test_partial_success_response():
    """测试部分成功响应格式"""
    data = {
        "successful_items": ["item1", "item2"],
        "failed_items": ["item3"],
        "total_processed": 3,
        "success_rate": 0.67
    }
    
    details = {
        "failures": [
            {"item": "item3", "reason": "数据格式错误"}
        ]
    }
    
    return APIResponseBuilder.partial_success(
        data=data,
        message="部分操作成功",
        details=details
    ).dict()

@router.get("/paginated")
async def test_paginated_response(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页大小")
):
    """测试分页响应格式"""
    # 模拟数据
    total_items = 95
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total_items)
    
    items = [
        {
            "id": i,
            "name": f"Item {i}",
            "value": i * 10,
            "created_at": time.time() - (i * 3600)
        }
        for i in range(start_idx + 1, end_idx + 1)
    ]
    
    return APIResponseBuilder.paginated_success(
        items=items,
        total=total_items,
        page=page,
        page_size=page_size,
        message="分页数据获取成功"
    ).dict()

@router.post("/validation")
async def test_validation(request: TestRequest):
    """测试数据验证和成功处理"""
    # 处理数据
    processed_data = TestResponse(
        processed_name=request.name.upper(),
        doubled_value=request.value * 2,
        timestamp=time.time()
    )
    
    return APIResponseBuilder.success(
        data=processed_data.dict(),
        message="数据验证和处理成功"
    ).dict()

@router.get("/error/business")
async def test_business_error():
    """测试业务逻辑错误"""
    raise BusinessException(
        message="这是一个业务逻辑错误示例",
        details={"error_type": "business_rule_violation", "rule_id": "BR001"}
    )

@router.get("/error/mindsdb")
async def test_mindsdb_error():
    """测试MindsDB连接错误"""
    raise MindsDBException(
        message="MindsDB服务连接失败",
        details={"connection_url": "localhost:47334", "timeout": 30}
    )

@router.get("/error/ai-engine")
async def test_ai_engine_error():
    """测试AI引擎错误"""
    raise AIEngineException(
        message="AI引擎处理失败",
        details={"engine": "gemini", "model": "gemini-2.0-flash", "error_code": "QUOTA_EXCEEDED"}
    )

@router.get("/error/sql")
async def test_sql_error():
    """测试SQL执行错误"""
    raise SQLExecutionException(
        message="SQL查询执行失败",
        details={"query": "SELECT * FROM invalid_table", "error": "Table 'invalid_table' doesn't exist"}
    )

@router.get("/error/agent")
async def test_agent_error():
    """测试Agent错误"""
    raise AgentException(
        message="Agent执行失败",
        details={"agent_id": "agent_001", "task": "text_analysis", "error": "模型加载失败"}
    )

@router.get("/error/permission")
async def test_permission_error():
    """测试权限错误"""
    raise PermissionException(
        message="权限不足，无法访问该资源",
        details={"required_permission": "admin", "user_permission": "user", "resource": "/api/admin/users"}
    )

@router.get("/error/http")
async def test_http_error():
    """测试HTTP异常"""
    raise HTTPException(
        status_code=404,
        detail="请求的资源未找到"
    )

@router.get("/error/validation")
async def test_validation_error(invalid_param: int = Query(..., ge=100, description="必须大于等于100")):
    """测试验证错误（通过查询参数）"""
    return APIResponseBuilder.success(
        data={"param": invalid_param},
        message="验证通过"
    ).dict()

@router.get("/error/internal")
async def test_internal_error():
    """测试内部服务器错误"""
    # 故意引发一个未处理的异常
    result = 1 / 0  # ZeroDivisionError
    return {"result": result}

@router.get("/error/timeout")
async def test_timeout_error():
    """测试超时错误"""
    # 模拟超时
    await asyncio.sleep(10)  # 假设有超时中间件会在5秒后中断
    return APIResponseBuilder.success(message="不应该到达这里").dict()

@router.get("/performance/slow")
async def test_slow_request():
    """测试慢请求（用于性能监控）"""
    # 模拟慢请求
    await asyncio.sleep(3)
    
    return APIResponseBuilder.success(
        data={"processing_time": "3 seconds"},
        message="慢请求处理完成"
    ).dict()

@router.get("/response-codes")
async def get_response_codes():
    """获取所有响应码说明"""
    codes = {}
    
    for code in ResponseCode:
        codes[code.value] = {
            "name": code.name,
            "description": code.name.replace("_", " ").title()
        }
    
    return APIResponseBuilder.success(
        data={"response_codes": codes},
        message="响应码列表获取成功"
    ).dict()

@router.get("/health")
async def api_health_check():
    """API健康检查"""
    health_data = {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0",
        "checks": {
            "api_server": "ok",
            "database": "ok",
            "external_services": "ok"
        }
    }
    
    return APIResponseBuilder.success(
        data=health_data,
        message="API服务健康"
    ).dict()

@router.get("/examples")
async def get_api_examples():
    """获取API使用示例"""
    examples = {
        "success_response": {
            "code": 0,
            "message": "操作成功",
            "data": {"key": "value"},
            "details": None,
            "timestamp": **********.123456,
            "request_id": "uuid-string"
        },
        "error_response": {
            "code": 1000,
            "message": "请求参数错误",
            "data": None,
            "details": {"field": "validation error details"},
            "timestamp": **********.123456,
            "request_id": "uuid-string"
        },
        "paginated_response": {
            "code": 0,
            "message": "查询成功",
            "data": {
                "items": [{"id": 1, "name": "item1"}],
                "total": 100,
                "page": 1,
                "page_size": 10,
                "total_pages": 10,
                "has_next": True,
                "has_prev": False
            },
            "timestamp": **********.123456,
            "request_id": "uuid-string"
        }
    }
    
    return APIResponseBuilder.success(
        data=examples,
        message="API示例获取成功"
    ).dict()
