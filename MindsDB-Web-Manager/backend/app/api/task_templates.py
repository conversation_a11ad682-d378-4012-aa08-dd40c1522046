"""
任务模板CRUD API
提供任务模板的创建、读取、更新、删除和实例化功能
"""

import logging
from typing import Dict, List, Any, Optional
from flask import Blueprint, request, jsonify
from functools import wraps

from app.core.template_manager import get_template_manager
from app.models.task_template import TemplateCategory, ParameterType

logger = logging.getLogger(__name__)

# 创建蓝图
task_templates_bp = Blueprint('task_templates', __name__, url_prefix='/api/task-templates')

def handle_api_error(f):
    """API错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"API参数错误: {e}")
            return jsonify({
                'success': False,
                'error': 'VALIDATION_ERROR',
                'message': str(e)
            }), 400
        except Exception as e:
            logger.error(f"API内部错误: {e}")
            return jsonify({
                'success': False,
                'error': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }), 500
    return decorated_function

def validate_template_data(data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
    """验证模板数据"""
    if not is_update:
        # 创建时的必需字段
        required_fields = ['name', 'task_config']
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValueError(f"缺少必需字段: {field}")
    
    # 验证分类
    if 'category' in data:
        valid_categories = [cat.value for cat in TemplateCategory]
        if data['category'] not in valid_categories:
            raise ValueError(f"无效的模板分类: {data['category']}")
    
    # 验证任务配置
    if 'task_config' in data:
        task_config = data['task_config']
        if not isinstance(task_config, dict):
            raise ValueError("task_config必须是字典类型")
        
        if 'task_name' not in task_config:
            raise ValueError("task_config中缺少task_name字段")
    
    # 验证参数定义
    if 'parameters' in data:
        parameters = data['parameters']
        if not isinstance(parameters, list):
            raise ValueError("parameters必须是数组类型")
        
        param_names = set()
        valid_types = [ptype.value for ptype in ParameterType]
        
        for i, param in enumerate(parameters):
            if not isinstance(param, dict):
                raise ValueError(f"参数定义 {i} 必须是字典类型")
            
            if 'name' not in param:
                raise ValueError(f"参数定义 {i} 缺少name字段")
            
            param_name = param['name']
            if param_name in param_names:
                raise ValueError(f"重复的参数名称: {param_name}")
            param_names.add(param_name)
            
            if 'type' not in param:
                raise ValueError(f"参数 {param_name} 缺少type字段")
            
            if param['type'] not in valid_types:
                raise ValueError(f"参数 {param_name} 的类型无效: {param['type']}")
    
    return data

@task_templates_bp.route('/', methods=['GET'])
@handle_api_error
def list_templates():
    """获取模板列表"""
    # 获取查询参数
    category = request.args.get('category')
    include_system = request.args.get('include_system', 'true').lower() == 'true'
    active_only = request.args.get('active_only', 'true').lower() == 'true'
    limit = request.args.get('limit', 50, type=int)
    offset = request.args.get('offset', 0, type=int)
    
    manager = get_template_manager()
    templates = manager.list_templates(
        category=category,
        include_system=include_system,
        active_only=active_only
    )
    
    # 分页
    total = len(templates)
    templates = templates[offset:offset + limit]
    
    return jsonify({
        'success': True,
        'data': {
            'templates': templates,
            'pagination': {
                'total': total,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total
            }
        }
    })

@task_templates_bp.route('/', methods=['POST'])
@handle_api_error
def create_template():
    """创建模板"""
    data = request.get_json()
    if not data:
        raise ValueError("请求体不能为空")
    
    # 验证数据
    validated_data = validate_template_data(data)
    
    # 设置默认值
    validated_data.setdefault('description', '')
    validated_data.setdefault('category', TemplateCategory.CUSTOM.value)
    validated_data.setdefault('parameters', [])
    validated_data.setdefault('version', '1.0.0')
    validated_data.setdefault('is_system', False)
    validated_data.setdefault('created_by', 'api_user')
    
    manager = get_template_manager()
    template = manager.create_template(validated_data)
    
    if not template:
        raise ValueError("模板创建失败")
    
    return jsonify({
        'success': True,
        'data': {
            'template': template.to_dict(),
            'message': '模板创建成功'
        }
    }), 201

@task_templates_bp.route('/<int:template_id>', methods=['GET'])
@handle_api_error
def get_template(template_id: int):
    """获取单个模板"""
    manager = get_template_manager()
    template = manager.get_template(template_id)
    
    if not template:
        return jsonify({
            'success': False,
            'error': 'NOT_FOUND',
            'message': '模板不存在'
        }), 404
    
    return jsonify({
        'success': True,
        'data': {
            'template': template.to_dict()
        }
    })

@task_templates_bp.route('/<int:template_id>', methods=['PUT'])
@handle_api_error
def update_template(template_id: int):
    """更新模板"""
    data = request.get_json()
    if not data:
        raise ValueError("请求体不能为空")
    
    # 验证数据
    validated_data = validate_template_data(data, is_update=True)
    
    manager = get_template_manager()
    template = manager.update_template(template_id, validated_data)
    
    if not template:
        return jsonify({
            'success': False,
            'error': 'NOT_FOUND',
            'message': '模板不存在'
        }), 404
    
    return jsonify({
        'success': True,
        'data': {
            'template': template.to_dict(),
            'message': '模板更新成功'
        }
    })

@task_templates_bp.route('/<int:template_id>', methods=['DELETE'])
@handle_api_error
def delete_template(template_id: int):
    """删除模板"""
    force = request.args.get('force', 'false').lower() == 'true'
    
    manager = get_template_manager()
    success = manager.delete_template(template_id, force=force)
    
    if not success:
        return jsonify({
            'success': False,
            'error': 'NOT_FOUND',
            'message': '模板不存在或无法删除'
        }), 404
    
    return jsonify({
        'success': True,
        'data': {
            'message': '模板删除成功'
        }
    })

@task_templates_bp.route('/<int:template_id>/clone', methods=['POST'])
@handle_api_error
def clone_template(template_id: int):
    """克隆模板"""
    data = request.get_json() or {}
    
    new_name = data.get('name')
    if not new_name:
        raise ValueError("缺少新模板名称")
    
    new_version = data.get('version', '1.0.0')
    
    manager = get_template_manager()
    cloned_template = manager.clone_template(template_id, new_name, new_version)
    
    if not cloned_template:
        return jsonify({
            'success': False,
            'error': 'NOT_FOUND',
            'message': '源模板不存在'
        }), 404
    
    return jsonify({
        'success': True,
        'data': {
            'template': cloned_template.to_dict(),
            'message': '模板克隆成功'
        }
    }), 201

@task_templates_bp.route('/<int:template_id>/validate', methods=['POST'])
@handle_api_error
def validate_template_config(template_id: int):
    """验证模板配置"""
    data = request.get_json()
    if not data:
        raise ValueError("请求体不能为空")
    
    task_config = data.get('task_config')
    parameters = data.get('parameters', [])
    
    if not task_config:
        raise ValueError("缺少task_config字段")
    
    manager = get_template_manager()
    is_valid, errors = manager.validate_template_config(task_config, parameters)
    
    return jsonify({
        'success': True,
        'data': {
            'valid': is_valid,
            'errors': errors
        }
    })

@task_templates_bp.route('/<int:template_id>/instances', methods=['POST'])
@handle_api_error
def create_instance(template_id: int):
    """创建模板实例"""
    data = request.get_json()
    if not data:
        raise ValueError("请求体不能为空")
    
    param_values = data.get('parameters', {})
    instance_name = data.get('name')
    
    if not isinstance(param_values, dict):
        raise ValueError("parameters必须是字典类型")
    
    manager = get_template_manager()
    instance = manager.create_instance(template_id, param_values, instance_name)
    
    if not instance:
        return jsonify({
            'success': False,
            'error': 'NOT_FOUND',
            'message': '模板不存在或实例创建失败'
        }), 404
    
    return jsonify({
        'success': True,
        'data': {
            'instance': instance.to_dict(),
            'message': '模板实例创建成功'
        }
    }), 201

@task_templates_bp.route('/<int:template_id>/instances', methods=['GET'])
@handle_api_error
def list_instances(template_id: int):
    """获取模板实例列表"""
    manager = get_template_manager()
    instances = manager.list_instances(template_id=template_id)
    
    return jsonify({
        'success': True,
        'data': {
            'instances': instances
        }
    })

@task_templates_bp.route('/instances/<int:instance_id>', methods=['GET'])
@handle_api_error
def get_instance(instance_id: int):
    """获取模板实例"""
    manager = get_template_manager()
    instance = manager.get_instance(instance_id)
    
    if not instance:
        return jsonify({
            'success': False,
            'error': 'NOT_FOUND',
            'message': '实例不存在'
        }), 404
    
    return jsonify({
        'success': True,
        'data': {
            'instance': instance.to_dict()
        }
    })

@task_templates_bp.route('/categories', methods=['GET'])
@handle_api_error
def get_categories():
    """获取模板分类"""
    categories = [
        {
            'value': cat.value,
            'label': cat.value.replace('_', ' ').title()
        }
        for cat in TemplateCategory
    ]
    
    return jsonify({
        'success': True,
        'data': {
            'categories': categories
        }
    })

@task_templates_bp.route('/parameter-types', methods=['GET'])
@handle_api_error
def get_parameter_types():
    """获取参数类型"""
    types = [
        {
            'value': ptype.value,
            'label': ptype.value.title()
        }
        for ptype in ParameterType
    ]
    
    return jsonify({
        'success': True,
        'data': {
            'types': types
        }
    })

@task_templates_bp.route('/statistics', methods=['GET'])
@handle_api_error
def get_statistics():
    """获取模板统计信息"""
    manager = get_template_manager()
    stats = manager.get_template_statistics()
    
    return jsonify({
        'success': True,
        'data': stats
    })

@task_templates_bp.route('/render', methods=['POST'])
@handle_api_error
def render_template():
    """渲染模板配置（不创建实例）"""
    data = request.get_json()
    if not data:
        raise ValueError("请求体不能为空")
    
    template_id = data.get('template_id')
    param_values = data.get('parameters', {})
    
    if not template_id:
        raise ValueError("缺少template_id字段")
    
    if not isinstance(param_values, dict):
        raise ValueError("parameters必须是字典类型")
    
    manager = get_template_manager()
    template = manager.get_template(template_id)
    
    if not template:
        return jsonify({
            'success': False,
            'error': 'NOT_FOUND',
            'message': '模板不存在'
        }), 404
    
    try:
        rendered_config = template.render_config(param_values)
        
        return jsonify({
            'success': True,
            'data': {
                'rendered_config': rendered_config,
                'parameter_values': param_values
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'RENDER_ERROR',
            'message': str(e)
        }), 400

@task_templates_bp.route('/batch', methods=['POST'])
@handle_api_error
def batch_operations():
    """批量操作"""
    data = request.get_json()
    if not data:
        raise ValueError("请求体不能为空")
    
    operation = data.get('operation')
    template_ids = data.get('template_ids', [])
    
    if not operation:
        raise ValueError("缺少operation字段")
    
    if not isinstance(template_ids, list) or not template_ids:
        raise ValueError("template_ids必须是非空数组")
    
    manager = get_template_manager()
    results = []
    
    for template_id in template_ids:
        try:
            if operation == 'delete':
                force = data.get('force', False)
                success = manager.delete_template(template_id, force=force)
            elif operation == 'activate':
                success = manager.update_template(template_id, {'is_active': True}) is not None
            elif operation == 'deactivate':
                success = manager.update_template(template_id, {'is_active': False}) is not None
            else:
                raise ValueError(f"不支持的操作: {operation}")
            
            results.append({
                'template_id': template_id,
                'success': success,
                'message': '操作成功' if success else '操作失败'
            })
            
        except Exception as e:
            results.append({
                'template_id': template_id,
                'success': False,
                'message': str(e)
            })
    
    # 统计结果
    successful_count = sum(1 for r in results if r['success'])
    
    return jsonify({
        'success': True,
        'data': {
            'results': results,
            'summary': {
                'total': len(template_ids),
                'successful': successful_count,
                'failed': len(template_ids) - successful_count
            }
        }
    })

# 错误处理
@task_templates_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'NOT_FOUND',
        'message': '资源不存在'
    }), 404

@task_templates_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'error': 'METHOD_NOT_ALLOWED',
        'message': '方法不允许'
    }), 405

@task_templates_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'INTERNAL_ERROR',
        'message': '服务器内部错误'
    }), 500
