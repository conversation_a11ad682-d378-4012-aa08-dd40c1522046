"""
异步任务管理API
提供任务创建、查询、管理的REST接口
"""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

try:
    # 尝试使用Celery版本
    from core.async_tasks.task_manager import (
        AsyncTaskManager,
        TaskStatus,
        TaskPriority,
        TaskInfo
    )
    from core.async_tasks.celery_config import celery_app
    CELERY_AVAILABLE = True
except ImportError:
    # 使用简化版本
    from core.async_tasks.simple_task_manager import (
        get_task_manager,
        TaskStatus,
        TaskPriority,
        TaskInfo
    )
    CELERY_AVAILABLE = False
from core.api_response import APIResponseBuilder
from core.exception_handlers import BusinessException

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/async-tasks", tags=["异步任务"])

# 获取任务管理器实例
def get_task_manager_instance():
    """获取任务管理器实例"""
    if CELERY_AVAILABLE:
        return AsyncTaskManager()
    else:
        return get_task_manager()

# 任务管理器适配器
class TaskManagerAdapter:
    """任务管理器适配器，统一不同实现的接口"""

    def __init__(self):
        self.manager = get_task_manager_instance()
        self.is_simple = not CELERY_AVAILABLE

    def create_task(self, task_type: str, task_function: str, args=None, kwargs=None,
                   priority=TaskPriority.MEDIUM, callback_url=None, metadata=None):
        """创建任务"""
        if self.is_simple:
            # 简化版本：直接提交函数执行
            def dummy_task():
                return {"message": f"模拟执行 {task_function}", "args": args, "kwargs": kwargs}

            return self.manager.submit_task(
                dummy_task,
                name=f"{task_type}_{task_function}",
                priority=priority,
                metadata=metadata or {}
            )
        else:
            # Celery版本
            return self.manager.create_task(
                task_type=task_type,
                task_function=task_function,
                args=args or [],
                kwargs=kwargs or {},
                priority=priority,
                callback_url=callback_url,
                metadata=metadata or {}
            )

    def get_task_info(self, task_id: str):
        """获取任务信息"""
        return self.manager.get_task_info(task_id)

    def list_tasks(self, status=None, task_type=None, limit=100):
        """列出任务"""
        if self.is_simple:
            return self.manager.list_tasks(status=status, limit=limit)
        else:
            return self.manager.list_tasks(status=status, task_type=task_type, limit=limit)

    def cancel_task(self, task_id: str):
        """取消任务"""
        return self.manager.cancel_task(task_id)

    def update_task_progress(self, task_id: str, current: int, total=None, message="", details=None):
        """更新任务进度"""
        if self.is_simple:
            # 简化版本暂不支持进度更新
            return True
        else:
            return self.manager.update_task_progress(task_id, current, total, message, details)

    def retry_task(self, task_id: str):
        """重试任务"""
        if self.is_simple:
            # 简化版本暂不支持重试
            return False
        else:
            return self.manager.retry_task(task_id)

    def cleanup_old_tasks(self, days: int):
        """清理旧任务"""
        if self.is_simple:
            # 简化版本有自动清理机制
            return True
        else:
            return self.manager.cleanup_old_tasks(days)

    def get_statistics(self):
        """获取统计信息"""
        if self.is_simple:
            return self.manager.get_statistics()
        else:
            # 为Celery版本构建统计信息
            all_tasks = self.manager.list_tasks(limit=1000)
            return {
                'total': len(all_tasks),
                'pending': len([t for t in all_tasks if t.status == TaskStatus.PENDING]),
                'running': len([t for t in all_tasks if t.status == TaskStatus.RUNNING]),
                'success': len([t for t in all_tasks if t.status == TaskStatus.SUCCESS]),
                'failed': len([t for t in all_tasks if t.status == TaskStatus.FAILED])
            }

# Pydantic模型定义
class CreateTaskRequest(BaseModel):
    """创建任务请求"""
    task_type: str = Field(..., description="任务类型")
    task_function: str = Field(..., description="任务函数名")
    args: List[Any] = Field(default=[], description="位置参数")
    kwargs: Dict[str, Any] = Field(default={}, description="关键字参数")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="任务优先级")
    callback_url: Optional[str] = Field(None, description="回调URL")
    metadata: Dict[str, Any] = Field(default={}, description="元数据")

class ModelTrainingRequest(BaseModel):
    """模型训练请求"""
    model_name: str = Field(..., description="模型名称")
    query: str = Field(..., description="训练查询SQL")
    database_name: str = Field(default="mindsdb", description="数据库名称")
    training_options: Dict[str, Any] = Field(default={}, description="训练选项")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="任务优先级")
    callback_url: Optional[str] = Field(None, description="回调URL")

class ModelPredictionRequest(BaseModel):
    """模型预测请求"""
    model_name: str = Field(..., description="模型名称")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    prediction_options: Dict[str, Any] = Field(default={}, description="预测选项")
    priority: TaskPriority = Field(default=TaskPriority.HIGH, description="任务优先级")
    callback_url: Optional[str] = Field(None, description="回调URL")

class TaskProgressUpdate(BaseModel):
    """任务进度更新"""
    current: int = Field(..., description="当前进度")
    total: Optional[int] = Field(None, description="总进度")
    message: str = Field(default="", description="进度消息")
    details: Dict[str, Any] = Field(default={}, description="详细信息")

class TaskResponse(BaseModel):
    """任务响应"""
    task_id: str
    task_type: str
    status: str
    priority: str
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: Dict[str, Any]
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    retry_count: int
    metadata: Dict[str, Any]

@router.get("/health")
async def get_health():
    """获取异步任务系统健康状态"""
    try:
        # 检查Celery连接
        celery_status = "healthy"
        try:
            # 尝试获取Celery状态
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            if not stats:
                celery_status = "unhealthy"
        except Exception:
            celery_status = "unhealthy"

        task_manager = TaskManagerAdapter()

        # 获取任务统计
        task_stats = task_manager.get_statistics()
        
        return create_success_response({
            'celery_health': health_status,
            'task_statistics': task_stats,
            'system_status': 'healthy' if health_status['status'] == 'healthy' else 'degraded'
        })
        
    except Exception as e:
        logger.error(f"获取健康状态失败: {e}")
        return create_error_response(f"获取健康状态失败: {str(e)}", 500)

@router.post("/tasks")
async def create_task(request: CreateTaskRequest):
    """创建通用异步任务"""
    try:
        task_manager = TaskManagerAdapter()
        
        task_id = task_manager.create_task(
            task_type=request.task_type,
            task_function=request.task_function,
            args=request.args,
            kwargs=request.kwargs,
            priority=request.priority,
            callback_url=request.callback_url,
            metadata=request.metadata
        )
        
        return create_success_response({
            'task_id': task_id,
            'message': '任务已创建'
        })
        
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        return create_error_response(f"创建任务失败: {str(e)}", 500)

@router.post("/model-training")
async def create_model_training_task(request: ModelTrainingRequest):
    """创建模型训练任务"""
    try:
        task_manager = TaskManagerAdapter()
        
        task_id = task_manager.create_task(
            task_type="MODEL_TRAINING",
            task_function="app.core.async_tasks.model_training_tasks.train_model_task",
            kwargs={
                'model_name': request.model_name,
                'query': request.query,
                'database_name': request.database_name,
                'training_options': request.training_options
            },
            priority=request.priority,
            callback_url=request.callback_url,
            metadata={
                'model_name': request.model_name,
                'task_function': 'train_model_task'
            }
        )
        
        return create_success_response({
            'task_id': task_id,
            'model_name': request.model_name,
            'message': '模型训练任务已创建'
        })
        
    except Exception as e:
        logger.error(f"创建模型训练任务失败: {e}")
        return create_error_response(f"创建模型训练任务失败: {str(e)}", 500)

@router.post("/model-prediction")
async def create_model_prediction_task(request: ModelPredictionRequest):
    """创建模型预测任务"""
    try:
        task_manager = TaskManagerAdapter()
        
        task_id = task_manager.create_task(
            task_type="MODEL_PREDICTION",
            task_function="app.core.async_tasks.model_training_tasks.predict_model_task",
            kwargs={
                'model_name': request.model_name,
                'input_data': request.input_data,
                'prediction_options': request.prediction_options
            },
            priority=request.priority,
            callback_url=request.callback_url,
            metadata={
                'model_name': request.model_name,
                'task_function': 'predict_model_task'
            }
        )
        
        return create_success_response({
            'task_id': task_id,
            'model_name': request.model_name,
            'message': '模型预测任务已创建'
        })
        
    except Exception as e:
        logger.error(f"创建模型预测任务失败: {e}")
        return create_error_response(f"创建模型预测任务失败: {str(e)}", 500)

@router.get("/tasks/{task_id}")
async def get_task(task_id: str):
    """获取任务信息"""
    try:
        task_manager = TaskManagerAdapter()
        task_info = task_manager.get_task_info(task_id)
        
        if not task_info:
            return create_error_response("任务不存在", 404)
        
        return create_success_response(_task_info_to_dict(task_info))
        
    except Exception as e:
        logger.error(f"获取任务信息失败: {e}")
        return create_error_response(f"获取任务信息失败: {str(e)}", 500)

@router.get("/tasks")
async def list_tasks(
    status: Optional[str] = Query(None, description="任务状态过滤"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    limit: int = Query(100, description="返回数量限制")
):
    """列出任务"""
    try:
        task_manager = TaskManagerAdapter()
        
        # 转换状态过滤器
        status_filter = None
        if status:
            try:
                status_filter = TaskStatus(status.lower())
            except ValueError:
                return create_error_response(f"无效的任务状态: {status}", 400)
        
        tasks = task_manager.list_tasks(
            status=status_filter,
            task_type=task_type,
            limit=limit
        )
        
        return create_success_response({
            'tasks': [_task_info_to_dict(task) for task in tasks],
            'total': len(tasks)
        })
        
    except Exception as e:
        logger.error(f"列出任务失败: {e}")
        return create_error_response(f"列出任务失败: {str(e)}", 500)

@router.put("/tasks/{task_id}/progress")
async def update_task_progress(task_id: str, progress: TaskProgressUpdate):
    """更新任务进度"""
    try:
        task_manager = TaskManagerAdapter()
        
        task_manager.update_task_progress(
            task_id=task_id,
            current=progress.current,
            total=progress.total,
            message=progress.message,
            details=progress.details
        )
        
        return create_success_response({'message': '任务进度已更新'})
        
    except Exception as e:
        logger.error(f"更新任务进度失败: {e}")
        return create_error_response(f"更新任务进度失败: {str(e)}", 500)

@router.post("/tasks/{task_id}/cancel")
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        task_manager = TaskManagerAdapter()
        
        success = task_manager.cancel_task(task_id)
        
        if success:
            return create_success_response({'message': '任务已取消'})
        else:
            return create_error_response("取消任务失败", 500)
        
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        return create_error_response(f"取消任务失败: {str(e)}", 500)

@router.post("/tasks/{task_id}/retry")
async def retry_task(task_id: str):
    """重试任务"""
    try:
        task_manager = TaskManagerAdapter()
        
        success = task_manager.retry_task(task_id)
        
        if success:
            return create_success_response({'message': '任务已重试'})
        else:
            return create_error_response("重试任务失败", 500)
        
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        return create_error_response(f"重试任务失败: {str(e)}", 500)

@router.delete("/tasks/cleanup")
async def cleanup_old_tasks(
    days: int = Query(7, description="清理多少天前的任务"),
    background_tasks: BackgroundTasks = None
):
    """清理旧任务"""
    try:
        task_manager = TaskManagerAdapter()
        
        # 在后台执行清理
        if background_tasks:
            background_tasks.add_task(task_manager.cleanup_old_tasks, days)
            return create_success_response({'message': f'已开始清理{days}天前的旧任务'})
        else:
            task_manager.cleanup_old_tasks(days)
            return create_success_response({'message': f'已清理{days}天前的旧任务'})
        
    except Exception as e:
        logger.error(f"清理旧任务失败: {e}")
        return create_error_response(f"清理旧任务失败: {str(e)}", 500)

def _task_info_to_dict(task_info: TaskInfo) -> Dict[str, Any]:
    """将TaskInfo转换为字典"""
    return {
        'task_id': task_info.task_id,
        'task_type': task_info.task_type,
        'status': task_info.status.value,
        'priority': task_info.priority.value,
        'created_at': task_info.created_at.isoformat() if task_info.created_at else None,
        'started_at': task_info.started_at.isoformat() if task_info.started_at else None,
        'completed_at': task_info.completed_at.isoformat() if task_info.completed_at else None,
        'progress': {
            'current': task_info.progress.current,
            'total': task_info.progress.total,
            'percentage': task_info.progress.percentage,
            'message': task_info.progress.message,
            'details': task_info.progress.details
        } if task_info.progress else None,
        'result': task_info.result,
        'error': task_info.error,
        'retry_count': task_info.retry_count,
        'max_retries': task_info.max_retries,
        'callback_url': task_info.callback_url,
        'metadata': task_info.metadata
    }
