"""
健康检查API端点
提供系统健康状态和服务连接检查的REST API
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional
import logging
from datetime import datetime

from ..core.service_health import (
    get_health_checker, 
    check_all_services, 
    check_service
)
from ..core.config import get_config
from ..core.redis_client import get_redis_client, test_redis_connection

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/health", tags=["健康检查"])

@router.get("/", summary="系统整体健康检查")
async def health_check() -> Dict[str, Any]:
    """
    系统整体健康检查
    返回所有服务的健康状态
    """
    try:
        health_results = check_all_services()
        
        # 根据整体状态设置HTTP状态码
        status_code = 200
        overall_status = health_results.get("overall_status", "unknown")
        
        if overall_status == "unhealthy":
            status_code = 503  # Service Unavailable
        elif overall_status == "degraded":
            status_code = 206  # Partial Content
        
        return JSONResponse(
            status_code=status_code,
            content=health_results
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "overall_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/simple", summary="简单健康检查")
async def simple_health_check() -> Dict[str, str]:
    """
    简单健康检查，仅返回基本状态
    用于负载均衡器等外部系统的快速检查
    """
    try:
        config = get_config()
        
        # 基本配置检查
        errors = config.validate()
        if errors:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "message": "配置验证失败"
                }
            )
        
        # Redis基本连接检查
        if not test_redis_connection():
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy", 
                    "message": "Redis连接失败"
                }
            )
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"简单健康检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": str(e)
            }
        )

@router.get("/services/{service_name}", summary="特定服务健康检查")
async def service_health_check(service_name: str) -> Dict[str, Any]:
    """
    检查特定服务的健康状态
    
    支持的服务:
    - config: 配置管理
    - redis: Redis服务
    - mindsdb: MindsDB服务
    - celery: Celery任务队列
    """
    try:
        service_result = check_service(service_name)
        
        # 根据服务状态设置HTTP状态码
        status_code = 200
        service_status = service_result.get("status", "unknown")
        
        if service_status in ["unhealthy", "unreachable", "timeout"]:
            status_code = 503
        elif service_status in ["error", "unknown"]:
            status_code = 500
        elif service_status in ["no_workers", "degraded"]:
            status_code = 206
        
        return JSONResponse(
            status_code=status_code,
            content=service_result
        )
        
    except Exception as e:
        logger.error(f"服务 {service_name} 健康检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "service": service_name,
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/config", summary="配置信息检查")
async def config_check() -> Dict[str, Any]:
    """
    获取当前配置信息（不包含敏感数据）
    """
    try:
        config = get_config()
        
        # 获取配置摘要
        config_summary = config.get_config_summary()
        
        # 验证配置
        validation_errors = config.validate()
        
        return {
            "config": config_summary,
            "validation": {
                "valid": len(validation_errors) == 0,
                "errors": validation_errors
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"配置检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/redis", summary="Redis详细状态")
async def redis_detailed_check() -> Dict[str, Any]:
    """
    获取Redis的详细状态信息
    """
    try:
        redis_client = get_redis_client()
        
        # 健康检查
        health_info = redis_client.health_check()
        
        # 统计信息
        stats_info = redis_client.get_stats()
        
        # 连接信息
        config = get_config()
        connection_info = config.get_redis_connection_info()
        
        # 移除敏感信息
        safe_connection_info = {
            "host": connection_info["host"],
            "port": connection_info["port"],
            "db_broker": connection_info["db_broker"],
            "db_result": connection_info["db_result"]
        }
        
        return {
            "health": health_info,
            "stats": stats_info,
            "connection": safe_connection_info,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Redis详细检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/summary", summary="服务摘要信息")
async def service_summary() -> Dict[str, Any]:
    """
    获取所有服务的摘要信息
    """
    try:
        health_checker = get_health_checker()
        summary = health_checker.get_service_summary()
        
        return summary
        
    except Exception as e:
        logger.error(f"服务摘要获取失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-connection", summary="测试服务连接")
async def test_connection(service_name: Optional[str] = None) -> Dict[str, Any]:
    """
    测试服务连接
    如果指定service_name，则测试特定服务；否则测试所有服务
    """
    try:
        if service_name:
            # 测试特定服务
            result = check_service(service_name)
            return {
                "test_type": "single_service",
                "service": service_name,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            # 测试所有服务
            results = check_all_services()
            return {
                "test_type": "all_services",
                "results": results,
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"连接测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/readiness", summary="就绪检查")
async def readiness_check() -> Dict[str, Any]:
    """
    就绪检查 - 检查应用是否准备好接收请求
    """
    try:
        config = get_config()
        
        # 检查关键服务
        critical_checks = {
            "config_valid": len(config.validate()) == 0,
            "redis_connected": test_redis_connection()
        }
        
        # 所有关键检查都通过才算就绪
        is_ready = all(critical_checks.values())
        
        status_code = 200 if is_ready else 503
        
        return JSONResponse(
            status_code=status_code,
            content={
                "ready": is_ready,
                "checks": critical_checks,
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"就绪检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "ready": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/liveness", summary="存活检查")
async def liveness_check() -> Dict[str, str]:
    """
    存活检查 - 检查应用是否还在运行
    """
    return {
        "alive": "true",
        "timestamp": datetime.now().isoformat()
    }
