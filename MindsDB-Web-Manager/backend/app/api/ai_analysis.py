"""
AI分析API路由
提供智能数据分析功能
"""
import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from core.ai_analysis_service import get_ai_analysis_service, AnalysisResult
from core.ai_function_manager import AnalysisType
from core.models import APIResponse
from core.api_utils import create_success_response, create_error_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ai-analysis", tags=["AI Analysis"])

class AnalysisRequest(BaseModel):
    """分析请求"""
    table: str = Field(..., description="表名")
    columns: List[str] = Field(..., description="分析列")
    analysis_type: str = Field(..., description="分析类型")
    conditions: Optional[str] = Field(None, description="查询条件")
    limit: Optional[int] = Field(1000, description="数据限制")

class AnalysisResponse(BaseModel):
    """分析响应"""
    analysis_type: str
    insights: List[str]
    recommendations: List[str]
    data_summary: Dict[str, Any]
    confidence_score: float
    visualization_suggestions: List[Dict[str, Any]]
    sql_query: Optional[str] = None

@router.post("/analyze", response_model=APIResponse[AnalysisResponse])
async def analyze_data(request: AnalysisRequest):
    """执行数据分析"""
    try:
        # 验证分析类型
        try:
            analysis_type = AnalysisType(request.analysis_type)
        except ValueError:
            return create_error_response(
                f"不支持的分析类型: {request.analysis_type}",
                data=None
            )
        
        # 获取AI分析服务
        ai_analysis_service = get_ai_analysis_service()
        
        # 执行分析
        result = ai_analysis_service.analyze_data(
            table=request.table,
            columns=request.columns,
            analysis_type=analysis_type,
            conditions=request.conditions,
            limit=request.limit
        )
        
        # 构建响应
        response = AnalysisResponse(
            analysis_type=result.analysis_type,
            insights=result.insights,
            recommendations=result.recommendations,
            data_summary=result.data_summary,
            confidence_score=result.confidence_score,
            visualization_suggestions=result.visualization_suggestions
        )
        
        return create_success_response(
            data=response,
            message="数据分析完成"
        )
        
    except Exception as e:
        logger.error(f"数据分析失败: {str(e)}")
        return create_error_response(
            f"数据分析失败: {str(e)}",
            data=None
        )

@router.get("/types", response_model=APIResponse[List[Dict[str, str]]])
async def get_analysis_types():
    """获取支持的分析类型"""
    try:
        analysis_types = [
            {
                "type": AnalysisType.TREND_ANALYSIS.value,
                "name": "趋势分析",
                "description": "分析数据的变化趋势和模式"
            },
            {
                "type": AnalysisType.PATTERN_RECOGNITION.value,
                "name": "模式识别",
                "description": "识别数据中的重复模式和规律"
            },
            {
                "type": AnalysisType.ANOMALY_DETECTION.value,
                "name": "异常检测",
                "description": "检测数据中的异常值和离群点"
            },
            {
                "type": AnalysisType.CORRELATION_ANALYSIS.value,
                "name": "相关性分析",
                "description": "分析变量之间的相关性"
            },
            {
                "type": AnalysisType.CLUSTERING_ANALYSIS.value,
                "name": "聚类分析",
                "description": "将数据分组为相似的集群"
            },
            {
                "type": AnalysisType.TIME_SERIES_ANALYSIS.value,
                "name": "时间序列分析",
                "description": "分析时间序列数据的特征"
            }
        ]
        
        return create_success_response(
            data=analysis_types,
            message="获取分析类型成功"
        )
        
    except Exception as e:
        logger.error(f"获取分析类型失败: {str(e)}")
        return create_error_response(
            f"获取分析类型失败: {str(e)}",
            data=None
        )

@router.post("/text-analyze", response_model=APIResponse[AnalysisResponse])
async def text_to_analysis(request: Dict[str, Any]):
    """文本转AI分析"""
    try:
        query = request.get("query", "")
        databases = request.get("databases", [])
        
        if not query:
            return create_error_response(
                "查询文本不能为空",
                data=None
            )
        
        # 这里可以集成现有的Text2SQL引擎来解析查询
        # 然后调用AI分析功能
        
        # 临时实现：返回示例分析结果
        response = AnalysisResponse(
            analysis_type="trend_analysis",
            insights=["基于文本查询的AI分析功能正在开发中"],
            recommendations=["建议使用结构化的分析请求"],
            data_summary={"query": query, "databases": databases},
            confidence_score=0.7,
            visualization_suggestions=[
                {
                    "type": "line_chart",
                    "description": "推荐使用折线图展示趋势",
                    "priority": "high"
                }
            ]
        )
        
        return create_success_response(
            data=response,
            message="文本分析完成"
        )
        
    except Exception as e:
        logger.error(f"文本分析失败: {str(e)}")
        return create_error_response(
            f"文本分析失败: {str(e)}",
            data=None
        )

@router.get("/examples", response_model=APIResponse[List[Dict[str, Any]]])
async def get_analysis_examples():
    """获取分析示例"""
    try:
        examples = [
            {
                "title": "销售趋势分析",
                "description": "分析销售数据的时间趋势",
                "request": {
                    "table": "sales_data",
                    "columns": ["sales_amount", "date"],
                    "analysis_type": "trend_analysis"
                }
            },
            {
                "title": "客户行为模式识别",
                "description": "识别客户购买行为模式",
                "request": {
                    "table": "customer_behavior",
                    "columns": ["purchase_frequency", "amount_spent"],
                    "analysis_type": "pattern_recognition"
                }
            },
            {
                "title": "异常交易检测",
                "description": "检测异常的交易记录",
                "request": {
                    "table": "transactions",
                    "columns": ["amount", "transaction_time"],
                    "analysis_type": "anomaly_detection"
                }
            },
            {
                "title": "产品相关性分析",
                "description": "分析不同产品之间的关联性",
                "request": {
                    "table": "product_sales",
                    "columns": ["product_a_sales", "product_b_sales"],
                    "analysis_type": "correlation_analysis"
                }
            }
        ]
        
        return create_success_response(
            data=examples,
            message="获取分析示例成功"
        )
        
    except Exception as e:
        logger.error(f"获取分析示例失败: {str(e)}")
        return create_error_response(
            f"获取分析示例失败: {str(e)}",
            data=None
        )

@router.get("/health", response_model=APIResponse[Dict[str, Any]])
async def check_ai_analysis_health():
    """检查AI分析服务健康状态"""
    try:
        ai_analysis_service = get_ai_analysis_service()
        
        # 简单的健康检查
        health_status = {
            "status": "healthy",
            "service": "ai_analysis",
            "supported_types": len(AnalysisType),
            "timestamp": "2025-07-10T11:30:00Z"
        }
        
        return create_success_response(
            data=health_status,
            message="AI分析服务运行正常"
        )
        
    except Exception as e:
        logger.error(f"AI分析服务健康检查失败: {str(e)}")
        return create_error_response(
            f"AI分析服务不可用: {str(e)}",
            data=None
        )
