"""
Agent日志收集API
提供Agent日志的收集、查询和分析功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta
import logging
import uuid
import json

from core.auth_middleware import get_current_active_user
from core.auth_models import User
from core.database import get_db
from core.api_response import APIResponseBuilder
from core.agent_log_models import (
    AgentExecutionLog, AgentLogSession, AgentLogRepository, LogLevel, LogCategory
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/agent-logs", tags=["agent-logs"])

# 请求模型
class LogEntryRequest(BaseModel):
    """日志条目请求"""
    agent_id: str = Field(..., min_length=1, max_length=100, description="Agent ID")
    agent_name: Optional[str] = Field(None, max_length=200, description="Agent名称")
    agent_version: Optional[str] = Field(None, max_length=50, description="Agent版本")
    execution_id: Optional[str] = Field(None, max_length=100, description="执行ID")
    test_run_id: Optional[int] = Field(None, description="测试运行ID")
    test_case_id: Optional[int] = Field(None, description="测试用例ID")
    level: str = Field("info", description="日志级别")
    category: str = Field("execution", description="日志分类")
    message: str = Field(..., min_length=1, description="日志消息")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    stack_trace: Optional[str] = Field(None, description="堆栈跟踪")
    response_time_ms: Optional[float] = Field(None, ge=0, description="响应时间(毫秒)")
    memory_usage_mb: Optional[float] = Field(None, ge=0, description="内存使用量(MB)")
    cpu_usage_percent: Optional[float] = Field(None, ge=0, le=100, description="CPU使用率")
    source_file: Optional[str] = Field(None, max_length=500, description="源文件")
    source_line: Optional[int] = Field(None, description="源代码行号")
    function_name: Optional[str] = Field(None, max_length=200, description="函数名")
    request_id: Optional[str] = Field(None, max_length=100, description="请求ID")
    user_id: Optional[str] = Field(None, max_length=100, description="用户ID")
    session_id: Optional[str] = Field(None, max_length=100, description="会话ID")
    environment: Optional[Dict[str, Any]] = Field(None, description="环境信息")

    @validator('level')
    def validate_level(cls, v):
        valid_levels = [level.value for level in LogLevel]
        if v not in valid_levels:
            raise ValueError(f"日志级别必须是: {', '.join(valid_levels)}")
        return v

    @validator('category')
    def validate_category(cls, v):
        valid_categories = [cat.value for cat in LogCategory]
        if v not in valid_categories:
            raise ValueError(f"日志分类必须是: {', '.join(valid_categories)}")
        return v

class BatchLogRequest(BaseModel):
    """批量日志请求"""
    logs: List[LogEntryRequest] = Field(..., min_items=1, max_items=1000, description="日志列表")

class LogSessionRequest(BaseModel):
    """日志会话请求"""
    agent_id: str = Field(..., min_length=1, max_length=100, description="Agent ID")
    agent_name: Optional[str] = Field(None, max_length=200, description="Agent名称")
    agent_version: Optional[str] = Field(None, max_length=50, description="Agent版本")
    metadata: Optional[Dict[str, Any]] = Field(None, description="会话元数据")

# 响应模型
class LogEntryResponse(BaseModel):
    """日志条目响应"""
    id: int
    log_id: str
    agent_id: str
    agent_name: Optional[str]
    level: str
    category: str
    message: str
    timestamp: str
    response_time_ms: Optional[float]
    memory_usage_mb: Optional[float]
    cpu_usage_percent: Optional[float]

class LogStatisticsResponse(BaseModel):
    """日志统计响应"""
    total_logs: int
    error_count: int
    warning_count: int
    info_count: int
    debug_count: int
    critical_count: int
    avg_response_time: float
    max_memory_usage: float
    avg_cpu_usage: float
    level_distribution: Dict[str, int]

# API端点实现
@router.post("/log", response_model=dict)
async def create_log_entry(
    log_data: LogEntryRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建单个日志条目"""
    try:
        repo = AgentLogRepository(db)
        
        # 准备日志数据
        log_dict = log_data.dict()
        if not log_dict.get("timestamp"):
            log_dict["timestamp"] = datetime.now()
        
        # 创建日志
        log = repo.create_log(log_dict)
        
        return APIResponseBuilder.success(
            data={
                "id": log.id,
                "log_id": log.log_id,
                "agent_id": log.agent_id,
                "level": log.level,
                "category": log.category,
                "timestamp": log.timestamp.isoformat()
            },
            message="日志创建成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"创建日志失败: {str(e)}")
        return APIResponseBuilder.error(
            message="创建日志失败",
            error_code="CREATE_LOG_ERROR"
        ).dict()

@router.post("/logs/batch", response_model=dict)
async def create_batch_logs(
    batch_data: BatchLogRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """批量创建日志条目"""
    try:
        repo = AgentLogRepository(db)
        
        # 准备批量日志数据
        logs_data = []
        for log_data in batch_data.logs:
            log_dict = log_data.dict()
            if not log_dict.get("timestamp"):
                log_dict["timestamp"] = datetime.now()
            logs_data.append(log_dict)
        
        # 批量创建日志
        logs = repo.batch_create_logs(logs_data)
        
        return APIResponseBuilder.success(
            data={
                "created_count": len(logs),
                "log_ids": [log.log_id for log in logs]
            },
            message=f"批量创建 {len(logs)} 条日志成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"批量创建日志失败: {str(e)}")
        return APIResponseBuilder.error(
            message="批量创建日志失败",
            error_code="BATCH_CREATE_LOG_ERROR"
        ).dict()

@router.get("/logs", response_model=dict)
async def get_logs(
    agent_id: Optional[str] = Query(None, description="Agent ID"),
    level: Optional[str] = Query(None, description="日志级别"),
    category: Optional[str] = Query(None, description="日志分类"),
    execution_id: Optional[str] = Query(None, description="执行ID"),
    request_id: Optional[str] = Query(None, description="请求ID"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """查询日志列表"""
    try:
        repo = AgentLogRepository(db)
        
        logs = repo.get_logs(
            agent_id=agent_id,
            level=level,
            category=category,
            execution_id=execution_id,
            request_id=request_id,
            start_time=start_time,
            end_time=end_time,
            limit=limit,
            offset=offset
        )
        
        logs_data = []
        for log in logs:
            logs_data.append({
                "id": log.id,
                "log_id": log.log_id,
                "agent_id": log.agent_id,
                "agent_name": log.agent_name,
                "execution_id": log.execution_id,
                "level": log.level,
                "category": log.category,
                "message": log.message,
                "timestamp": log.timestamp.isoformat() if log.timestamp else None,
                "details": log.details,
                "response_time_ms": log.response_time_ms,
                "memory_usage_mb": log.memory_usage_mb,
                "cpu_usage_percent": log.cpu_usage_percent,
                "source_file": log.source_file,
                "source_line": log.source_line,
                "function_name": log.function_name,
                "request_id": log.request_id,
                "user_id": log.user_id,
                "session_id": log.session_id
            })
        
        return APIResponseBuilder.success(
            data={
                "logs": logs_data,
                "total": len(logs_data),
                "limit": limit,
                "offset": offset
            },
            message="获取日志列表成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取日志列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取日志列表失败",
            error_code="GET_LOGS_ERROR"
        ).dict()

@router.get("/logs/{log_id}", response_model=dict)
async def get_log_by_id(
    log_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """根据ID获取日志详情"""
    try:
        repo = AgentLogRepository(db)
        log = repo.get_log_by_id(log_id)
        
        if not log:
            return APIResponseBuilder.error(
                message="日志不存在",
                error_code="LOG_NOT_FOUND"
            ).dict()
        
        return APIResponseBuilder.success(
            data=log.to_dict(),
            message="获取日志详情成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取日志详情失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取日志详情失败",
            error_code="GET_LOG_ERROR"
        ).dict()

@router.get("/logs/search", response_model=dict)
async def search_logs(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    agent_id: Optional[str] = Query(None, description="Agent ID"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """搜索日志内容"""
    try:
        repo = AgentLogRepository(db)
        logs = repo.search_logs(q, agent_id=agent_id, limit=limit)
        
        logs_data = []
        for log in logs:
            logs_data.append({
                "id": log.id,
                "log_id": log.log_id,
                "agent_id": log.agent_id,
                "agent_name": log.agent_name,
                "level": log.level,
                "category": log.category,
                "message": log.message,
                "timestamp": log.timestamp.isoformat() if log.timestamp else None,
                "response_time_ms": log.response_time_ms
            })
        
        return APIResponseBuilder.success(
            data={
                "logs": logs_data,
                "total": len(logs_data),
                "search_query": q
            },
            message="搜索日志成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"搜索日志失败: {str(e)}")
        return APIResponseBuilder.error(
            message="搜索日志失败",
            error_code="SEARCH_LOGS_ERROR"
        ).dict()

@router.get("/statistics", response_model=dict)
async def get_log_statistics(
    agent_id: Optional[str] = Query(None, description="Agent ID"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取日志统计信息"""
    try:
        repo = AgentLogRepository(db)
        stats = repo.get_log_statistics(
            agent_id=agent_id,
            start_time=start_time,
            end_time=end_time
        )
        
        return APIResponseBuilder.success(
            data=stats,
            message="获取日志统计成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取日志统计失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取日志统计失败",
            error_code="GET_STATISTICS_ERROR"
        ).dict()

@router.post("/sessions", response_model=dict)
async def create_log_session(
    session_data: LogSessionRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建日志会话"""
    try:
        repo = AgentLogRepository(db)
        session = repo.create_session(session_data.dict())
        
        return APIResponseBuilder.success(
            data={
                "id": session.id,
                "session_id": session.session_id,
                "agent_id": session.agent_id,
                "started_at": session.started_at.isoformat()
            },
            message="日志会话创建成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"创建日志会话失败: {str(e)}")
        return APIResponseBuilder.error(
            message="创建日志会话失败",
            error_code="CREATE_SESSION_ERROR"
        ).dict()

@router.post("/sessions/{session_id}/end", response_model=dict)
async def end_log_session(
    session_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """结束日志会话"""
    try:
        repo = AgentLogRepository(db)
        session = repo.end_session(session_id)
        
        if not session:
            return APIResponseBuilder.error(
                message="会话不存在",
                error_code="SESSION_NOT_FOUND"
            ).dict()
        
        return APIResponseBuilder.success(
            data={
                "session_id": session.session_id,
                "ended_at": session.ended_at.isoformat() if session.ended_at else None,
                "duration_seconds": session.duration_seconds,
                "total_logs": session.total_logs,
                "error_logs": session.error_logs
            },
            message="日志会话结束成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"结束日志会话失败: {str(e)}")
        return APIResponseBuilder.error(
            message="结束日志会话失败",
            error_code="END_SESSION_ERROR"
        ).dict()
