"""
任务管理API路由
提供任务的CRUD操作和执行控制功能
"""

import logging
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel, Field
from enum import Enum

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/tasks", tags=["TaskManagement"])

# 任务相关枚举和模型
class TaskType(str, Enum):
    """任务类型"""
    SSH = "ssh"
    POSTGRESQL = "postgresql"
    MINDSDB = "mindsdb"

class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class TaskPriority(str, Enum):
    """任务优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class TaskSchedule(str, Enum):
    """任务调度类型"""
    IMMEDIATE = "immediate"
    SCHEDULED = "scheduled"
    RECURRING = "recurring"

class TaskCreateRequest(BaseModel):
    """创建任务请求"""
    name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    type: TaskType = Field(..., description="任务类型")
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="任务优先级")
    timeout: int = Field(300, ge=10, le=3600, description="超时时间（秒）")
    schedule: TaskSchedule = Field(TaskSchedule.IMMEDIATE, description="调度类型")
    scheduled_time: Optional[datetime] = Field(None, description="调度时间")
    config: Dict[str, Any] = Field(..., description="任务配置")

class TaskResponse(BaseModel):
    """任务响应"""
    id: int
    name: str
    description: Optional[str]
    type: TaskType
    status: TaskStatus
    priority: TaskPriority
    progress: float = Field(0.0, ge=0.0, le=100.0, description="进度百分比")
    timeout: int
    schedule: TaskSchedule
    scheduled_time: Optional[datetime]
    config: Dict[str, Any]
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

# 模拟任务存储
tasks_db = {}
next_task_id = 1

def get_next_task_id():
    """获取下一个任务ID"""
    global next_task_id
    current_id = next_task_id
    next_task_id += 1
    return current_id

@router.get("/", response_model=List[TaskResponse])
async def list_tasks(
    status: Optional[TaskStatus] = Query(None, description="按状态筛选"),
    type: Optional[TaskType] = Query(None, description="按类型筛选"),
    priority: Optional[TaskPriority] = Query(None, description="按优先级筛选"),
    limit: int = Query(50, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """获取任务列表"""
    try:
        # 筛选任务
        filtered_tasks = []
        for task_data in tasks_db.values():
            if status and task_data['status'] != status:
                continue
            if type and task_data['type'] != type:
                continue
            if priority and task_data['priority'] != priority:
                continue
            filtered_tasks.append(task_data)
        
        # 排序（按创建时间倒序）
        filtered_tasks.sort(key=lambda x: x['created_at'], reverse=True)
        
        # 分页
        paginated_tasks = filtered_tasks[offset:offset + limit]
        
        # 转换为响应格式
        tasks = []
        for task_data in paginated_tasks:
            # 隐藏敏感配置信息
            config = task_data['config'].copy()
            if 'password' in config:
                config['password'] = '***'
            
            tasks.append(TaskResponse(**{
                **task_data,
                'config': config
            }))
        
        return tasks
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务列表失败")

@router.post("/", response_model=TaskResponse, status_code=201)
async def create_task(task: TaskCreateRequest, background_tasks: BackgroundTasks):
    """创建新任务"""
    try:
        # 验证调度时间
        if task.schedule != TaskSchedule.IMMEDIATE and not task.scheduled_time:
            raise HTTPException(status_code=400, detail="定时或循环任务需要指定调度时间")
        
        # 创建任务
        task_id = get_next_task_id()
        now = datetime.now()
        
        task_data = {
            'id': task_id,
            'name': task.name,
            'description': task.description,
            'type': task.type,
            'status': TaskStatus.PENDING,
            'priority': task.priority,
            'progress': 0.0,
            'timeout': task.timeout,
            'schedule': task.schedule,
            'scheduled_time': task.scheduled_time,
            'config': task.config,
            'result': None,
            'error_message': None,
            'created_at': now,
            'updated_at': now,
            'started_at': None,
            'completed_at': None
        }
        
        tasks_db[task_id] = task_data
        
        # 如果是立即执行，启动任务
        if task.schedule == TaskSchedule.IMMEDIATE:
            background_tasks.add_task(execute_task, task_id)
        
        # 隐藏敏感配置信息
        config = task_data['config'].copy()
        if 'password' in config:
            config['password'] = '***'
        
        logger.info(f"任务创建成功: {task.name} (ID: {task_id})")
        
        return TaskResponse(**{
            **task_data,
            'config': config
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        raise HTTPException(status_code=500, detail="创建任务失败")

@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(task_id: int):
    """获取指定任务"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    try:
        task_data = tasks_db[task_id]
        
        # 隐藏敏感配置信息
        config = task_data['config'].copy()
        if 'password' in config:
            config['password'] = '***'
        
        return TaskResponse(**{
            **task_data,
            'config': config
        })
        
    except Exception as e:
        logger.error(f"获取任务失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务失败")

@router.post("/{task_id}/start")
async def start_task(task_id: int, background_tasks: BackgroundTasks):
    """启动任务"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    try:
        task_data = tasks_db[task_id]
        
        # 检查任务状态
        if task_data['status'] == TaskStatus.RUNNING:
            raise HTTPException(status_code=400, detail="任务已在运行中")
        
        if task_data['status'] == TaskStatus.COMPLETED:
            raise HTTPException(status_code=400, detail="任务已完成，无法重新启动")
        
        # 更新任务状态
        task_data['status'] = TaskStatus.RUNNING
        task_data['started_at'] = datetime.now()
        task_data['updated_at'] = datetime.now()
        task_data['progress'] = 0.0
        task_data['error_message'] = None
        
        # 添加后台任务执行
        background_tasks.add_task(execute_task, task_id)
        
        return {
            "success": True,
            "message": f"任务 '{task_data['name']}' 已启动",
            "task_id": task_id,
            "status": task_data['status']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")

@router.post("/{task_id}/pause")
async def pause_task(task_id: int):
    """暂停任务"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    try:
        task_data = tasks_db[task_id]
        
        # 检查任务状态
        if task_data['status'] != TaskStatus.RUNNING:
            raise HTTPException(status_code=400, detail="只能暂停运行中的任务")
        
        # 更新任务状态
        task_data['status'] = TaskStatus.PAUSED
        task_data['updated_at'] = datetime.now()
        
        return {
            "success": True,
            "message": f"任务 '{task_data['name']}' 已暂停",
            "task_id": task_id,
            "status": task_data['status']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停任务失败: {e}")
        raise HTTPException(status_code=500, detail="暂停任务失败")

@router.post("/{task_id}/cancel")
async def cancel_task(task_id: int):
    """取消任务"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    try:
        task_data = tasks_db[task_id]
        
        # 检查任务状态
        if task_data['status'] in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
            raise HTTPException(status_code=400, detail="任务已完成或已取消")
        
        # 更新任务状态
        task_data['status'] = TaskStatus.CANCELLED
        task_data['updated_at'] = datetime.now()
        task_data['completed_at'] = datetime.now()
        
        return {
            "success": True,
            "message": f"任务 '{task_data['name']}' 已取消",
            "task_id": task_id,
            "status": task_data['status']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail="取消任务失败")

@router.get("/statistics/summary")
async def get_task_statistics():
    """获取任务统计信息"""
    try:
        total_tasks = len(tasks_db)
        
        if total_tasks == 0:
            return {
                "total": 0,
                "by_status": {},
                "by_type": {},
                "by_priority": {}
            }
        
        # 按状态统计
        status_stats = {}
        for status in TaskStatus:
            status_stats[status.value] = len([t for t in tasks_db.values() if t['status'] == status])
        
        # 按类型统计
        type_stats = {}
        for task_type in TaskType:
            type_stats[task_type.value] = len([t for t in tasks_db.values() if t['type'] == task_type])
        
        # 按优先级统计
        priority_stats = {}
        for priority in TaskPriority:
            priority_stats[priority.value] = len([t for t in tasks_db.values() if t['priority'] == priority])
        
        return {
            "total": total_tasks,
            "by_status": status_stats,
            "by_type": type_stats,
            "by_priority": priority_stats
        }
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务统计失败")

async def execute_task(task_id: int):
    """执行任务（后台任务）"""
    try:
        task_data = tasks_db.get(task_id)
        if not task_data:
            return
        
        # 模拟任务执行
        total_steps = 10
        for step in range(total_steps):
            # 检查任务是否被取消或暂停
            if task_data['status'] in [TaskStatus.CANCELLED, TaskStatus.PAUSED]:
                break
            
            # 模拟执行步骤
            await asyncio.sleep(1)
            
            # 更新进度
            progress = ((step + 1) / total_steps) * 100
            task_data['progress'] = progress
            task_data['updated_at'] = datetime.now()
        
        # 完成任务
        if task_data['status'] == TaskStatus.RUNNING:
            task_data['status'] = TaskStatus.COMPLETED
            task_data['progress'] = 100.0
            task_data['completed_at'] = datetime.now()
            task_data['updated_at'] = datetime.now()
            task_data['result'] = {"message": "任务执行成功", "output": "模拟执行结果"}
        
    except Exception as e:
        logger.error(f"执行任务失败: {e}")
        
        # 更新任务状态为失败
        if task_id in tasks_db:
            task_data = tasks_db[task_id]
            task_data['status'] = TaskStatus.FAILED
            task_data['error_message'] = str(e)
            task_data['completed_at'] = datetime.now()
            task_data['updated_at'] = datetime.now()

# 初始化一些示例任务
def init_sample_tasks():
    """初始化示例任务"""
    global next_task_id
    
    sample_tasks = [
        {
            "name": "数据库备份任务",
            "description": "定期备份生产数据库",
            "type": TaskType.POSTGRESQL,
            "priority": TaskPriority.HIGH,
            "config": {
                "host": "prod-db.example.com",
                "port": 5432,
                "database": "production",
                "username": "backup_user",
                "password": "secure_password",
                "query": "SELECT pg_dump('production');",
                "ssl": True
            }
        },
        {
            "name": "服务器日志清理",
            "description": "清理过期的服务器日志文件",
            "type": TaskType.SSH,
            "priority": TaskPriority.MEDIUM,
            "config": {
                "host": "server.example.com",
                "port": 22,
                "username": "admin",
                "auth_method": "password",
                "password": "admin_password",
                "command": "find /var/log -name '*.log' -mtime +30 -delete"
            }
        }
    ]
    
    for task in sample_tasks:
        task_id = get_next_task_id()
        now = datetime.now()
        
        tasks_db[task_id] = {
            'id': task_id,
            'name': task['name'],
            'description': task['description'],
            'type': task['type'],
            'status': TaskStatus.PENDING,
            'priority': task['priority'],
            'progress': 0.0,
            'timeout': 300,
            'schedule': TaskSchedule.IMMEDIATE,
            'scheduled_time': None,
            'config': task['config'],
            'result': None,
            'error_message': None,
            'created_at': now,
            'updated_at': now,
            'started_at': None,
            'completed_at': None
        }

# 启动时初始化示例任务
init_sample_tasks()
