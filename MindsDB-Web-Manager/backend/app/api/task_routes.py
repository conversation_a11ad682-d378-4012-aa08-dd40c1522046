"""
任务管理RESTful API路由
提供任务创建、查询、管理的HTTP接口
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query, Path, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, ValidationError, validator
from enum import Enum

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/tasks", tags=["tasks"])

# 任务相关枚举和模型
class TaskType(str, Enum):
    """任务类型"""
    SSH = "ssh"
    POSTGRESQL = "postgresql"
    MINDSDB = "mindsdb"

class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class TaskPriority(str, Enum):
    """任务优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class TaskSchedule(str, Enum):
    """任务调度类型"""
    IMMEDIATE = "immediate"
    SCHEDULED = "scheduled"
    RECURRING = "recurring"

class TaskCreateRequest(BaseModel):
    """创建任务请求"""
    name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    type: TaskType = Field(..., description="任务类型")
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="任务优先级")
    timeout: int = Field(300, ge=10, le=3600, description="超时时间（秒）")
    schedule: TaskSchedule = Field(TaskSchedule.IMMEDIATE, description="调度类型")
    scheduled_time: Optional[datetime] = Field(None, description="调度时间")
    config: Dict[str, Any] = Field(..., description="任务配置")

class TaskUpdateRequest(BaseModel):
    """更新任务请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    priority: Optional[TaskPriority] = Field(None, description="任务优先级")
    timeout: Optional[int] = Field(None, ge=10, le=3600, description="超时时间（秒）")
    config: Optional[Dict[str, Any]] = Field(None, description="任务配置")

class TaskResponse(BaseModel):
    """任务响应"""
    id: int
    name: str
    description: Optional[str]
    type: TaskType
    status: TaskStatus
    priority: TaskPriority
    progress: float = Field(0.0, ge=0.0, le=100.0, description="进度百分比")
    timeout: int
    schedule: TaskSchedule
    scheduled_time: Optional[datetime]
    config: Dict[str, Any]
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

# 模拟任务存储
tasks_db = {}
task_executions = {}
next_task_id = 1

def get_next_task_id():
    """获取下一个任务ID"""
    global next_task_id
    current_id = next_task_id
    next_task_id += 1
    return current_id

@router.get("/", response_model=List[TaskResponse])
async def list_tasks(
    status: Optional[TaskStatus] = Query(None, description="按状态筛选"),
    type: Optional[TaskType] = Query(None, description="按类型筛选"),
    priority: Optional[TaskPriority] = Query(None, description="按优先级筛选"),
    limit: int = Query(50, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """获取任务列表"""
    try:
        # 筛选任务
        filtered_tasks = []
        for task_data in tasks_db.values():
            if status and task_data['status'] != status:
                continue
            if type and task_data['type'] != type:
                continue
            if priority and task_data['priority'] != priority:
                continue
            filtered_tasks.append(task_data)

        # 排序（按创建时间倒序）
        filtered_tasks.sort(key=lambda x: x['created_at'], reverse=True)

        # 分页
        paginated_tasks = filtered_tasks[offset:offset + limit]

        # 转换为响应格式
        tasks = []
        for task_data in paginated_tasks:
            # 隐藏敏感配置信息
            config = task_data['config'].copy()
            if 'password' in config:
                config['password'] = '***'

            tasks.append(TaskResponse(
                id=task_data['id'],
                name=task_data['name'],
                description=task_data['description'],
                type=task_data['type'],
                status=task_data['status'],
                priority=task_data['priority'],
                progress=task_data['progress'],
                timeout=task_data['timeout'],
                schedule=task_data['schedule'],
                scheduled_time=task_data['scheduled_time'],
                config=config,
                result=task_data['result'],
                error_message=task_data['error_message'],
                created_at=task_data['created_at'],
                updated_at=task_data['updated_at'],
                started_at=task_data['started_at'],
                completed_at=task_data['completed_at']
            ))

        return tasks

    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务列表失败")

@router.post("/", response_model=TaskResponse, status_code=201)
async def create_task(
    task: TaskCreateRequest,
    background_tasks: BackgroundTasks
) -> TaskResponse:
    """创建新任务"""
    try:
        # 验证调度时间
        if task.schedule != TaskSchedule.IMMEDIATE and not task.scheduled_time:
            raise HTTPException(status_code=400, detail="定时或循环任务需要指定调度时间")

        # 创建任务
        task_id = get_next_task_id()
        now = datetime.now()

        task_data = {
            'id': task_id,
            'name': task.name,
            'description': task.description,
            'type': task.type,
            'status': TaskStatus.PENDING,
            'priority': task.priority,
            'progress': 0.0,
            'timeout': task.timeout,
            'schedule': task.schedule,
            'scheduled_time': task.scheduled_time,
            'config': task.config,
            'result': None,
            'error_message': None,
            'created_at': now,
            'updated_at': now,
            'started_at': None,
            'completed_at': None
        }

        tasks_db[task_id] = task_data

        # 如果是立即执行，启动任务
        if task.schedule == TaskSchedule.IMMEDIATE:
            background_tasks.add_task(execute_task, task_id)

        # 隐藏敏感配置信息
        config = task_data['config'].copy()
        if 'password' in config:
            config['password'] = '***'

        logger.info(f"任务创建成功: {task.name} (ID: {task_id})")
        
        # 1. 验证任务请求
        validation_result = task_validator.validate_task_request(task_request.model_dump())
        
        if not validation_result.is_valid:
            error_messages = [error.message for error in validation_result.errors]
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "任务配置验证失败",
                    "errors": error_messages,
                    "warnings": validation_result.warnings
                }
            )
        
        # 2. 创建任务
        task_id = await task_manager.create_task(task_request.config)
        
        # 3. 如果需要立即调度，添加到后台任务
        if task_request.schedule_immediately:
            background_tasks.add_task(task_manager.schedule_task, task_id)
        
        # 4. 返回响应
        response = TaskResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            message="任务创建成功",
            created_at=datetime.now(),
            config=task_request.config
        )
        
        logger.info(f"任务创建成功: {task_id}")
        
        # 添加验证警告到响应头
        if validation_result.warnings:
            response_dict = response.model_dump()
            response_dict["warnings"] = validation_result.warnings
            return JSONResponse(
                content=response_dict,
                status_code=201,
                headers={"X-Validation-Warnings": str(len(validation_result.warnings))}
            )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"任务创建失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": "任务创建失败",
                "error": str(e)
            }
        )

@router.get("/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(
    task_id: str = Path(..., description="任务ID"),
    task_manager: TaskManager = Depends(get_task_manager)
) -> TaskStatusResponse:
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        task_manager: 任务管理器
        
    Returns:
        任务状态响应
        
    Raises:
        HTTPException: 当任务不存在时
    """
    try:
        logger.debug(f"查询任务状态: {task_id}")
        
        task_info = await task_manager.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(
                status_code=404,
                detail={
                    "message": f"任务 {task_id} 不存在"
                }
            )
        
        return TaskStatusResponse(**task_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": "获取任务状态失败",
                "error": str(e)
            }
        )

@router.get("/", response_model=TaskListResponse)
async def list_tasks(
    status: Optional[str] = Query(None, description="任务状态过滤"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="页大小"),
    task_manager: TaskManager = Depends(get_task_manager)
) -> TaskListResponse:
    """
    获取任务列表
    
    Args:
        status: 状态过滤器
        task_type: 类型过滤器
        page: 页码
        page_size: 页大小
        task_manager: 任务管理器
        
    Returns:
        任务列表响应
    """
    try:
        logger.debug(f"查询任务列表: status={status}, type={task_type}, page={page}")
        
        # 构建过滤器
        filters = {}
        if status:
            try:
                filters["status"] = TaskStatus(status)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail={"message": f"无效的任务状态: {status}"}
                )
        
        if task_type:
            try:
                filters["task_type"] = TaskType(task_type)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail={"message": f"无效的任务类型: {task_type}"}
                )
        
        # 获取任务列表
        tasks_data = await task_manager.list_tasks(
            filters=filters,
            page=page,
            page_size=page_size
        )
        
        # 转换为响应模型
        tasks = [TaskStatusResponse(**task) for task in tasks_data["tasks"]]
        
        return TaskListResponse(
            tasks=tasks,
            total=tasks_data["total"],
            page=page,
            page_size=page_size
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": "获取任务列表失败",
                "error": str(e)
            }
        )

@router.post("/{task_id}/cancel")
async def cancel_task(
    task_id: str = Path(..., description="任务ID"),
    task_manager: TaskManager = Depends(get_task_manager)
) -> Dict[str, Any]:
    """
    取消任务
    
    Args:
        task_id: 任务ID
        task_manager: 任务管理器
        
    Returns:
        操作结果
        
    Raises:
        HTTPException: 当操作失败时
    """
    try:
        logger.info(f"取消任务: {task_id}")
        
        success = await task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": f"无法取消任务 {task_id}，任务可能不存在或已完成"
                }
            )
        
        return {
            "message": f"任务 {task_id} 已取消",
            "task_id": task_id,
            "cancelled_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": "取消任务失败",
                "error": str(e)
            }
        )

@router.post("/{task_id}/retry")
async def retry_task(
    background_tasks: BackgroundTasks,
    task_id: str = Path(..., description="任务ID"),
    task_manager: TaskManager = Depends(get_task_manager)
) -> Dict[str, Any]:
    """
    重试任务
    
    Args:
        task_id: 任务ID
        background_tasks: 后台任务处理器
        task_manager: 任务管理器
        
    Returns:
        操作结果
        
    Raises:
        HTTPException: 当操作失败时
    """
    try:
        logger.info(f"重试任务: {task_id}")
        
        success = await task_manager.retry_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": f"无法重试任务 {task_id}，任务可能不存在或状态不允许重试"
                }
            )
        
        # 添加到后台任务队列
        background_tasks.add_task(task_manager.schedule_task, task_id)
        
        return {
            "message": f"任务 {task_id} 已重新调度",
            "task_id": task_id,
            "retried_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": "重试任务失败",
                "error": str(e)
            }
        )

@router.delete("/{task_id}")
async def delete_task(
    task_id: str = Path(..., description="任务ID"),
    task_manager: TaskManager = Depends(get_task_manager)
) -> Dict[str, Any]:
    """
    删除任务
    
    Args:
        task_id: 任务ID
        task_manager: 任务管理器
        
    Returns:
        操作结果
        
    Raises:
        HTTPException: 当操作失败时
    """
    try:
        logger.info(f"删除任务: {task_id}")
        
        success = await task_manager.delete_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail={
                    "message": f"任务 {task_id} 不存在"
                }
            )
        
        return {
            "message": f"任务 {task_id} 已删除",
            "task_id": task_id,
            "deleted_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": "删除任务失败",
                "error": str(e)
            }
        )

@router.post("/validate", status_code=200)
async def validate_task_config(
    config_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    验证任务配置
    
    Args:
        config_data: 任务配置数据
        
    Returns:
        验证结果
    """
    try:
        logger.debug("验证任务配置")
        
        # 构建完整的请求数据进行验证
        request_data = {
            "config": config_data,
            "schedule_immediately": False
        }
        
        validation_result = validate_task_request_data(request_data)
        
        return {
            "is_valid": validation_result.is_valid,
            "errors": [
                {
                    "message": error.message,
                    "field": error.field,
                    "code": error.code
                }
                for error in validation_result.errors
            ],
            "warnings": validation_result.warnings,
            "suggestions": validation_result.suggestions,
            "validated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"配置验证失败: {e}")
        return {
            "is_valid": False,
            "errors": [
                {
                    "message": f"验证异常: {str(e)}",
                    "field": None,
                    "code": "VALIDATION_EXCEPTION"
                }
            ],
            "warnings": [],
            "suggestions": [],
            "validated_at": datetime.now().isoformat()
        }

@router.get("/stats/summary")
async def get_task_stats(
    task_manager: TaskManager = Depends(get_task_manager)
) -> Dict[str, Any]:
    """
    获取任务统计信息
    
    Args:
        task_manager: 任务管理器
        
    Returns:
        统计信息
    """
    try:
        logger.debug("获取任务统计信息")
        
        stats = await task_manager.get_task_stats()
        
        return {
            "stats": stats,
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": "获取统计信息失败",
                "error": str(e)
            }
        )

# 注意：异常处理器应该在应用级别添加，而不是路由级别
# 这里只是示例代码，实际使用时应该在main.py中添加
