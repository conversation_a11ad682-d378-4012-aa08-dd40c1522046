"""
配置管理API
提供环境配置信息给前端，支持动态配置和健康检查增强
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
import logging
import os
import httpx
import asyncio
from datetime import datetime

from app.core.config import app_config
from app.core.api_response import APIResponseBuilder
from core.exception_handlers import BusinessException

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["config"])

class ConfigResponse(BaseModel):
    """配置响应模型"""
    mindsdb_url: str = Field(..., description="MindsDB服务URL")
    enable_proxy: bool = Field(..., description="是否启用代理")
    public_domain: Optional[str] = Field(None, description="公网域名")
    web_manager_api_base_url: str = Field(..., description="Web Manager API基础URL")
    environment: str = Field(..., description="运行环境")
    version: str = Field(..., description="应用版本")
    features: Dict[str, bool] = Field(..., description="功能开关")

class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="整体状态: ok/degraded/error")
    mindsdb_connection: str = Field(..., description="MindsDB连接状态")
    mindsdb_error: Optional[str] = Field(None, description="MindsDB错误信息")
    proxy_service: str = Field(..., description="代理服务状态")
    response_time: float = Field(..., description="响应时间(秒)")
    timestamp: str = Field(..., description="检查时间")
    details: Dict[str, Any] = Field(..., description="详细信息")

@router.get("/config", response_model=ConfigResponse)
async def get_config():
    """
    获取环境配置信息
    
    返回前端所需的环境配置，包括：
    - MindsDB服务URL
    - 代理设置
    - 公网域名
    - Web Manager API基础URL
    - 环境信息和功能开关
    """
    try:
        # 从环境变量读取配置
        mindsdb_url = os.getenv("MINDSDB_URL", app_config.mindsdb_url)
        enable_proxy = os.getenv("ENABLE_PROXY", "true").lower() == "true"
        public_domain = os.getenv("PUBLIC_DOMAIN", None)
        web_manager_api_base_url = os.getenv("WEB_MANAGER_API_BASE_URL", "http://localhost:8081")
        
        # 确定运行环境
        environment = "production" if public_domain else "development"
        
        # 功能开关配置
        features = {
            "intelligent_text2sql": os.getenv("FEATURE_INTELLIGENT_TEXT2SQL", "true").lower() == "true",
            "agent_testing": os.getenv("FEATURE_AGENT_TESTING", "true").lower() == "true",
            "prediction_models": os.getenv("FEATURE_PREDICTION_MODELS", "true").lower() == "true",
            "permission_manager": os.getenv("FEATURE_PERMISSION_MANAGER", "true").lower() == "true",
            "mcp_integration": os.getenv("FEATURE_MCP_INTEGRATION", "true").lower() == "true",
            "health_monitoring": os.getenv("FEATURE_HEALTH_MONITORING", "true").lower() == "true",
            "ai_analysis": os.getenv("FEATURE_AI_ANALYSIS", "true").lower() == "true",
            # 新功能开关 - Agent技能系统真实化
            "agent_skills_real": os.getenv("FEATURE_AGENT_SKILLS_REAL", "true").lower() == "true",
            "intelligent_text2sql_v2": os.getenv("FEATURE_INTELLIGENT_TEXT2SQL_V2", "true").lower() == "true"
        }
        
        config_data = ConfigResponse(
            mindsdb_url=mindsdb_url,
            enable_proxy=enable_proxy,
            public_domain=public_domain,
            web_manager_api_base_url=web_manager_api_base_url,
            environment=environment,
            version="1.0.0",
            features=features
        )
        
        logger.info(f"配置信息获取成功: environment={environment}, proxy={enable_proxy}")
        
        return config_data
        
    except Exception as e:
        logger.error(f"获取配置信息失败: {str(e)}")
        raise BusinessException(f"获取配置信息失败: {str(e)}")

@router.get("/health", response_model=HealthCheckResponse)
async def get_health_status():
    """
    增强的健康检查
    
    检查系统各组件的健康状态：
    - MindsDB服务连接状态
    - 代理服务状态
    - 响应时间测量
    - 详细错误信息
    """
    start_time = datetime.now()
    
    try:
        # 获取配置
        mindsdb_url = os.getenv("MINDSDB_URL", app_config.mindsdb_url)
        enable_proxy = os.getenv("ENABLE_PROXY", "true").lower() == "true"
        
        # 初始化状态
        mindsdb_status = "disconnected"
        proxy_status = "operational"
        mindsdb_error = None
        overall_status = "degraded"
        
        # 检查MindsDB连接
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # 尝试连接MindsDB API
                test_url = f"{mindsdb_url}/api/projects"
                logger.info(f"正在检查MindsDB连接: {test_url}")
                
                response = await client.get(test_url)
                
                if response.status_code == 200:
                    mindsdb_status = "connected"
                    overall_status = "ok"
                    logger.info("MindsDB连接正常")
                elif response.status_code == 404:
                    # 404可能是正常的，说明服务在运行但端点不存在
                    mindsdb_status = "connected"
                    overall_status = "ok"
                    logger.info("MindsDB服务运行中（端点可能不同）")
                else:
                    mindsdb_status = "error"
                    mindsdb_error = f"MindsDB返回状态码 {response.status_code}"
                    logger.warning(f"MindsDB状态异常: {response.status_code}")
                    
        except httpx.TimeoutException:
            mindsdb_status = "timeout"
            mindsdb_error = "连接MindsDB超时"
            logger.error("MindsDB连接超时")
        except httpx.ConnectError:
            mindsdb_status = "disconnected"
            mindsdb_error = "无法连接到MindsDB服务"
            logger.error("无法连接到MindsDB服务")
        except Exception as exc:
            mindsdb_status = "error"
            mindsdb_error = f"MindsDB检查异常: {str(exc)}"
            logger.error(f"MindsDB检查异常: {str(exc)}")
        
        # 检查代理服务状态
        if enable_proxy:
            try:
                # 这里可以添加代理服务的具体检查逻辑
                # 目前假设代理服务正常运行
                proxy_status = "operational"
            except Exception as exc:
                proxy_status = "failed"
                logger.error(f"代理服务检查失败: {str(exc)}")
        else:
            proxy_status = "disabled"
        
        # 计算响应时间
        end_time = datetime.now()
        response_time = (end_time - start_time).total_seconds()
        
        # 构建详细信息
        details = {
            "mindsdb_url": mindsdb_url,
            "proxy_enabled": enable_proxy,
            "check_timestamp": start_time.isoformat(),
            "components": {
                "mindsdb": {
                    "status": mindsdb_status,
                    "url": mindsdb_url,
                    "error": mindsdb_error
                },
                "proxy": {
                    "status": proxy_status,
                    "enabled": enable_proxy
                }
            }
        }
        
        health_data = HealthCheckResponse(
            status=overall_status,
            mindsdb_connection=mindsdb_status,
            mindsdb_error=mindsdb_error,
            proxy_service=proxy_status,
            response_time=round(response_time, 3),
            timestamp=start_time.isoformat(),
            details=details
        )
        
        logger.info(f"健康检查完成: status={overall_status}, response_time={response_time:.3f}s")
        
        return health_data
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        
        # 即使检查失败，也要返回基本的健康状态
        end_time = datetime.now()
        response_time = (end_time - start_time).total_seconds()
        
        return HealthCheckResponse(
            status="error",
            mindsdb_connection="error",
            mindsdb_error=f"健康检查失败: {str(e)}",
            proxy_service="unknown",
            response_time=round(response_time, 3),
            timestamp=start_time.isoformat(),
            details={"error": str(e)}
        )

@router.get("/config/features")
async def get_feature_flags():
    """
    获取功能开关配置
    
    返回各个功能模块的启用状态
    """
    try:
        features = {
            "intelligent_text2sql": {
                "enabled": os.getenv("FEATURE_INTELLIGENT_TEXT2SQL", "true").lower() == "true",
                "description": "智能Text2SQL专业版"
            },
            "agent_testing": {
                "enabled": os.getenv("FEATURE_AGENT_TESTING", "true").lower() == "true",
                "description": "Agent测试仪表板"
            },
            "prediction_models": {
                "enabled": os.getenv("FEATURE_PREDICTION_MODELS", "true").lower() == "true",
                "description": "预测模型管理器"
            },
            "permission_manager": {
                "enabled": os.getenv("FEATURE_PERMISSION_MANAGER", "true").lower() == "true",
                "description": "Text2SQL权限管理器"
            },
            "mcp_integration": {
                "enabled": os.getenv("FEATURE_MCP_INTEGRATION", "true").lower() == "true",
                "description": "MCP集成服务"
            },
            "health_monitoring": {
                "enabled": os.getenv("FEATURE_HEALTH_MONITORING", "true").lower() == "true",
                "description": "健康监控系统"
            },
            "ai_analysis": {
                "enabled": os.getenv("FEATURE_AI_ANALYSIS", "true").lower() == "true",
                "description": "AI分析功能"
            },
            # 新功能开关 - Agent技能系统真实化
            "agent_skills_real": {
                "enabled": os.getenv("FEATURE_AGENT_SKILLS_REAL", "true").lower() == "true",
                "description": "Agent技能系统真实化 (替换前端模拟为真实MindsDB集成)"
            },
            "intelligent_text2sql_v2": {
                "enabled": os.getenv("FEATURE_INTELLIGENT_TEXT2SQL_V2", "true").lower() == "true",
                "description": "智能Text2SQL V2 (基于真实Agent查询的高级Text2SQL功能)"
            }
        }
        
        return APIResponseBuilder.success(
            data=features,
            message="功能开关配置获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取功能开关配置失败: {str(e)}")
        raise BusinessException(f"获取功能开关配置失败: {str(e)}")

@router.get("/config/environment")
async def get_environment_info():
    """
    获取环境信息
    
    返回当前运行环境的详细信息
    """
    try:
        env_info = {
            "environment": "production" if os.getenv("PUBLIC_DOMAIN") else "development",
            "host": os.getenv("HOST", "0.0.0.0"),
            "port": int(os.getenv("PORT", "8081")),
            "debug": os.getenv("DEBUG", "false").lower() == "true",
            "public_domain": os.getenv("PUBLIC_DOMAIN"),
            "mindsdb_url": os.getenv("MINDSDB_URL", app_config.mindsdb_url),
            "enable_proxy": os.getenv("ENABLE_PROXY", "true").lower() == "true",
            "log_level": os.getenv("LOG_LEVEL", "INFO"),
            "version": "1.0.0",
            "python_version": os.sys.version,
            "startup_time": datetime.now().isoformat()
        }
        
        return APIResponseBuilder.success(
            data=env_info,
            message="环境信息获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取环境信息失败: {str(e)}")
        raise BusinessException(f"获取环境信息失败: {str(e)}")
