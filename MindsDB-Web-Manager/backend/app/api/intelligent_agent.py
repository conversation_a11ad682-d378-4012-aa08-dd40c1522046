"""
智能Agent创建API
提供数据库分析和智能提示词生成功能
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any, List
import logging
from pydantic import BaseModel

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 尝试导入核心模块
try:
    from core.database_analyzer import database_analyzer
    from core.prompt_generator import prompt_generator
    CORE_MODULES_AVAILABLE = True
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"核心模块导入失败: {e}")
    database_analyzer = None
    prompt_generator = None
    CORE_MODULES_AVAILABLE = False

logger = logging.getLogger(__name__)

router = APIRouter()

def get_client():
    """获取MindsDB客户端，如果未初始化则抛出异常"""
    from core.mindsdb_client import get_mindsdb_client
    client = get_mindsdb_client()
    if not client:
        raise HTTPException(status_code=500, detail="MindsDB客户端未初始化")
    return client

class DatabaseAnalysisRequest(BaseModel):
    """数据库分析请求"""
    database: str
    purpose: Optional[str] = "数据分析"

class PromptGenerationRequest(BaseModel):
    """提示词生成请求"""
    database: str
    purpose: Optional[str] = "数据分析"
    agent_name: Optional[str] = None
    custom_requirements: Optional[str] = None

class AgentCreationRequest(BaseModel):
    """智能Agent创建请求"""
    name: str
    database: str
    purpose: Optional[str] = "数据分析"
    model: Optional[str] = "gpt-4"
    custom_requirements: Optional[str] = None
    auto_generate_prompt: bool = True

@router.get("/databases")
async def get_available_databases():
    """获取可用的数据库列表"""
    try:
        result = get_client().execute_query("SHOW DATABASES")
        
        if result and 'data' in result:
            databases = []
            for row in result['data']:
                db_name = row[0]
                # 过滤系统数据库
                if db_name not in ['information_schema', 'mindsdb', 'files', 'log']:
                    databases.append({
                        'name': db_name,
                        'type': 'user_database'
                    })
            
            return {
                'success': True,
                'data': databases,
                'message': f'成功获取{len(databases)}个可用数据库'
            }
        
        return {
            'success': False,
            'data': [],
            'message': '未找到可用数据库'
        }
        
    except Exception as e:
        logger.error(f"获取数据库列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据库列表失败: {str(e)}")

@router.post("/analyze-database")
async def analyze_database(request: DatabaseAnalysisRequest):
    """分析数据库结构"""
    try:
        if not CORE_MODULES_AVAILABLE or not database_analyzer:
            raise HTTPException(status_code=503, detail="数据库分析模块不可用")

        logger.info(f"开始分析数据库: {request.database}")

        # 执行数据库分析
        schema = await database_analyzer.analyze_database(request.database)
        
        # 转换为API响应格式
        response_data = {
            'database': schema.database,
            'tables_count': len(schema.tables),
            'relationships_count': len(schema.relationships),
            'tables': [
                {
                    'name': table.name,
                    'columns_count': len(table.columns),
                    'row_count': table.row_count,
                    'primary_keys': table.primary_keys,
                    'foreign_keys': table.foreign_keys,
                    'columns': [
                        {
                            'name': col.name,
                            'data_type': col.data_type,
                            'is_primary_key': col.is_primary_key,
                            'is_foreign_key': col.is_foreign_key
                        }
                        for col in table.columns
                    ]
                }
                for table in schema.tables
            ],
            'relationships': schema.relationships
        }
        
        return {
            'success': True,
            'data': response_data,
            'message': f'成功分析数据库 {request.database}，包含 {len(schema.tables)} 个表'
        }
        
    except Exception as e:
        logger.error(f"数据库分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据库分析失败: {str(e)}")

@router.post("/generate-prompt")
async def generate_intelligent_prompt(request: PromptGenerationRequest):
    """生成智能提示词"""
    try:
        if not CORE_MODULES_AVAILABLE or not database_analyzer or not prompt_generator:
            raise HTTPException(status_code=503, detail="提示词生成模块不可用")

        logger.info(f"开始生成提示词: {request.database}")

        # 分析数据库结构
        schema = await database_analyzer.analyze_database(request.database)

        # 生成智能提示词
        prompt = prompt_generator.generate_agent_prompt(schema, request.purpose)
        
        # 如果有自定义要求，添加到提示词中
        if request.custom_requirements:
            prompt += f"\n\n## 特殊要求\n{request.custom_requirements}"
        
        return {
            'success': True,
            'data': {
                'prompt': prompt,
                'database': schema.database,
                'tables_analyzed': len(schema.tables),
                'relationships_found': len(schema.relationships),
                'prompt_length': len(prompt)
            },
            'message': f'成功生成智能提示词，包含 {len(schema.tables)} 个表的分析'
        }
        
    except Exception as e:
        logger.error(f"生成提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成提示词失败: {str(e)}")

@router.post("/create-intelligent-agent")
async def create_intelligent_agent(request: AgentCreationRequest):
    """创建智能Agent"""
    try:
        logger.info(f"开始创建智能Agent: {request.name}")

        # 检查Agent名称是否已存在
        existing_agents = get_client().execute_query("SHOW AGENTS")
        if existing_agents and 'data' in existing_agents:
            existing_names = [row[0] for row in existing_agents['data']]
            if request.name in existing_names:
                raise HTTPException(status_code=400, detail=f"Agent名称 '{request.name}' 已存在")
        
        # 生成智能提示词
        if request.auto_generate_prompt:
            if not CORE_MODULES_AVAILABLE or not database_analyzer or not prompt_generator:
                raise HTTPException(status_code=503, detail="智能提示词生成模块不可用")

            schema = await database_analyzer.analyze_database(request.database)
            prompt = prompt_generator.generate_agent_prompt(schema, request.purpose)
            
            if request.custom_requirements:
                prompt += f"\n\n## 特殊要求\n{request.custom_requirements}"
        else:
            prompt = f"你是一个专业的{request.purpose}助手，专门帮助用户分析{request.database}数据库中的数据。"
            if request.custom_requirements:
                prompt += f"\n\n{request.custom_requirements}"
        
        # 创建Agent
        create_sql = f"""
        CREATE AGENT {request.name}
        USING
            model = '{request.model}',
            description = '{prompt[:500]}...',  -- 限制描述长度
            prompt_template = '{prompt.replace("'", "''")}'  -- 转义单引号
        """
        
        result = get_client().execute_query(create_sql)
        
        if result:
            # 获取创建的Agent详情
            try:
                agent_details = get_client().execute_query(f"SHOW AGENTS WHERE name = '{request.name}'")
                agent_detail = None
                if agent_details and 'data' in agent_details and len(agent_details['data']) > 0:
                    agent_detail = agent_details['data'][0]
            except Exception as e:
                logger.warning(f"获取Agent详情失败: {str(e)}")
                agent_detail = None

            return {
                'success': True,
                'data': {
                    'agent_name': request.name,
                    'database': request.database,
                    'model': request.model,
                    'purpose': request.purpose,
                    'prompt_generated': request.auto_generate_prompt,
                    'prompt_length': len(prompt) if request.auto_generate_prompt else 0,
                    'agent_details': agent_detail
                },
                'message': f'成功创建智能Agent: {request.name}'
            }
        else:
            raise HTTPException(status_code=500, detail="Agent创建失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建智能Agent失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建智能Agent失败: {str(e)}")

@router.get("/database/{database_name}/preview")
async def preview_database_structure(database_name: str, limit: int = Query(5, ge=1, le=20)):
    """预览数据库结构（快速版本）"""
    try:
        from core.mindsdb_client import mindsdb_client
        
        # 获取表列表
        tables_result = get_client().execute_query(f"SHOW TABLES FROM {database_name}")
        
        if not tables_result or 'data' not in tables_result:
            raise HTTPException(status_code=404, detail=f"数据库 {database_name} 不存在或无法访问")
        
        tables = [row[0] for row in tables_result['data'][:limit]]
        
        preview_data = {
            'database': database_name,
            'total_tables': len(tables_result['data']),
            'preview_tables': []
        }
        
        for table_name in tables:
            # 获取表结构
            describe_result = get_client().execute_query(f"DESCRIBE {database_name}.{table_name}")
            columns = []
            
            if describe_result and 'data' in describe_result:
                columns = [
                    {
                        'name': row[0],
                        'type': row[1] if len(row) > 1 else 'unknown'
                    }
                    for row in describe_result['data'][:10]  # 限制显示前10列
                ]
            
            # 获取行数
            try:
                count_result = get_client().execute_query(f"SELECT COUNT(*) FROM {database_name}.{table_name}")
                row_count = int(count_result['data'][0][0]) if count_result and 'data' in count_result else 0
            except:
                row_count = 0
            
            preview_data['preview_tables'].append({
                'name': table_name,
                'columns': columns,
                'row_count': row_count,
                'columns_count': len(describe_result['data']) if describe_result and 'data' in describe_result else 0
            })
        
        return {
            'success': True,
            'data': preview_data,
            'message': f'成功预览数据库 {database_name} 的结构'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览数据库结构失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预览数据库结构失败: {str(e)}")

@router.get("/prompt-templates")
async def get_prompt_templates():
    """获取提示词模板"""
    templates = {
        'data_analyst': {
            'name': '数据分析师',
            'description': '专业的数据分析和洞察助手',
            'purpose': '数据分析'
        },
        'business_analyst': {
            'name': '业务分析师',
            'description': '专注于业务指标和KPI分析',
            'purpose': '业务分析'
        },
        'sql_expert': {
            'name': 'SQL专家',
            'description': '专业的SQL查询和数据库优化助手',
            'purpose': 'SQL查询'
        },
        'report_generator': {
            'name': '报表生成器',
            'description': '自动生成数据报表和可视化建议',
            'purpose': '报表生成'
        }
    }
    
    return {
        'success': True,
        'data': templates,
        'message': f'获取到 {len(templates)} 个提示词模板'
    }
