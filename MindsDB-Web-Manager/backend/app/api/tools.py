"""
工具模块API
提供系统配置、实用工具和管理功能
"""
from fastapi import APIRouter, HTTPException, Query, Request, Body, UploadFile, File
from fastapi.responses import FileResponse
from typing import List, Dict, Any, Optional
import logging
import json
import os
import csv
import io
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.mindsdb_client import mindsdb_client

# 尝试导入核心模块
try:
    from core.models import APIResponse
    from core.api_utils import create_success_response, create_error_response
    from core.exceptions import ValidationException
    CORE_MODULES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"核心模块导入失败: {e}")
    CORE_MODULES_AVAILABLE = False

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/tools", tags=["tools"])

# 配置存储
app_config = {
    "log_level": "INFO",
    "mindsdb_url": "http://localhost:47334",
    "auto_refresh": True,
    "refresh_interval": 30,
    "theme": "light",
    "language": "zh-CN"
}

@router.get("/config", response_model=Dict[str, Any])
async def get_system_config(request: Request):
    """
    获取系统配置
    
    Returns:
        系统配置信息
    """
    try:
        logger.info("获取系统配置")
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                app_config,
                "获取系统配置成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "获取系统配置成功",
                "data": app_config
            }
        
    except Exception as e:
        logger.error(f"获取系统配置失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"获取系统配置失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"获取系统配置失败: {str(e)}",
                "data": None
            }

@router.put("/config", response_model=Dict[str, Any])
async def update_system_config(request: Request, config_data: Dict[str, Any]):
    """
    更新系统配置
    
    Args:
        config_data: 配置数据
        
    Returns:
        更新结果
    """
    try:
        logger.info("更新系统配置")
        
        # 验证配置项
        valid_keys = set(app_config.keys())
        for key in config_data:
            if key not in valid_keys:
                raise ValidationException(f"无效的配置项: {key}")
        
        # 更新配置
        app_config.update(config_data)
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                app_config,
                "系统配置更新成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "系统配置更新成功",
                "data": app_config
            }
        
    except ValidationException as e:
        logger.warning(f"配置更新验证失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(str(e), None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": str(e),
                "data": None
            }
    except Exception as e:
        logger.error(f"更新系统配置失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"更新系统配置失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"更新系统配置失败: {str(e)}",
                "data": None
            }

@router.post("/export/data", response_model=Dict[str, Any])
async def export_data(request: Request, export_config: Dict[str, Any]):
    """
    导出数据
    
    Args:
        export_config: 导出配置
        
    Returns:
        导出结果
    """
    try:
        logger.info("导出数据")
        
        # 验证必需字段
        if 'query' not in export_config:
            raise ValidationException("缺少查询语句")
        
        query = export_config['query']
        format_type = export_config.get('format', 'csv').lower()
        
        # 执行查询
        result = mindsdb_client.execute_query(query)
        
        if not result or 'data' not in result:
            raise ValidationException("查询无返回数据")
        
        data = result['data']
        columns = result.get('column_names', [])
        
        # 生成文件
        if format_type == 'csv':
            file_content = generate_csv(columns, data)
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        elif format_type == 'json':
            file_content = generate_json(columns, data)
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        else:
            raise ValidationException(f"不支持的导出格式: {format_type}")
        
        # 保存临时文件
        temp_dir = "/tmp"
        file_path = os.path.join(temp_dir, filename)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(file_content)
        
        export_result = {
            "filename": filename,
            "file_path": file_path,
            "format": format_type,
            "row_count": len(data),
            "column_count": len(columns),
            "file_size": len(file_content.encode('utf-8'))
        }
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                export_result,
                "数据导出成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "数据导出成功",
                "data": export_result
            }
        
    except ValidationException as e:
        logger.warning(f"数据导出验证失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(str(e), None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": str(e),
                "data": None
            }
    except Exception as e:
        logger.error(f"数据导出失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"数据导出失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"数据导出失败: {str(e)}",
                "data": None
            }

@router.get("/export/download/{filename}")
async def download_export_file(filename: str):
    """
    下载导出文件
    
    Args:
        filename: 文件名
        
    Returns:
        文件下载响应
    """
    try:
        logger.info(f"下载导出文件: {filename}")
        
        temp_dir = "/tmp"
        file_path = os.path.join(temp_dir, filename)
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")

@router.post("/import/data")
async def import_data(request: Request, file: UploadFile = File(...)):
    """
    导入数据
    
    Args:
        file: 上传的文件
        
    Returns:
        导入结果
    """
    try:
        logger.info(f"导入数据文件: {file.filename}")
        
        # 检查文件类型
        if not file.filename.lower().endswith(('.csv', '.json')):
            raise ValidationException("只支持CSV和JSON文件")
        
        # 读取文件内容
        content = await file.read()
        content_str = content.decode('utf-8')
        
        # 解析文件
        if file.filename.lower().endswith('.csv'):
            data = parse_csv(content_str)
        else:
            data = parse_json(content_str)
        
        import_result = {
            "filename": file.filename,
            "file_size": len(content),
            "row_count": len(data.get('rows', [])),
            "column_count": len(data.get('columns', [])),
            "preview": data.get('rows', [])[:5]  # 预览前5行
        }
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                import_result,
                "数据导入成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "数据导入成功",
                "data": import_result
            }
        
    except ValidationException as e:
        logger.warning(f"数据导入验证失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(str(e), None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": str(e),
                "data": None
            }
    except Exception as e:
        logger.error(f"数据导入失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"数据导入失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"数据导入失败: {str(e)}",
                "data": None
            }

@router.get("/utilities", response_model=Dict[str, Any])
async def get_utilities_list(request: Request):
    """
    获取可用工具列表
    
    Returns:
        工具列表
    """
    try:
        utilities = [
            {
                "id": "data_export",
                "name": "数据导出",
                "description": "将查询结果导出为CSV或JSON格式",
                "category": "数据处理"
            },
            {
                "id": "data_import",
                "name": "数据导入",
                "description": "从CSV或JSON文件导入数据",
                "category": "数据处理"
            },
            {
                "id": "config_management",
                "name": "配置管理",
                "description": "管理系统配置和设置",
                "category": "系统管理"
            },
            {
                "id": "log_viewer",
                "name": "日志查看",
                "description": "查看系统日志和错误信息",
                "category": "系统管理"
            },
            {
                "id": "connection_test",
                "name": "连接测试",
                "description": "测试MindsDB和数据源连接",
                "category": "诊断工具"
            },
            {
                "id": "performance_monitor",
                "name": "性能监控",
                "description": "监控系统性能和资源使用",
                "category": "监控工具"
            }
        ]
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                {"utilities": utilities},
                "获取工具列表成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "获取工具列表成功",
                "data": {"utilities": utilities}
            }
        
    except Exception as e:
        logger.error(f"获取工具列表失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"获取工具列表失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"获取工具列表失败: {str(e)}",
                "data": None
            }

def generate_csv(columns: List[str], data: List[List[Any]]) -> str:
    """生成CSV内容"""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # 写入列头
    writer.writerow(columns)
    
    # 写入数据
    for row in data:
        writer.writerow(row)
    
    return output.getvalue()

def generate_json(columns: List[str], data: List[List[Any]]) -> str:
    """生成JSON内容"""
    result = []
    for row in data:
        row_dict = {}
        for i, value in enumerate(row):
            if i < len(columns):
                row_dict[columns[i]] = value
        result.append(row_dict)
    
    return json.dumps(result, ensure_ascii=False, indent=2)

def parse_csv(content: str) -> Dict[str, Any]:
    """解析CSV内容"""
    reader = csv.reader(io.StringIO(content))
    rows = list(reader)
    
    if not rows:
        return {"columns": [], "rows": []}
    
    columns = rows[0]
    data_rows = rows[1:]
    
    return {"columns": columns, "rows": data_rows}

def parse_json(content: str) -> Dict[str, Any]:
    """解析JSON内容"""
    data = json.loads(content)
    
    if isinstance(data, list) and data:
        # 假设是对象数组
        columns = list(data[0].keys()) if data[0] else []
        rows = [[item.get(col, '') for col in columns] for item in data]
        return {"columns": columns, "rows": rows}
    else:
        return {"columns": [], "rows": []}
