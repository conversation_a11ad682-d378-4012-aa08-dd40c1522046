"""
认证API端点
提供用户登录、登出、注册、密码管理等功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr, Field
from typing import Optional
import logging

from core.auth_service import AuthService, get_auth_service, AuthenticationError, TokenError
from core.auth_middleware import (
    get_current_active_user, get_current_superuser, security,
    require_user_management_permission
)
from core.auth_models import User, UserRepository
from core.database import get_db
from core.api_response import APIResponseBuilder

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/auth", tags=["authentication"])

# 请求模型
class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., min_length=1, max_length=100, description="用户名或邮箱")
    password: str = Field(..., min_length=1, max_length=100, description="密码")

class RegisterRequest(BaseModel):
    """注册请求"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=6, max_length=100, description="密码")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")

class ChangePasswordRequest(BaseModel):
    """修改密码请求"""
    old_password: str = Field(..., min_length=1, max_length=100, description="原密码")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")

class ResetPasswordRequest(BaseModel):
    """重置密码请求"""
    email: EmailStr = Field(..., description="邮箱地址")

class ConfirmResetPasswordRequest(BaseModel):
    """确认重置密码请求"""
    reset_token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")

class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str = Field(..., description="刷新令牌")

# 响应模型
class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int
    user: dict

class UserResponse(BaseModel):
    """用户信息响应"""
    id: int
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    is_superuser: bool
    roles: list
    permissions: list

def get_client_info(request: Request) -> tuple:
    """获取客户端信息"""
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("User-Agent")
    return ip_address, user_agent

@router.post("/login", response_model=dict)
async def login(
    request: Request,
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """用户登录"""
    try:
        auth_service = get_auth_service(db)
        ip_address, user_agent = get_client_info(request)
        
        result = auth_service.login(
            username=login_data.username,
            password=login_data.password,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        return APIResponseBuilder.success(
            data=result,
            message="登录成功"
        ).dict()
        
    except AuthenticationError as e:
        return APIResponseBuilder.error(
            message=str(e),
            error_code="AUTHENTICATION_FAILED"
        ).dict()
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        return APIResponseBuilder.error(
            message="登录失败，请稍后重试",
            error_code="LOGIN_ERROR"
        ).dict()

@router.post("/logout")
async def logout(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """用户登出"""
    try:
        if not credentials:
            return APIResponseBuilder.error(
                message="未提供认证令牌",
                error_code="NO_TOKEN"
            ).dict()
        
        auth_service = get_auth_service(db)
        ip_address, user_agent = get_client_info(request)
        
        success = auth_service.logout(
            token=credentials.credentials,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if success:
            return APIResponseBuilder.success(
                message="登出成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message="登出失败",
                error_code="LOGOUT_FAILED"
            ).dict()
            
    except Exception as e:
        logger.error(f"登出失败: {str(e)}")
        return APIResponseBuilder.error(
            message="登出失败，请稍后重试",
            error_code="LOGOUT_ERROR"
        ).dict()

@router.post("/register")
async def register(
    register_data: RegisterRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_user_management_permission())
):
    """用户注册（需要用户管理权限）"""
    try:
        user_repo = UserRepository(db)
        
        # 检查用户名是否已存在
        existing_user = user_repo.get_by_username(register_data.username)
        if existing_user:
            return APIResponseBuilder.error(
                message="用户名已存在",
                error_code="USERNAME_EXISTS"
            ).dict()
        
        # 检查邮箱是否已存在
        existing_email = user_repo.get_by_email(register_data.email)
        if existing_email:
            return APIResponseBuilder.error(
                message="邮箱已存在",
                error_code="EMAIL_EXISTS"
            ).dict()
        
        # 创建新用户
        new_user = User(
            username=register_data.username,
            email=register_data.email,
            full_name=register_data.full_name,
            is_active=True,
            is_superuser=False
        )
        new_user.set_password(register_data.password)
        
        created_user = user_repo.create(new_user)
        
        return APIResponseBuilder.success(
            data={
                "id": created_user.id,
                "username": created_user.username,
                "email": created_user.email,
                "full_name": created_user.full_name
            },
            message="用户注册成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"用户注册失败: {str(e)}")
        return APIResponseBuilder.error(
            message="用户注册失败，请稍后重试",
            error_code="REGISTER_ERROR"
        ).dict()

@router.get("/me")
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """获取当前用户信息"""
    try:
        return APIResponseBuilder.success(
            data={
                "id": current_user.id,
                "username": current_user.username,
                "email": current_user.email,
                "full_name": current_user.full_name,
                "is_active": current_user.is_active,
                "is_superuser": current_user.is_superuser,
                "roles": [
                    {
                        "id": role.id,
                        "name": role.name,
                        "display_name": role.display_name,
                        "description": role.description
                    }
                    for role in current_user.roles
                ],
                "permissions": current_user.get_permissions(),
                "last_login": current_user.last_login.isoformat() if current_user.last_login else None
            },
            message="获取用户信息成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取用户信息失败",
            error_code="GET_USER_INFO_ERROR"
        ).dict()

@router.post("/change-password")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """修改密码"""
    try:
        auth_service = get_auth_service(db)
        
        success = auth_service.change_password(
            user_id=current_user.id,
            old_password=password_data.old_password,
            new_password=password_data.new_password
        )
        
        if success:
            return APIResponseBuilder.success(
                message="密码修改成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message="密码修改失败",
                error_code="CHANGE_PASSWORD_FAILED"
            ).dict()
            
    except AuthenticationError as e:
        return APIResponseBuilder.error(
            message=str(e),
            error_code="AUTHENTICATION_FAILED"
        ).dict()
    except Exception as e:
        logger.error(f"修改密码失败: {str(e)}")
        return APIResponseBuilder.error(
            message="修改密码失败，请稍后重试",
            error_code="CHANGE_PASSWORD_ERROR"
        ).dict()

@router.post("/reset-password")
async def reset_password(
    reset_data: ResetPasswordRequest,
    db: Session = Depends(get_db)
):
    """请求重置密码"""
    try:
        auth_service = get_auth_service(db)
        
        reset_token = auth_service.reset_password(reset_data.email)
        
        return APIResponseBuilder.success(
            data={"reset_token": reset_token},  # 实际生产中应该通过邮件发送
            message="密码重置链接已发送到您的邮箱"
        ).dict()
        
    except Exception as e:
        logger.error(f"密码重置失败: {str(e)}")
        return APIResponseBuilder.error(
            message="密码重置失败，请稍后重试",
            error_code="RESET_PASSWORD_ERROR"
        ).dict()

@router.post("/confirm-reset-password")
async def confirm_reset_password(
    confirm_data: ConfirmResetPasswordRequest,
    db: Session = Depends(get_db)
):
    """确认重置密码"""
    try:
        auth_service = get_auth_service(db)
        
        success = auth_service.confirm_password_reset(
            reset_token=confirm_data.reset_token,
            new_password=confirm_data.new_password
        )
        
        if success:
            return APIResponseBuilder.success(
                message="密码重置成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message="密码重置失败",
                error_code="CONFIRM_RESET_FAILED"
            ).dict()
            
    except AuthenticationError as e:
        return APIResponseBuilder.error(
            message=str(e),
            error_code="AUTHENTICATION_FAILED"
        ).dict()
    except Exception as e:
        logger.error(f"确认密码重置失败: {str(e)}")
        return APIResponseBuilder.error(
            message="密码重置失败，请稍后重试",
            error_code="CONFIRM_RESET_ERROR"
        ).dict()

@router.post("/refresh-token")
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        auth_service = get_auth_service(db)
        
        new_access_token = auth_service.refresh_access_token(refresh_data.refresh_token)
        
        return APIResponseBuilder.success(
            data={
                "access_token": new_access_token,
                "token_type": "bearer"
            },
            message="令牌刷新成功"
        ).dict()
        
    except AuthenticationError as e:
        return APIResponseBuilder.error(
            message=str(e),
            error_code="AUTHENTICATION_FAILED"
        ).dict()
    except Exception as e:
        logger.error(f"刷新令牌失败: {str(e)}")
        return APIResponseBuilder.error(
            message="刷新令牌失败，请稍后重试",
            error_code="REFRESH_TOKEN_ERROR"
        ).dict()

@router.get("/verify-token")
async def verify_token(
    current_user: User = Depends(get_current_active_user)
):
    """验证令牌有效性"""
    try:
        return APIResponseBuilder.success(
            data={
                "valid": True,
                "user_id": current_user.id,
                "username": current_user.username
            },
            message="令牌有效"
        ).dict()
        
    except Exception as e:
        logger.error(f"验证令牌失败: {str(e)}")
        return APIResponseBuilder.error(
            message="令牌无效",
            error_code="INVALID_TOKEN"
        ).dict()

# 用户管理API（需要管理员权限）
@router.get("/users")
async def list_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_user_management_permission()),
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    try:
        user_repo = UserRepository(db)
        users = user_repo.get_all(User, skip=skip, limit=limit)

        users_data = []
        for user in users:
            users_data.append({
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "roles": [{"id": role.id, "name": role.name, "display_name": role.display_name}
                         for role in user.roles]
            })

        return APIResponseBuilder.success(
            data={
                "users": users_data,
                "total": len(users_data),
                "skip": skip,
                "limit": limit
            },
            message="获取用户列表成功"
        ).dict()

    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取用户列表失败",
            error_code="LIST_USERS_ERROR"
        ).dict()

@router.get("/users/{user_id}")
async def get_user(
    user_id: int,
    current_user: User = Depends(require_user_management_permission()),
    db: Session = Depends(get_db)
):
    """获取指定用户信息"""
    try:
        user_repo = UserRepository(db)
        user = user_repo.get_by_id(User, user_id)

        if not user:
            return APIResponseBuilder.error(
                message="用户不存在",
                error_code="USER_NOT_FOUND"
            ).dict()

        return APIResponseBuilder.success(
            data={
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "roles": [
                    {
                        "id": role.id,
                        "name": role.name,
                        "display_name": role.display_name,
                        "description": role.description
                    }
                    for role in user.roles
                ],
                "permissions": user.get_permissions()
            },
            message="获取用户信息成功"
        ).dict()

    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取用户信息失败",
            error_code="GET_USER_ERROR"
        ).dict()

@router.put("/users/{user_id}/status")
async def update_user_status(
    user_id: int,
    is_active: bool,
    current_user: User = Depends(require_user_management_permission()),
    db: Session = Depends(get_db)
):
    """更新用户状态"""
    try:
        user_repo = UserRepository(db)
        user = user_repo.get_by_id(User, user_id)

        if not user:
            return APIResponseBuilder.error(
                message="用户不存在",
                error_code="USER_NOT_FOUND"
            ).dict()

        # 不能禁用自己
        if user.id == current_user.id and not is_active:
            return APIResponseBuilder.error(
                message="不能禁用自己的账户",
                error_code="CANNOT_DISABLE_SELF"
            ).dict()

        user.is_active = is_active
        user_repo.update(user)

        return APIResponseBuilder.success(
            data={
                "id": user.id,
                "username": user.username,
                "is_active": user.is_active
            },
            message=f"用户状态已{'启用' if is_active else '禁用'}"
        ).dict()

    except Exception as e:
        logger.error(f"更新用户状态失败: {str(e)}")
        return APIResponseBuilder.error(
            message="更新用户状态失败",
            error_code="UPDATE_USER_STATUS_ERROR"
        ).dict()
