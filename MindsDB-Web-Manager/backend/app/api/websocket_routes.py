"""
WebSocket API路由
处理WebSocket连接、消息传递和Agent实时交互
"""
import json
import logging
import asyncio
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, HTTPException
from fastapi.responses import J<PERSON>NResponse

from core.websocket_manager import websocket_manager, Message
from core.mindsdb_client import MindsDBClient
from utils.unicode_handler import UnicodeHandler
import psutil
import time

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/ws", tags=["WebSocket"])

# 初始化组件
mindsdb_client = MindsDBClient()
unicode_handler = UnicodeHandler()

@router.websocket("/connect")
async def websocket_endpoint(
    websocket: WebSocket,
    client_id: str = Query(..., description="客户端唯一标识"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    agent_name: Optional[str] = Query(None, description="订阅的Agent名称")
):
    """
    WebSocket连接端点
    支持实时双向通信、Agent订阅、消息广播
    """
    # 建立连接
    connected = await websocket_manager.connect(websocket, client_id, user_id, agent_name)
    if not connected:
        return
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message_data = json.loads(data)
                message_type = message_data.get("type", "message")
                content = message_data.get("content", "")
                target_agent = message_data.get("agent_name")
                recipient = message_data.get("recipient")
                
                logger.info(f"收到WebSocket消息: {client_id}, 类型: {message_type}")
                
                # 创建消息对象
                message = Message(
                    type=message_type,
                    content=content,
                    sender=client_id,
                    recipient=recipient,
                    agent_name=target_agent,
                    timestamp=datetime.now(),
                    message_id=f"{client_id}_{datetime.now().timestamp()}"
                )
                
                # 添加到历史记录
                websocket_manager.add_message_to_history(message)
                
                # 处理不同类型的消息
                if message_type == "heartbeat":
                    await websocket_manager.handle_heartbeat(client_id)
                    
                elif message_type == "agent_message":
                    # Agent对话消息
                    await handle_agent_message(client_id, target_agent, content, message)
                    
                elif message_type == "private_message":
                    # 私有消息
                    if recipient:
                        await websocket_manager.send_to_client(recipient, {
                            "type": "private_message",
                            "content": content,
                            "sender": client_id,
                            "timestamp": message.timestamp.isoformat(),
                            "message_id": message.message_id
                        })
                    
                elif message_type == "broadcast":
                    # 广播消息
                    await websocket_manager.broadcast_to_all({
                        "type": "broadcast",
                        "content": content,
                        "sender": client_id,
                        "timestamp": message.timestamp.isoformat(),
                        "message_id": message.message_id
                    })
                    
                elif message_type == "subscribe_agent":
                    # 订阅Agent
                    if target_agent:
                        if target_agent not in websocket_manager.agent_subscriptions:
                            websocket_manager.agent_subscriptions[target_agent] = set()
                        websocket_manager.agent_subscriptions[target_agent].add(client_id)
                        
                        await websocket_manager.send_to_client(client_id, {
                            "type": "system",
                            "content": f"已订阅Agent: {target_agent}",
                            "timestamp": datetime.now().isoformat()
                        })
                
                elif message_type == "unsubscribe_agent":
                    # 取消订阅Agent
                    if target_agent and target_agent in websocket_manager.agent_subscriptions:
                        websocket_manager.agent_subscriptions[target_agent].discard(client_id)
                        
                        await websocket_manager.send_to_client(client_id, {
                            "type": "system",
                            "content": f"已取消订阅Agent: {target_agent}",
                            "timestamp": datetime.now().isoformat()
                        })
                
                else:
                    # 普通消息
                    await websocket_manager.send_to_client(client_id, {
                        "type": "echo",
                        "content": f"收到消息: {content}",
                        "original_message": message_data,
                        "timestamp": datetime.now().isoformat()
                    })
                    
            except json.JSONDecodeError:
                await websocket_manager.send_to_client(client_id, {
                    "type": "error",
                    "content": "消息格式错误，请发送有效的JSON",
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                logger.error(f"处理WebSocket消息错误: {e}")
                await websocket_manager.send_to_client(client_id, {
                    "type": "error",
                    "content": f"处理消息时发生错误: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                })
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket客户端断开连接: {client_id}")
    except Exception as e:
        logger.error(f"WebSocket连接错误: {client_id}, 错误: {e}")
    finally:
        await websocket_manager.disconnect(client_id)

async def handle_agent_message(client_id: str, agent_name: str, content: str, message: Message):
    """处理Agent对话消息"""
    if not agent_name:
        await websocket_manager.send_to_client(client_id, {
            "type": "error",
            "content": "请指定Agent名称",
            "timestamp": datetime.now().isoformat()
        })
        return
    
    try:
        # 发送"正在思考"状态
        await websocket_manager.send_to_client(client_id, {
            "type": "agent_status",
            "content": "Agent正在思考中...",
            "agent_name": agent_name,
            "status": "thinking",
            "timestamp": datetime.now().isoformat()
        })
        
        # 调用MindsDB Agent
        query = f'SELECT * FROM {agent_name} WHERE question = "{content}"'
        response = await asyncio.to_thread(mindsdb_client.execute_query, query)
        
        if response and response.get("data"):
            # 处理Agent响应
            agent_response = response["data"][0][0] if response["data"] else "Agent没有返回响应"
            
            # Unicode解码（带错误处理）
            try:
                decoded_response = unicode_handler.decode_unicode_string(agent_response)
            except Exception as decode_error:
                logger.warning(f"Unicode解码失败: {decode_error}, 使用原始响应")
                decoded_response = str(agent_response)
            
            # 发送Agent响应给发送者
            await websocket_manager.send_to_client(client_id, {
                "type": "agent_response",
                "content": decoded_response,
                "agent_name": agent_name,
                "question": content,
                "timestamp": datetime.now().isoformat(),
                "message_id": message.message_id
            })
            
            # 广播给Agent的所有订阅者（除了发送者）
            if agent_name in websocket_manager.agent_subscriptions:
                subscribers = websocket_manager.agent_subscriptions[agent_name].copy()
                subscribers.discard(client_id)  # 排除发送者
                
                for subscriber_id in subscribers:
                    await websocket_manager.send_to_client(subscriber_id, {
                        "type": "agent_broadcast",
                        "content": decoded_response,
                        "agent_name": agent_name,
                        "question": content,
                        "sender": client_id,
                        "timestamp": datetime.now().isoformat(),
                        "message_id": message.message_id
                    })
        else:
            await websocket_manager.send_to_client(client_id, {
                "type": "error",
                "content": f"Agent {agent_name} 响应失败",
                "timestamp": datetime.now().isoformat()
            })
            
    except Exception as e:
        logger.error(f"Agent消息处理错误: {e}")
        await websocket_manager.send_to_client(client_id, {
            "type": "error",
            "content": f"Agent响应错误: {str(e)}",
            "timestamp": datetime.now().isoformat()
        })

@router.get("/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计信息"""
    try:
        stats = websocket_manager.get_connection_stats()
        return JSONResponse(content={
            "success": True,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取WebSocket统计信息错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/connections")
async def get_active_connections():
    """获取活跃连接列表"""
    try:
        connections = []
        for client_id, conn_info in websocket_manager.active_connections.items():
            connections.append({
                "client_id": client_id,
                "user_id": conn_info.user_id,
                "agent_name": conn_info.agent_name,
                "connected_at": conn_info.connected_at.isoformat(),
                "last_activity": conn_info.last_activity.isoformat()
            })
        
        return JSONResponse(content={
            "success": True,
            "data": connections,
            "total": len(connections),
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取活跃连接错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/broadcast")
async def broadcast_message(message: dict):
    """通过HTTP API发送广播消息"""
    try:
        await websocket_manager.broadcast_to_all({
            "type": "system_broadcast",
            "content": message.get("content", ""),
            "sender": "system",
            "timestamp": datetime.now().isoformat()
        })
        
        return JSONResponse(content={
            "success": True,
            "message": "广播消息已发送",
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"广播消息错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.websocket("/monitor")
async def monitor_websocket(
    websocket: WebSocket,
    client_id: str = Query(..., description="客户端唯一标识"),
    monitor_type: str = Query("system", description="监控类型: system, dashboard, performance")
):
    """
    实时监控WebSocket端点
    支持系统监控、仪表板数据、性能指标的实时推送
    """
    await websocket.accept()
    logger.info(f"监控WebSocket连接建立: {client_id}, 类型: {monitor_type}")

    try:
        # 添加到监控连接列表
        await websocket_manager.add_monitor_connection(websocket, client_id, monitor_type)

        # 发送初始数据
        await send_initial_monitor_data(websocket, monitor_type)

        # 启动监控循环
        monitor_task = asyncio.create_task(monitor_loop(websocket, monitor_type, client_id))

        # 监听客户端消息
        while True:
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)

                if message_data.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }))
                elif message_data.get("type") == "change_monitor":
                    # 更改监控类型
                    new_type = message_data.get("monitor_type", "system")
                    monitor_type = new_type
                    await send_initial_monitor_data(websocket, monitor_type)

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"监控WebSocket消息处理错误: {e}")
                break

    except WebSocketDisconnect:
        logger.info(f"监控WebSocket连接断开: {client_id}")
    except Exception as e:
        logger.error(f"监控WebSocket错误: {e}")
    finally:
        # 清理连接
        await websocket_manager.remove_monitor_connection(client_id)
        if 'monitor_task' in locals():
            monitor_task.cancel()

async def send_initial_monitor_data(websocket: WebSocket, monitor_type: str):
    """发送初始监控数据"""
    try:
        if monitor_type == "system":
            data = await get_system_monitor_data()
        elif monitor_type == "dashboard":
            data = await get_dashboard_monitor_data()
        elif monitor_type == "performance":
            data = await get_performance_monitor_data()
        else:
            data = {"error": "未知监控类型"}

        await websocket.send_text(json.dumps({
            "type": "initial_data",
            "monitor_type": monitor_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }))
    except Exception as e:
        logger.error(f"发送初始监控数据失败: {e}")

async def monitor_loop(websocket: WebSocket, monitor_type: str, client_id: str):
    """监控数据推送循环"""
    try:
        while True:
            await asyncio.sleep(5)  # 每5秒推送一次

            try:
                if monitor_type == "system":
                    data = await get_system_monitor_data()
                elif monitor_type == "dashboard":
                    data = await get_dashboard_monitor_data()
                elif monitor_type == "performance":
                    data = await get_performance_monitor_data()
                else:
                    continue

                await websocket.send_text(json.dumps({
                    "type": "monitor_update",
                    "monitor_type": monitor_type,
                    "data": data,
                    "timestamp": datetime.now().isoformat()
                }))

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"监控数据推送失败: {e}")
                break

    except asyncio.CancelledError:
        logger.info(f"监控循环已取消: {client_id}")
    except Exception as e:
        logger.error(f"监控循环错误: {e}")

async def get_system_monitor_data():
    """获取系统监控数据"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)

        # 内存使用情况
        memory = psutil.virtual_memory()

        # 磁盘使用情况
        disk = psutil.disk_usage('/')

        # 网络统计
        network = psutil.net_io_counters()

        return {
            "cpu": {
                "usage_percent": cpu_percent,
                "core_count": psutil.cpu_count()
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv
            },
            "processes": {
                "count": len(psutil.pids())
            }
        }
    except Exception as e:
        logger.error(f"获取系统监控数据失败: {e}")
        return {"error": str(e)}

async def get_dashboard_monitor_data():
    """获取仪表板监控数据"""
    try:
        # 获取MindsDB状态
        try:
            result = mindsdb_client.execute_query("SELECT VERSION()")
            mindsdb_status = "connected"
            mindsdb_version = result.get('data', [['Unknown']])[0][0] if result.get('data') else "Unknown"
        except:
            mindsdb_status = "disconnected"
            mindsdb_version = None

        # 获取数据库统计
        try:
            result = mindsdb_client.execute_query("SHOW DATABASES")
            databases = result.get('data', []) if result else []
            database_count = len(databases)
        except:
            database_count = 0

        # 获取Agent统计
        try:
            result = mindsdb_client.execute_query("SHOW AGENTS")
            agents = result.get('data', []) if result else []
            agent_count = len(agents)
        except:
            agent_count = 0

        # 获取模型统计
        try:
            result = mindsdb_client.execute_query("SHOW MODELS")
            models = result.get('data', []) if result else []
            model_count = len(models)
        except:
            model_count = 0

        return {
            "mindsdb": {
                "status": mindsdb_status,
                "version": mindsdb_version
            },
            "statistics": {
                "databases": database_count,
                "agents": agent_count,
                "models": model_count,
                "connections": len(websocket_manager.active_connections)
            }
        }
    except Exception as e:
        logger.error(f"获取仪表板监控数据失败: {e}")
        return {"error": str(e)}

async def get_performance_monitor_data():
    """获取性能监控数据"""
    try:
        # 获取详细的性能数据
        cpu_times = psutil.cpu_times()
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()

        return {
            "cpu": {
                "usage_percent": psutil.cpu_percent(interval=1),
                "times": {
                    "user": cpu_times.user,
                    "system": cpu_times.system,
                    "idle": cpu_times.idle
                },
                "load_average": list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else None
            },
            "memory": {
                "virtual": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "swap": {
                    "total": swap.total,
                    "used": swap.used,
                    "percent": swap.percent
                }
            },
            "io": {
                "disk": dict(psutil.disk_io_counters()._asdict()) if psutil.disk_io_counters() else {},
                "network": dict(psutil.net_io_counters()._asdict()) if psutil.net_io_counters() else {}
            }
        }
    except Exception as e:
        logger.error(f"获取性能监控数据失败: {e}")
        return {"error": str(e)}
