"""
PostgreSQL数据管理API端点
提供PostgreSQL数据提取和管理的REST API接口
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import logging
from datetime import datetime

from ..core.postgres_data_manager import PostgreSQLDataManager, DataManagerConfig
from ..core.postgres_ssh_connector import PostgreSQLConfig
from ..core.ssh_tunnel_manager import SSHConfig

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/postgres", tags=["PostgreSQL数据管理"])

# Pydantic模型
class PostgreSQLConfigModel(BaseModel):
    host: str = "localhost"
    port: int = 5432
    database: str
    username: str
    password: str

class SSHConfigModel(BaseModel):
    hostname: str
    port: int = 22
    username: str
    password: Optional[str] = None
    key_filename: Optional[str] = None
    timeout: int = 30

class DataManagerConfigModel(BaseModel):
    max_concurrent_jobs: int = 3
    default_batch_size: int = 10000
    max_memory_usage_mb: int = 1000
    connection_timeout: int = 30
    enable_type_conversion: bool = True
    enable_progress_tracking: bool = True
    auto_cleanup_jobs: bool = True
    job_retention_hours: int = 24

class ConnectionRequest(BaseModel):
    postgres_config: PostgreSQLConfigModel
    ssh_config: Optional[SSHConfigModel] = None
    manager_config: Optional[DataManagerConfigModel] = None

class ExtractionJobRequest(BaseModel):
    table_name: str
    schema_name: str = "public"
    batch_size: Optional[int] = None
    method: str = "auto"

class TableDataRequest(BaseModel):
    table_name: str
    schema_name: str = "public"
    limit: Optional[int] = None
    where_clause: Optional[str] = None
    order_by: Optional[str] = None

# 全局数据管理器实例
_data_managers: Dict[str, PostgreSQLDataManager] = {}

def get_or_create_manager(connection_id: str, request: ConnectionRequest) -> PostgreSQLDataManager:
    """获取或创建数据管理器"""
    if connection_id not in _data_managers:
        # 转换配置
        postgres_config = PostgreSQLConfig(**request.postgres_config.dict())
        
        ssh_config = None
        if request.ssh_config:
            ssh_config = SSHConfig(**request.ssh_config.dict())
        
        manager_config = None
        if request.manager_config:
            manager_config = DataManagerConfig(**request.manager_config.dict())
        
        # 创建管理器
        _data_managers[connection_id] = PostgreSQLDataManager(
            postgres_config=postgres_config,
            ssh_config=ssh_config,
            config=manager_config
        )
    
    return _data_managers[connection_id]

@router.post("/connections/{connection_id}/test", summary="测试数据库连接")
async def test_connection(connection_id: str, request: ConnectionRequest) -> Dict[str, Any]:
    """测试PostgreSQL数据库连接"""
    try:
        manager = get_or_create_manager(connection_id, request)
        result = manager.test_connection()
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"测试连接失败: {connection_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/connections/{connection_id}/database/info", summary="获取数据库信息")
async def get_database_info(connection_id: str) -> Dict[str, Any]:
    """获取数据库基本信息"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        info = manager.get_database_info()
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": info,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据库信息失败: {connection_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/connections/{connection_id}/schemas", summary="列出数据库模式")
async def list_schemas(connection_id: str) -> Dict[str, Any]:
    """列出数据库中的所有模式"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        schemas = manager.list_schemas()
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": schemas,
            "count": len(schemas),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出模式失败: {connection_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/connections/{connection_id}/tables", summary="列出数据库表")
async def list_tables(connection_id: str, schema: Optional[str] = None) -> Dict[str, Any]:
    """列出数据库中的表"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        tables = manager.list_tables(schema)
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": tables,
            "count": len(tables),
            "schema": schema,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出表失败: {connection_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/connections/{connection_id}/tables/{table_name}/schema", summary="获取表结构")
async def get_table_schema(connection_id: str, table_name: str, schema: str = "public") -> Dict[str, Any]:
    """获取表的详细结构信息"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        table_schema = manager.get_table_schema(table_name, schema)
        
        # 转换为字典格式
        schema_dict = {
            "table_name": table_schema.table_name,
            "schema_name": table_schema.schema_name,
            "table_type": table_schema.table_type,
            "columns": table_schema.columns,
            "primary_keys": table_schema.primary_keys,
            "indexes": table_schema.indexes,
            "constraints": table_schema.constraints,
            "row_count": table_schema.row_count,
            "table_size": table_schema.table_size
        }
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": schema_dict,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取表结构失败: {connection_id}.{schema}.{table_name} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/connections/{connection_id}/tables/data", summary="提取表数据")
async def extract_table_data(connection_id: str, request: TableDataRequest) -> Dict[str, Any]:
    """提取表数据（适用于小表或有限数据）"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        df = manager.extract_table_data(
            table_name=request.table_name,
            schema=request.schema_name,
            limit=request.limit,
            where_clause=request.where_clause,
            order_by=request.order_by
        )
        
        # 转换为JSON格式
        data = df.to_dict('records')
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": data,
            "row_count": len(data),
            "columns": list(df.columns),
            "table_name": request.table_name,
            "schema_name": request.schema_name,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提取表数据失败: {connection_id}.{request.schema_name}.{request.table_name} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/connections/{connection_id}/jobs", summary="创建数据提取任务")
async def create_extraction_job(connection_id: str, request: ExtractionJobRequest) -> Dict[str, Any]:
    """创建大表数据提取任务"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        job_id = manager.create_extraction_job(
            table_name=request.table_name,
            schema=request.schema_name,
            batch_size=request.batch_size,
            method=request.method
        )
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "job_id": job_id,
            "message": "数据提取任务已创建",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建提取任务失败: {connection_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/connections/{connection_id}/jobs", summary="列出提取任务")
async def list_extraction_jobs(connection_id: str, status: Optional[str] = None) -> Dict[str, Any]:
    """列出数据提取任务"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        jobs = manager.list_jobs(status)
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": jobs,
            "count": len(jobs),
            "status_filter": status,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出提取任务失败: {connection_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/connections/{connection_id}/jobs/{job_id}", summary="获取任务状态")
async def get_job_status(connection_id: str, job_id: str) -> Dict[str, Any]:
    """获取数据提取任务状态"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        job_status = manager.get_job_status(job_id)
        
        if job_status is None:
            raise HTTPException(status_code=404, detail=f"任务不存在: {job_id}")
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": job_status,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {connection_id}.{job_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/connections/{connection_id}/jobs/{job_id}", summary="取消提取任务")
async def cancel_extraction_job(connection_id: str, job_id: str) -> Dict[str, Any]:
    """取消数据提取任务"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        success = manager.cancel_job(job_id)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"任务不存在或无法取消: {job_id}")
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "job_id": job_id,
            "message": "任务已取消",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {connection_id}.{job_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/connections/{connection_id}/stats", summary="获取管理器统计")
async def get_manager_stats(connection_id: str) -> Dict[str, Any]:
    """获取数据管理器统计信息"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        stats = manager.get_manager_stats()
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取管理器统计失败: {connection_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/connections/{connection_id}", summary="关闭连接")
async def close_connection(connection_id: str) -> Dict[str, Any]:
    """关闭数据库连接和管理器"""
    try:
        if connection_id not in _data_managers:
            raise HTTPException(status_code=404, detail=f"连接不存在: {connection_id}")
        
        manager = _data_managers[connection_id]
        manager.shutdown()
        del _data_managers[connection_id]
        
        return {
            "status": "success",
            "connection_id": connection_id,
            "message": "连接已关闭",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"关闭连接失败: {connection_id} - {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health", summary="PostgreSQL服务健康检查")
async def postgres_health_check() -> Dict[str, Any]:
    """PostgreSQL数据管理服务健康检查"""
    try:
        active_connections = len(_data_managers)
        
        return {
            "status": "healthy",
            "active_connections": active_connections,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"PostgreSQL健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
