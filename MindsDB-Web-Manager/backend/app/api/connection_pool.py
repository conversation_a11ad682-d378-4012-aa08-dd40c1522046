"""
连接池管理API
提供连接池状态监控和管理功能
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging
from core.mindsdb_client import MindsDBClient
from core.mindsdb_connection_pool import get_connection_pool, close_connection_pool
from core.config import app_config
from core.api_response import APIResponseBuilder
from core.exception_handlers import MindsDBException

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/connection-pool", tags=["connection-pool"])

@router.get("/status")
async def get_pool_status():
    """
    获取连接池状态

    Returns:
        连接池状态信息
    """
    try:
        client = MindsDBClient()
        stats = client.get_pool_stats()

        data = {
            "pool_stats": stats,
            "config": {
                "base_url": app_config.mindsdb_url,
                "max_connections": app_config.max_connections,
                "min_connections": app_config.min_connections,
                "connection_timeout": app_config.connection_timeout,
                "idle_timeout": app_config.idle_timeout,
                "health_check_interval": app_config.health_check_interval
            }
        }

        return APIResponseBuilder.success(
            data=data,
            message="连接池状态获取成功"
        ).dict()
    except Exception as e:
        logger.error(f"获取连接池状态失败: {str(e)}")
        raise MindsDBException(f"获取连接池状态失败: {str(e)}")

@router.post("/test")
async def test_connection() -> Dict[str, Any]:
    """
    测试连接池连接
    
    Returns:
        测试结果
    """
    try:
        client = MindsDBClient()
        result = client.execute_query("SELECT 1 as test")
        
        return {
            "status": "success",
            "message": "连接测试成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"连接测试失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"连接测试失败: {str(e)}")

@router.post("/health-check")
async def force_health_check() -> Dict[str, Any]:
    """
    强制执行健康检查
    
    Returns:
        健康检查结果
    """
    try:
        pool = get_connection_pool()
        pool._perform_health_check()
        
        stats = pool.get_pool_stats()
        
        return {
            "status": "success",
            "message": "健康检查完成",
            "data": stats
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")

@router.get("/connections")
async def get_connections_info() -> Dict[str, Any]:
    """
    获取所有连接的详细信息
    
    Returns:
        连接详细信息
    """
    try:
        pool = get_connection_pool()
        
        connections_info = []
        for conn_id, connection in pool._all_connections.items():
            connections_info.append({
                "connection_id": conn_id,
                "created_at": connection.info.created_at,
                "last_used": connection.info.last_used,
                "is_healthy": connection.info.is_healthy,
                "use_count": connection.info.use_count,
                "idle_time": connection.info.last_used - connection.info.created_at if connection.info.last_used else 0
            })
        
        return {
            "status": "success",
            "data": {
                "connections": connections_info,
                "total_count": len(connections_info)
            }
        }
    except Exception as e:
        logger.error(f"获取连接信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取连接信息失败: {str(e)}")

@router.post("/reset")
async def reset_connection_pool() -> Dict[str, Any]:
    """
    重置连接池（谨慎使用）
    
    Returns:
        重置结果
    """
    try:
        # 关闭现有连接池
        close_connection_pool()
        
        # 创建新的连接池
        config = app_config.get_connection_config()
        new_pool = get_connection_pool(config)
        
        stats = new_pool.get_pool_stats()
        
        return {
            "status": "success",
            "message": "连接池已重置",
            "data": stats
        }
    except Exception as e:
        logger.error(f"重置连接池失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置连接池失败: {str(e)}")

@router.get("/metrics")
async def get_pool_metrics() -> Dict[str, Any]:
    """
    获取连接池性能指标
    
    Returns:
        性能指标
    """
    try:
        pool = get_connection_pool()
        stats = pool.get_pool_stats()
        
        # 计算额外的性能指标
        metrics = {
            "pool_efficiency": (stats["healthy_connections"] / stats["total_connections"]) * 100 if stats["total_connections"] > 0 else 0,
            "connection_reuse_rate": stats["total_use_count"] / stats["total_connections"] if stats["total_connections"] > 0 else 0,
            "pool_saturation": stats["pool_utilization"],
            "available_capacity": stats["max_connections"] - stats["active_connections"],
            "health_ratio": (stats["healthy_connections"] / stats["total_connections"]) * 100 if stats["total_connections"] > 0 else 0
        }
        
        return {
            "status": "success",
            "data": {
                "basic_stats": stats,
                "performance_metrics": metrics
            }
        }
    except Exception as e:
        logger.error(f"获取性能指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")
