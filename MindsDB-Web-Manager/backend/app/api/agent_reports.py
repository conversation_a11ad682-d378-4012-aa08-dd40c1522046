"""
Agent测试报告API
提供Agent测试报告的生成、查询和管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta
import logging
import uuid
import asyncio

from core.auth_middleware import get_current_active_user
from core.auth_models import User
from core.database import get_db
from core.api_response import APIResponseBuilder
from core.agent_report_models import (
    AgentTestReport, ReportTemplate, AgentReportRepository, 
    ReportType, ReportStatus, PerformanceMetrics
)
from core.report_generator import ReportGenerator, ReportGenerationConfig, get_report_generator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/agent-reports", tags=["agent-reports"])

# 请求模型
class ReportGenerationRequest(BaseModel):
    """报告生成请求"""
    agent_id: str = Field(..., min_length=1, max_length=100, description="Agent ID")
    report_type: str = Field(ReportType.SUMMARY.value, description="报告类型")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    test_run_id: Optional[int] = Field(None, description="测试运行ID")
    test_suite_id: Optional[int] = Field(None, description="测试套件ID")
    include_charts: bool = Field(True, description="是否包含图表")
    include_recommendations: bool = Field(True, description="是否包含建议")
    template_id: Optional[str] = Field(None, description="模板ID")
    custom_metrics: Optional[List[str]] = Field(None, description="自定义指标")

    @validator('report_type')
    def validate_report_type(cls, v):
        valid_types = [t.value for t in ReportType]
        if v not in valid_types:
            raise ValueError(f"报告类型必须是: {', '.join(valid_types)}")
        return v

class ReportTemplateRequest(BaseModel):
    """报告模板请求"""
    name: str = Field(..., min_length=1, max_length=200, description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")
    report_type: str = Field(..., description="报告类型")
    template_config: Dict[str, Any] = Field(..., description="模板配置")
    chart_configs: Optional[Dict[str, Any]] = Field(None, description="图表配置")
    metrics_config: Optional[Dict[str, Any]] = Field(None, description="指标配置")
    is_default: bool = Field(False, description="是否为默认模板")

# 响应模型
class ReportSummaryResponse(BaseModel):
    """报告摘要响应"""
    id: int
    report_id: str
    title: str
    report_type: str
    status: str
    agent_id: str
    generated_at: Optional[str]
    total_tests: int
    success_rate: float

class ReportDetailResponse(BaseModel):
    """报告详情响应"""
    id: int
    report_id: str
    title: str
    description: Optional[str]
    report_type: str
    status: str
    agent_id: str
    agent_name: Optional[str]
    report_period_start: str
    report_period_end: str
    generated_at: Optional[str]
    total_tests: int
    passed_tests: int
    failed_tests: int
    skipped_tests: int
    error_tests: int
    performance_metrics: Optional[Dict[str, Any]]
    summary: Optional[str]
    detailed_analysis: Optional[Dict[str, Any]]
    recommendations: Optional[List[Dict[str, Any]]]
    charts_data: Optional[Dict[str, Any]]

# API端点实现
@router.post("/generate", response_model=dict)
async def generate_report(
    request: ReportGenerationRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """生成测试报告"""
    try:
        # 创建报告生成配置
        config = ReportGenerationConfig(
            agent_id=request.agent_id,
            report_type=request.report_type,
            start_time=request.start_time,
            end_time=request.end_time,
            test_run_id=request.test_run_id,
            test_suite_id=request.test_suite_id,
            include_charts=request.include_charts,
            include_recommendations=request.include_recommendations,
            template_id=request.template_id,
            custom_metrics=request.custom_metrics
        )
        
        # 获取报告生成器并生成报告
        generator = get_report_generator()
        report_id = await generator.generate_report(config)
        
        return APIResponseBuilder.success(
            data={
                "report_id": report_id,
                "status": "generating",
                "message": "报告生成已启动，请稍后查询结果"
            },
            message="报告生成请求已提交"
        ).dict()
        
    except Exception as e:
        logger.error(f"生成报告失败: {str(e)}")
        return APIResponseBuilder.error(
            message="生成报告失败",
            error_code="GENERATE_REPORT_ERROR"
        ).dict()

@router.get("/reports", response_model=dict)
async def get_reports(
    agent_id: Optional[str] = Query(None, description="Agent ID"),
    report_type: Optional[str] = Query(None, description="报告类型"),
    status: Optional[str] = Query(None, description="报告状态"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取报告列表"""
    try:
        repo = AgentReportRepository(db)
        reports = repo.get_reports(
            agent_id=agent_id,
            report_type=report_type,
            status=status,
            start_time=start_time,
            end_time=end_time,
            limit=limit,
            offset=offset
        )
        
        reports_data = []
        for report in reports:
            reports_data.append({
                "id": report.id,
                "report_id": report.report_id,
                "title": report.title,
                "report_type": report.report_type,
                "status": report.status,
                "agent_id": report.agent_id,
                "agent_name": report.agent_name,
                "generated_at": report.generated_at.isoformat() if report.generated_at else None,
                "total_tests": report.total_tests,
                "success_rate": report.success_rate,
                "report_period_start": report.report_period_start.isoformat() if report.report_period_start else None,
                "report_period_end": report.report_period_end.isoformat() if report.report_period_end else None
            })
        
        return APIResponseBuilder.success(
            data={
                "reports": reports_data,
                "total": len(reports_data),
                "limit": limit,
                "offset": offset
            },
            message="获取报告列表成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取报告列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取报告列表失败",
            error_code="GET_REPORTS_ERROR"
        ).dict()

@router.get("/reports/{report_id}", response_model=dict)
async def get_report_detail(
    report_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取报告详情"""
    try:
        repo = AgentReportRepository(db)
        report = repo.get_report_by_id(report_id)
        
        if not report:
            return APIResponseBuilder.error(
                message="报告不存在",
                error_code="REPORT_NOT_FOUND"
            ).dict()
        
        return APIResponseBuilder.success(
            data=report.to_dict(),
            message="获取报告详情成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取报告详情失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取报告详情失败",
            error_code="GET_REPORT_ERROR"
        ).dict()

@router.delete("/reports/{report_id}", response_model=dict)
async def delete_report(
    report_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除报告"""
    try:
        repo = AgentReportRepository(db)
        success = repo.delete_report(report_id)
        
        if not success:
            return APIResponseBuilder.error(
                message="报告不存在",
                error_code="REPORT_NOT_FOUND"
            ).dict()
        
        return APIResponseBuilder.success(
            data={"report_id": report_id},
            message="报告删除成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"删除报告失败: {str(e)}")
        return APIResponseBuilder.error(
            message="删除报告失败",
            error_code="DELETE_REPORT_ERROR"
        ).dict()

@router.get("/statistics", response_model=dict)
async def get_report_statistics(
    agent_id: Optional[str] = Query(None, description="Agent ID"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取报告统计信息"""
    try:
        repo = AgentReportRepository(db)
        stats = repo.get_report_statistics(
            agent_id=agent_id,
            start_time=start_time,
            end_time=end_time
        )
        
        return APIResponseBuilder.success(
            data=stats,
            message="获取报告统计成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取报告统计失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取报告统计失败",
            error_code="GET_STATISTICS_ERROR"
        ).dict()

# 报告模板相关API
@router.post("/templates", response_model=dict)
async def create_template(
    request: ReportTemplateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建报告模板"""
    try:
        repo = AgentReportRepository(db)
        
        template_data = request.dict()
        template = repo.create_template(template_data)
        
        return APIResponseBuilder.success(
            data={
                "id": template.id,
                "template_id": template.template_id,
                "name": template.name,
                "report_type": template.report_type
            },
            message="报告模板创建成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"创建报告模板失败: {str(e)}")
        return APIResponseBuilder.error(
            message="创建报告模板失败",
            error_code="CREATE_TEMPLATE_ERROR"
        ).dict()

@router.get("/templates", response_model=dict)
async def get_templates(
    report_type: Optional[str] = Query(None, description="报告类型"),
    is_active: bool = Query(True, description="是否活跃"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取报告模板列表"""
    try:
        repo = AgentReportRepository(db)
        templates = repo.get_templates(report_type=report_type, is_active=is_active)
        
        templates_data = []
        for template in templates:
            templates_data.append({
                "id": template.id,
                "template_id": template.template_id,
                "name": template.name,
                "description": template.description,
                "report_type": template.report_type,
                "version": template.version,
                "is_active": template.is_active,
                "is_default": template.is_default,
                "created_at": template.created_at.isoformat() if template.created_at else None
            })
        
        return APIResponseBuilder.success(
            data={
                "templates": templates_data,
                "total": len(templates_data)
            },
            message="获取报告模板列表成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取报告模板列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取报告模板列表失败",
            error_code="GET_TEMPLATES_ERROR"
        ).dict()

@router.get("/templates/{template_id}", response_model=dict)
async def get_template_detail(
    template_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取报告模板详情"""
    try:
        repo = AgentReportRepository(db)
        template = repo.get_template_by_id(template_id)
        
        if not template:
            return APIResponseBuilder.error(
                message="模板不存在",
                error_code="TEMPLATE_NOT_FOUND"
            ).dict()
        
        return APIResponseBuilder.success(
            data={
                "id": template.id,
                "template_id": template.template_id,
                "name": template.name,
                "description": template.description,
                "report_type": template.report_type,
                "version": template.version,
                "template_config": template.template_config,
                "chart_configs": template.chart_configs,
                "metrics_config": template.metrics_config,
                "is_active": template.is_active,
                "is_default": template.is_default,
                "created_at": template.created_at.isoformat() if template.created_at else None,
                "updated_at": template.updated_at.isoformat() if template.updated_at else None
            },
            message="获取模板详情成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取模板详情失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取模板详情失败",
            error_code="GET_TEMPLATE_ERROR"
        ).dict()
