"""
表结构分析与Agent技能集成API - 任务#119
提供表结构深度分析，并自动集成到Agent的prompt_template和技能配置中
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import logging
import time
import json
import asyncio
from datetime import datetime

# 导入核心模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.mindsdb_client import get_mindsdb_client
    from core.api_response import APIResponseBuilder
    from core.exception_handlers import BusinessException
    from utils.unicode_handler import unicode_handler
    from api.enhanced_agents import update_agent_prompt_template
    from api.enhanced_skills import create_skill, update_skill_tables
except ImportError as e:
    logging.warning(f"导入模块失败: {e}")
    
    # 提供模拟实现
    def get_mindsdb_client():
        return None
    
    class MockAPIResponseBuilder:
        @staticmethod
        def success(data=None, message=""):
            return {"success": True, "data": data, "message": message}
        
        @staticmethod
        def error(message="", error_code="", details=None):
            return {"success": False, "message": message, "error_code": error_code, "details": details}
    
    class MockBusinessException(Exception):
        pass
    
    class MockUnicodeHandler:
        def decode_response_data(self, data):
            return data
    
    APIResponseBuilder = MockAPIResponseBuilder
    BusinessException = MockBusinessException
    unicode_handler = MockUnicodeHandler()
    
    # 模拟函数
    async def update_agent_prompt_template(agent_name, prompt_template):
        pass
    
    async def create_skill(skill_data):
        pass
    
    async def update_skill_tables(skill_name, update_data):
        pass

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/table-analysis", tags=["table-analysis"])

# 功能开关检查
def check_table_analysis_features_enabled():
    """检查表分析功能是否启用"""
    agent_skills_real = os.getenv("FEATURE_AGENT_SKILLS_REAL", "false").lower() == "true"
    intelligent_text2sql = os.getenv("FEATURE_INTELLIGENT_TEXT2SQL", "false").lower() == "true"
    
    if not (agent_skills_real and intelligent_text2sql):
        raise HTTPException(
            status_code=503,
            detail="表分析功能未启用，请联系管理员启用相关功能开关"
        )

# 请求模型
class TableAnalysisRequest(BaseModel):
    """表分析请求模型"""
    database: str = Field(..., description="数据库名称")
    tables: List[str] = Field(..., description="要分析的表列表")
    include_sample_data: bool = Field(default=True, description="是否包含样本数据")
    include_relationships: bool = Field(default=True, description="是否分析表关系")
    sample_size: int = Field(default=5, description="样本数据行数", ge=1, le=100)

class AgentSkillIntegrationRequest(BaseModel):
    """Agent技能集成请求模型"""
    database: str = Field(..., description="数据库名称")
    tables: List[str] = Field(..., description="要分析的表列表")
    agent_name: Optional[str] = Field(None, description="Agent名称（可选，如果不提供会自动创建）")
    skill_name: Optional[str] = Field(None, description="技能名称（可选，如果不提供会自动创建）")
    update_prompt_template: bool = Field(default=True, description="是否更新Agent的prompt_template")
    update_skill_config: bool = Field(default=True, description="是否更新技能配置")
    include_sample_data: bool = Field(default=True, description="是否在prompt中包含样本数据")

class BatchTableAnalysisRequest(BaseModel):
    """批量表分析请求模型"""
    database: str = Field(..., description="数据库名称")
    agent_name: Optional[str] = Field(None, description="Agent名称")
    auto_select_tables: bool = Field(default=False, description="是否自动选择所有表")
    max_tables: int = Field(default=20, description="最大表数量", ge=1, le=50)
    include_sample_data: bool = Field(default=True, description="是否包含样本数据")

# 响应模型
class TableStructureInfo(BaseModel):
    """表结构信息"""
    name: str
    database: str
    columns: List[Dict[str, Any]]
    primary_keys: List[str]
    foreign_keys: List[Dict[str, Any]]
    indexes: List[Dict[str, Any]]
    row_count: Optional[int]
    sample_data: Optional[List[Dict[str, Any]]]
    comment: Optional[str]
    relationships: Optional[List[Dict[str, Any]]]

class TableAnalysisResponse(BaseModel):
    """表分析响应"""
    success: bool
    data: Optional[Dict[str, Any]]
    message: str
    error_code: Optional[str] = None

# 核心表分析函数 - 任务#119的关键实现
async def analyze_table_structure_enhanced(
    database: str, 
    table_name: str, 
    include_sample_data: bool = True,
    include_relationships: bool = True,
    sample_size: int = 5
) -> Dict[str, Any]:
    """
    增强版表结构分析 - 任务#119核心函数
    
    Args:
        database: 数据库名称
        table_name: 表名称
        include_sample_data: 是否包含样本数据
        include_relationships: 是否分析表关系
        sample_size: 样本数据行数
        
    Returns:
        Dict包含详细的表结构分析结果
    """
    try:
        logger.info(f"开始增强表结构分析: {database}.{table_name}")
        
        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")
        
        start_time = time.time()
        
        # 1. 获取基础表结构
        describe_query = f"DESCRIBE {database}.{table_name}"
        describe_result = client.execute_query(describe_query)
        
        if not describe_result or 'data' not in describe_result:
            raise BusinessException(f"无法获取表 {table_name} 的结构信息")
        
        # 解析列信息
        columns = []
        primary_keys = []
        column_names = describe_result.get('column_names', [])
        
        for row in describe_result['data']:
            decoded_row = unicode_handler.decode_response_data(row)
            
            column_info = {}
            for i, value in enumerate(decoded_row):
                if i < len(column_names):
                    key = column_names[i].lower()
                    column_info[key] = value
            
            # 标准化列信息
            column_data = {
                'name': column_info.get('field', column_info.get('column_name', '')),
                'type': column_info.get('type', column_info.get('data_type', '')),
                'nullable': column_info.get('null', 'YES') == 'YES',
                'key': column_info.get('key', ''),
                'default': column_info.get('default'),
                'extra': column_info.get('extra', ''),
                'comment': column_info.get('comment', '')
            }
            
            columns.append(column_data)
            
            # 识别主键
            if column_data['key'] == 'PRI' or 'primary' in str(column_data['key']).lower():
                primary_keys.append(column_data['name'])
        
        # 2. 获取表行数
        row_count = None
        try:
            count_query = f"SELECT COUNT(*) as row_count FROM {database}.{table_name}"
            count_result = client.execute_query(count_query)
            
            if count_result and 'data' in count_result and count_result['data']:
                row_count_data = unicode_handler.decode_response_data(count_result['data'][0])
                if isinstance(row_count_data, list) and len(row_count_data) > 0:
                    row_count = int(row_count_data[0])
                elif isinstance(row_count_data, dict):
                    row_count = int(row_count_data.get('row_count', 0))
        except Exception as e:
            logger.warning(f"获取表行数失败: {e}")
            row_count = None
        
        # 3. 获取样本数据
        sample_data = []
        if include_sample_data and row_count and row_count > 0:
            try:
                sample_query = f"SELECT * FROM {database}.{table_name} LIMIT {sample_size}"
                sample_result = client.execute_query(sample_query)
                
                if sample_result and 'data' in sample_result:
                    sample_column_names = sample_result.get('column_names', [])
                    
                    for row in sample_result['data']:
                        decoded_row = unicode_handler.decode_response_data(row)
                        
                        sample_row = {}
                        for i, value in enumerate(decoded_row):
                            if i < len(sample_column_names):
                                sample_row[sample_column_names[i]] = value
                        
                        sample_data.append(sample_row)
            except Exception as e:
                logger.warning(f"获取样本数据失败: {e}")
                sample_data = []
        
        # 4. 分析外键关系（如果支持）
        foreign_keys = []
        relationships = []
        
        if include_relationships:
            try:
                # 尝试获取外键信息（不同数据库语法可能不同）
                fk_queries = [
                    f"SHOW CREATE TABLE {database}.{table_name}",
                    f"SELECT * FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = '{database}' AND TABLE_NAME = '{table_name}' AND REFERENCED_TABLE_NAME IS NOT NULL"
                ]
                
                for fk_query in fk_queries:
                    try:
                        fk_result = client.execute_query(fk_query)
                        if fk_result and 'data' in fk_result:
                            # 解析外键信息（简化实现）
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"分析表关系失败: {e}")
        
        # 5. 生成表分析摘要
        analysis_summary = {
            'column_count': len(columns),
            'row_count': row_count,
            'has_primary_key': len(primary_keys) > 0,
            'has_foreign_keys': len(foreign_keys) > 0,
            'data_types': {},
            'nullable_columns': 0,
            'indexed_columns': 0
        }
        
        # 统计数据类型
        for column in columns:
            data_type = column['type'].upper()
            # 简化数据类型
            if 'INT' in data_type or 'BIGINT' in data_type:
                simplified_type = 'INTEGER'
            elif 'VARCHAR' in data_type or 'TEXT' in data_type or 'CHAR' in data_type:
                simplified_type = 'STRING'
            elif 'DECIMAL' in data_type or 'FLOAT' in data_type or 'DOUBLE' in data_type:
                simplified_type = 'NUMERIC'
            elif 'DATE' in data_type or 'TIME' in data_type:
                simplified_type = 'DATETIME'
            elif 'BOOL' in data_type:
                simplified_type = 'BOOLEAN'
            else:
                simplified_type = 'OTHER'
            
            analysis_summary['data_types'][simplified_type] = analysis_summary['data_types'].get(simplified_type, 0) + 1
            
            if column['nullable']:
                analysis_summary['nullable_columns'] += 1
        
        execution_time = time.time() - start_time
        
        # 构建完整的表结构信息
        table_structure = {
            'name': table_name,
            'database': database,
            'columns': columns,
            'primary_keys': primary_keys,
            'foreign_keys': foreign_keys,
            'indexes': [],  # 可以进一步实现索引分析
            'row_count': row_count,
            'sample_data': sample_data,
            'comment': None,  # 可以进一步实现表注释获取
            'relationships': relationships,
            'analysis_summary': analysis_summary,
            'analysis_metadata': {
                'analyzed_at': datetime.now().isoformat(),
                'execution_time': execution_time,
                'sample_size': len(sample_data),
                'include_sample_data': include_sample_data,
                'include_relationships': include_relationships
            }
        }
        
        logger.info(f"表结构分析完成: {database}.{table_name}, 耗时: {execution_time:.3f}秒")
        return table_structure
        
    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"表结构分析失败: {database}.{table_name}, 错误: {str(e)}")
        raise BusinessException(f"表结构分析失败: {str(e)}")

# 生成Agent提示模板 - 任务#119的关键功能
async def generate_agent_prompt_template(
    database: str,
    tables_analysis: List[Dict[str, Any]],
    include_sample_data: bool = True
) -> str:
    """
    基于表结构分析生成Agent提示模板
    
    Args:
        database: 数据库名称
        tables_analysis: 表分析结果列表
        include_sample_data: 是否包含样本数据
        
    Returns:
        生成的提示模板字符串
    """
    try:
        logger.info(f"生成Agent提示模板: {database}, {len(tables_analysis)}个表")
        
        # 构建提示模板
        prompt_parts = []
        
        # 1. 基础介绍
        prompt_parts.append(f"你是一个专业的SQL查询助手，专门处理 {database} 数据库的查询请求。")
        prompt_parts.append(f"数据库包含 {len(tables_analysis)} 个表，以下是详细的表结构信息：")
        prompt_parts.append("")
        
        # 2. 表结构详情
        for i, table_analysis in enumerate(tables_analysis, 1):
            table_name = table_analysis['name']
            columns = table_analysis['columns']
            primary_keys = table_analysis['primary_keys']
            row_count = table_analysis['row_count']
            sample_data = table_analysis.get('sample_data', [])
            
            prompt_parts.append(f"## 表 {i}: {table_name}")
            prompt_parts.append(f"- 表名: {database}.{table_name}")
            prompt_parts.append(f"- 行数: {row_count if row_count is not None else '未知'}")
            prompt_parts.append(f"- 主键: {', '.join(primary_keys) if primary_keys else '无'}")
            prompt_parts.append("")
            
            # 列信息
            prompt_parts.append("### 列结构:")
            for column in columns:
                nullable_text = "可空" if column['nullable'] else "非空"
                key_text = f" ({column['key']})" if column['key'] else ""
                comment_text = f" - {column['comment']}" if column['comment'] else ""
                
                prompt_parts.append(f"- {column['name']}: {column['type']} ({nullable_text}){key_text}{comment_text}")
            
            prompt_parts.append("")
            
            # 样本数据
            if include_sample_data and sample_data:
                prompt_parts.append("### 样本数据:")
                for j, sample_row in enumerate(sample_data[:3], 1):  # 最多显示3行样本
                    sample_values = []
                    for column in columns:
                        column_name = column['name']
                        value = sample_row.get(column_name, 'NULL')
                        if isinstance(value, str) and len(str(value)) > 20:
                            value = str(value)[:20] + "..."
                        sample_values.append(f"{column_name}={value}")
                    
                    prompt_parts.append(f"  样本{j}: {', '.join(sample_values)}")
                
                prompt_parts.append("")
        
        # 3. 查询指导原则
        prompt_parts.append("## 查询指导原则:")
        prompt_parts.append("1. 始终使用完整的表名格式: database.table_name")
        prompt_parts.append("2. 根据用户的自然语言问题生成准确的SQL查询")
        prompt_parts.append("3. 优先使用索引列和主键进行查询优化")
        prompt_parts.append("4. 对于模糊查询使用LIKE操作符")
        prompt_parts.append("5. 注意处理NULL值和数据类型转换")
        prompt_parts.append("6. 生成的SQL应该是可执行的和安全的")
        prompt_parts.append("")
        
        # 4. 表关系说明（如果有多个表）
        if len(tables_analysis) > 1:
            prompt_parts.append("## 表关系建议:")
            
            # 分析可能的关系
            all_columns = {}
            for table_analysis in tables_analysis:
                table_name = table_analysis['name']
                for column in table_analysis['columns']:
                    column_name = column['name'].lower()
                    if column_name not in all_columns:
                        all_columns[column_name] = []
                    all_columns[column_name].append(f"{table_name}.{column['name']}")
            
            # 找出可能的关联列
            potential_joins = []
            for column_name, table_columns in all_columns.items():
                if len(table_columns) > 1:
                    potential_joins.append(f"- 可能的关联列 '{column_name}': {', '.join(table_columns)}")
            
            if potential_joins:
                prompt_parts.extend(potential_joins)
            else:
                prompt_parts.append("- 未发现明显的表关联关系，请根据业务逻辑判断")
            
            prompt_parts.append("")
        
        # 5. 响应格式要求
        prompt_parts.append("## 响应格式:")
        prompt_parts.append("请按以下格式回复:")
        prompt_parts.append("```sql")
        prompt_parts.append("-- 生成的SQL查询")
        prompt_parts.append("SELECT ... FROM ... WHERE ...;")
        prompt_parts.append("```")
        prompt_parts.append("")
        prompt_parts.append("**查询说明**: 简要解释查询逻辑和注意事项")
        
        # 组合完整提示模板
        prompt_template = "\n".join(prompt_parts)
        
        logger.info(f"提示模板生成完成，长度: {len(prompt_template)} 字符")
        return prompt_template

    except Exception as e:
        logger.error(f"生成Agent提示模板失败: {str(e)}")
        raise BusinessException(f"生成Agent提示模板失败: {str(e)}")

# API端点实现
@router.post("/analyze")
async def analyze_tables(
    request: TableAnalysisRequest,
    _: None = Depends(check_table_analysis_features_enabled)
):
    """
    分析多个表的结构 - 任务#119基础功能
    """
    try:
        logger.info(f"开始表结构分析: {request.database}, 表: {request.tables}")

        if not request.tables:
            raise HTTPException(status_code=400, detail="表列表不能为空")

        start_time = time.time()
        analysis_results = []

        # 并行分析多个表
        tasks = []
        for table_name in request.tables:
            task = analyze_table_structure_enhanced(
                database=request.database,
                table_name=table_name,
                include_sample_data=request.include_sample_data,
                include_relationships=request.include_relationships,
                sample_size=request.sample_size
            )
            tasks.append(task)

        # 等待所有分析完成
        table_analyses = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        successful_analyses = []
        failed_analyses = []

        for i, result in enumerate(table_analyses):
            table_name = request.tables[i]

            if isinstance(result, Exception):
                failed_analyses.append({
                    'table_name': table_name,
                    'error': str(result)
                })
                logger.error(f"表 {table_name} 分析失败: {result}")
            else:
                successful_analyses.append(result)

        total_time = time.time() - start_time

        # 构建响应
        response_data = {
            'database': request.database,
            'total_tables': len(request.tables),
            'successful_analyses': len(successful_analyses),
            'failed_analyses': len(failed_analyses),
            'tables': successful_analyses,
            'errors': failed_analyses,
            'analysis_metadata': {
                'total_execution_time': total_time,
                'include_sample_data': request.include_sample_data,
                'include_relationships': request.include_relationships,
                'sample_size': request.sample_size,
                'analyzed_at': datetime.now().isoformat()
            }
        }

        return APIResponseBuilder.success(
            data=response_data,
            message=f"表结构分析完成，成功: {len(successful_analyses)}, 失败: {len(failed_analyses)}"
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"表结构分析业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"表结构分析错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/integrate-agent-skills")
async def integrate_with_agent_skills(
    request: AgentSkillIntegrationRequest,
    _: None = Depends(check_table_analysis_features_enabled)
):
    """
    集成表分析结果到Agent技能 - 任务#119核心功能
    """
    try:
        logger.info(f"开始Agent技能集成: {request.database}, Agent: {request.agent_name}")

        if not request.tables:
            raise HTTPException(status_code=400, detail="表列表不能为空")

        start_time = time.time()

        # 1. 分析表结构
        logger.info("步骤1: 分析表结构")
        analysis_tasks = []
        for table_name in request.tables:
            task = analyze_table_structure_enhanced(
                database=request.database,
                table_name=table_name,
                include_sample_data=request.include_sample_data,
                include_relationships=True,
                sample_size=5
            )
            analysis_tasks.append(task)

        table_analyses = await asyncio.gather(*analysis_tasks, return_exceptions=True)

        # 过滤成功的分析结果
        successful_analyses = []
        for i, result in enumerate(table_analyses):
            if not isinstance(result, Exception):
                successful_analyses.append(result)
            else:
                logger.warning(f"表 {request.tables[i]} 分析失败: {result}")

        if not successful_analyses:
            raise BusinessException("所有表分析都失败了")

        # 2. 生成Agent提示模板
        logger.info("步骤2: 生成Agent提示模板")
        prompt_template = await generate_agent_prompt_template(
            database=request.database,
            tables_analysis=successful_analyses,
            include_sample_data=request.include_sample_data
        )

        # 3. 确定Agent名称
        agent_name = request.agent_name
        if not agent_name:
            agent_name = f"text2sql_{request.database}_agent"
            logger.info(f"自动生成Agent名称: {agent_name}")

        # 4. 更新或创建Agent
        agent_updated = False
        if request.update_prompt_template:
            try:
                logger.info("步骤3: 更新Agent提示模板")
                await update_agent_prompt_template(agent_name, prompt_template)
                agent_updated = True
                logger.info(f"Agent {agent_name} 提示模板更新成功")
            except Exception as e:
                logger.warning(f"更新Agent提示模板失败: {e}")

        # 5. 确定技能名称
        skill_name = request.skill_name
        if not skill_name:
            skill_name = f"text2sql_{request.database}_skill"
            logger.info(f"自动生成技能名称: {skill_name}")

        # 6. 更新或创建技能
        skill_updated = False
        if request.update_skill_config:
            try:
                logger.info("步骤4: 更新技能配置")

                # 构建技能更新数据
                skill_update_data = {
                    'description': f'Text2SQL技能，用于查询{request.database}数据库的{len(request.tables)}个表',
                    'tables': request.tables,
                    'parameters': {
                        'database': request.database,
                        'analyzed_tables': [analysis['name'] for analysis in successful_analyses],
                        'schema_analysis': True,
                        'sample_data_included': request.include_sample_data,
                        'last_updated': datetime.now().isoformat()
                    }
                }

                # 尝试更新现有技能
                try:
                    await update_skill_tables(skill_name, skill_update_data)
                    skill_updated = True
                    logger.info(f"技能 {skill_name} 更新成功")
                except:
                    # 如果更新失败，尝试创建新技能
                    logger.info(f"技能 {skill_name} 不存在，创建新技能")

                    skill_create_data = {
                        'name': skill_name,
                        'skill_type': 'text2sql',
                        'database': request.database,
                        'tables': request.tables,
                        'description': skill_update_data['description'],
                        'parameters': skill_update_data['parameters']
                    }

                    await create_skill(skill_create_data)
                    skill_updated = True
                    logger.info(f"技能 {skill_name} 创建成功")

            except Exception as e:
                logger.warning(f"更新技能配置失败: {e}")

        total_time = time.time() - start_time

        # 构建响应
        response_data = {
            'database': request.database,
            'tables': request.tables,
            'agent_name': agent_name,
            'skill_name': skill_name,
            'successful_table_analyses': len(successful_analyses),
            'failed_table_analyses': len(request.tables) - len(successful_analyses),
            'agent_updated': agent_updated,
            'skill_updated': skill_updated,
            'prompt_template': prompt_template if request.update_prompt_template else None,
            'integration_metadata': {
                'total_execution_time': total_time,
                'include_sample_data': request.include_sample_data,
                'update_prompt_template': request.update_prompt_template,
                'update_skill_config': request.update_skill_config,
                'integrated_at': datetime.now().isoformat()
            },
            'table_analyses_summary': [
                {
                    'name': analysis['name'],
                    'column_count': analysis['analysis_summary']['column_count'],
                    'row_count': analysis['row_count'],
                    'has_primary_key': analysis['analysis_summary']['has_primary_key'],
                    'data_types': analysis['analysis_summary']['data_types']
                }
                for analysis in successful_analyses
            ]
        }

        return APIResponseBuilder.success(
            data=response_data,
            message=f"Agent技能集成完成，Agent: {agent_name}, 技能: {skill_name}"
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"Agent技能集成业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Agent技能集成错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/batch-analyze")
async def batch_analyze_database(
    request: BatchTableAnalysisRequest,
    _: None = Depends(check_table_analysis_features_enabled)
):
    """
    批量分析数据库中的所有表 - 任务#119扩展功能
    """
    try:
        logger.info(f"开始批量数据库分析: {request.database}")

        start_time = time.time()

        # 1. 获取数据库中的所有表
        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        tables_query = f"SHOW TABLES FROM {request.database}"
        tables_result = client.execute_query(tables_query)

        if not tables_result or 'data' not in tables_result:
            raise BusinessException(f"无法获取数据库 {request.database} 的表列表")

        # 解析表名列表
        all_tables = []
        for row in tables_result['data']:
            decoded_row = unicode_handler.decode_response_data(row)
            if isinstance(decoded_row, list) and len(decoded_row) > 0:
                table_name = str(decoded_row[0])
                all_tables.append(table_name)

        if not all_tables:
            raise BusinessException(f"数据库 {request.database} 中没有找到表")

        # 2. 限制表数量
        if request.auto_select_tables:
            selected_tables = all_tables[:request.max_tables]
        else:
            selected_tables = all_tables[:request.max_tables]

        logger.info(f"发现 {len(all_tables)} 个表，选择分析 {len(selected_tables)} 个表")

        # 3. 批量分析表结构
        analysis_tasks = []
        for table_name in selected_tables:
            task = analyze_table_structure_enhanced(
                database=request.database,
                table_name=table_name,
                include_sample_data=request.include_sample_data,
                include_relationships=True,
                sample_size=3  # 批量分析时减少样本数据
            )
            analysis_tasks.append(task)

        # 并行执行分析
        table_analyses = await asyncio.gather(*analysis_tasks, return_exceptions=True)

        # 处理结果
        successful_analyses = []
        failed_analyses = []

        for i, result in enumerate(table_analyses):
            table_name = selected_tables[i]

            if isinstance(result, Exception):
                failed_analyses.append({
                    'table_name': table_name,
                    'error': str(result)
                })
                logger.error(f"表 {table_name} 分析失败: {result}")
            else:
                successful_analyses.append(result)

        # 4. 如果指定了Agent，自动集成
        agent_integration_result = None
        if request.agent_name and successful_analyses:
            try:
                logger.info(f"自动集成到Agent: {request.agent_name}")

                # 构建集成请求
                integration_request = AgentSkillIntegrationRequest(
                    database=request.database,
                    tables=[analysis['name'] for analysis in successful_analyses],
                    agent_name=request.agent_name,
                    update_prompt_template=True,
                    update_skill_config=True,
                    include_sample_data=request.include_sample_data
                )

                # 执行集成（简化版，避免重复分析）
                prompt_template = await generate_agent_prompt_template(
                    database=request.database,
                    tables_analysis=successful_analyses,
                    include_sample_data=request.include_sample_data
                )

                # 更新Agent
                await update_agent_prompt_template(request.agent_name, prompt_template)

                agent_integration_result = {
                    'agent_name': request.agent_name,
                    'integrated_tables': len(successful_analyses),
                    'prompt_template_updated': True,
                    'integration_success': True
                }

                logger.info(f"Agent集成成功: {request.agent_name}")

            except Exception as e:
                logger.warning(f"Agent集成失败: {e}")
                agent_integration_result = {
                    'agent_name': request.agent_name,
                    'integration_success': False,
                    'error': str(e)
                }

        total_time = time.time() - start_time

        # 5. 生成数据库分析摘要
        database_summary = {
            'total_tables_in_database': len(all_tables),
            'analyzed_tables': len(successful_analyses),
            'failed_analyses': len(failed_analyses),
            'total_columns': sum(analysis['analysis_summary']['column_count'] for analysis in successful_analyses),
            'total_estimated_rows': sum(analysis['row_count'] or 0 for analysis in successful_analyses),
            'data_type_distribution': {},
            'tables_with_primary_keys': sum(1 for analysis in successful_analyses if analysis['analysis_summary']['has_primary_key']),
            'average_columns_per_table': 0
        }

        # 统计数据类型分布
        all_data_types = {}
        for analysis in successful_analyses:
            for data_type, count in analysis['analysis_summary']['data_types'].items():
                all_data_types[data_type] = all_data_types.get(data_type, 0) + count

        database_summary['data_type_distribution'] = all_data_types

        if successful_analyses:
            database_summary['average_columns_per_table'] = database_summary['total_columns'] / len(successful_analyses)

        # 构建响应
        response_data = {
            'database': request.database,
            'all_tables': all_tables,
            'analyzed_tables': [analysis['name'] for analysis in successful_analyses],
            'failed_tables': [error['table_name'] for error in failed_analyses],
            'database_summary': database_summary,
            'table_analyses': successful_analyses,
            'analysis_errors': failed_analyses,
            'agent_integration': agent_integration_result,
            'batch_metadata': {
                'total_execution_time': total_time,
                'auto_select_tables': request.auto_select_tables,
                'max_tables_limit': request.max_tables,
                'include_sample_data': request.include_sample_data,
                'analyzed_at': datetime.now().isoformat()
            }
        }

        return APIResponseBuilder.success(
            data=response_data,
            message=f"批量数据库分析完成，成功: {len(successful_analyses)}, 失败: {len(failed_analyses)}"
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"批量数据库分析业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"批量数据库分析错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/databases")
async def list_available_databases(
    _: None = Depends(check_table_analysis_features_enabled)
):
    """
    获取可用的数据库列表
    """
    try:
        logger.info("获取可用数据库列表")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 获取数据库列表
        databases_query = "SHOW DATABASES"
        databases_result = client.execute_query(databases_query)

        if not databases_result or 'data' not in databases_result:
            raise BusinessException("无法获取数据库列表")

        # 解析数据库列表
        databases = []
        for row in databases_result['data']:
            decoded_row = unicode_handler.decode_response_data(row)
            if isinstance(decoded_row, list) and len(decoded_row) > 0:
                db_name = str(decoded_row[0])
                # 过滤系统数据库
                if db_name not in ['information_schema', 'mysql', 'performance_schema', 'sys']:
                    databases.append(db_name)

        return APIResponseBuilder.success(
            data={
                'databases': databases,
                'total_count': len(databases),
                'retrieved_at': datetime.now().isoformat()
            },
            message=f"成功获取 {len(databases)} 个数据库"
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"获取数据库列表业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"获取数据库列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/databases/{database}/tables")
async def list_database_tables(
    database: str,
    _: None = Depends(check_table_analysis_features_enabled)
):
    """
    获取指定数据库的表列表
    """
    try:
        logger.info(f"获取数据库表列表: {database}")

        client = get_mindsdb_client()
        if not client:
            raise BusinessException("MindsDB客户端未初始化")

        # 获取表列表
        tables_query = f"SHOW TABLES FROM {database}"
        tables_result = client.execute_query(tables_query)

        if not tables_result or 'data' not in tables_result:
            raise BusinessException(f"无法获取数据库 {database} 的表列表")

        # 解析表列表
        tables = []
        for row in tables_result['data']:
            decoded_row = unicode_handler.decode_response_data(row)
            if isinstance(decoded_row, list) and len(decoded_row) > 0:
                table_name = str(decoded_row[0])
                tables.append(table_name)

        return APIResponseBuilder.success(
            data={
                'database': database,
                'tables': tables,
                'total_count': len(tables),
                'retrieved_at': datetime.now().isoformat()
            },
            message=f"成功获取数据库 {database} 的 {len(tables)} 个表"
        ).dict()

    except HTTPException:
        raise
    except BusinessException as e:
        logger.error(f"获取表列表业务错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"获取表列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
