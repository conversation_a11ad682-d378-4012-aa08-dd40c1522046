"""
Agent管理API
提供Agent的CRUD操作接口
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
import logging
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.mindsdb_client import get_mindsdb_client
from core.agent_skill_management_service import (
    AgentSkillManagementService,
    AgentConfiguration,
    SkillConfiguration,
    SkillType
)
from utils.unicode_handler import unicode_handler

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/agents", tags=["agents"])

@router.get("/", response_model=Dict[str, Any])
async def get_agents(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    limit: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[str] = Query(None, description="状态筛选"),
    model: Optional[str] = Query(None, description="模型筛选"),
    project: Optional[str] = Query(None, description="项目筛选")
):
    """
    获取Agent列表，支持分页和多种筛选条件

    Args:
        page: 页码，从1开始
        limit: 每页数量，最大100
        search: 搜索关键词
        status: 状态筛选
        model: 模型筛选
        project: 项目筛选

    Returns:
        包含分页信息的Agent列表数据
    """
    try:
        logger.info(f"获取Agent列表 - 页码: {page}, 每页: {limit}, 搜索: {search}")

        # 尝试多种查询方式获取Agent列表
        queries_to_try = [
            "SHOW AGENTS",
            "SELECT * FROM information_schema.agents",
            "SELECT * FROM mindsdb.agents",
            "SELECT name, project, model_name, skills, params FROM mindsdb.agents"
        ]

        result = None
        successful_query = None

        for query in queries_to_try:
            try:
                logger.info(f"尝试查询: {query}")
                client = get_mindsdb_client()
                if not client:
                    logger.error("MindsDB客户端未初始化")
                    continue
                result = client.execute_query(query)
                if result and 'data' in result and len(result['data']) >= 0:  # 允许空结果
                    successful_query = query
                    logger.info(f"查询成功: {query}, 返回 {len(result['data'])} 条记录")
                    break
                else:
                    logger.warning(f"查询返回无效结果: {query}")
            except Exception as e:
                logger.warning(f"查询失败 {query}: {e}")
                continue

        if not result or 'data' not in result:
            logger.warning("所有Agent查询方式都失败")
            return {
                "success": True,
                "data": [],
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": 0,
                    "total_pages": 0,
                    "has_next": False,
                    "has_prev": False
                },
                "message": "未找到任何Agent（可能MindsDB中没有Agent或查询方式不支持）",
                "debug_info": {
                    "tried_queries": queries_to_try,
                    "successful_query": None
                }
            }

        logger.info(f"使用查询 '{successful_query}' 成功获取Agent数据")
        agents = []
        if result and 'data' in result:
            column_names = result.get('column_names', [])
            for agent_row in result['data']:
                # 解码Unicode内容
                decoded_row = unicode_handler.decode_response_data(agent_row)

                # 将数组转换为字典格式，便于处理
                agent_dict = {}
                for i, value in enumerate(decoded_row):
                    if i < len(column_names):
                        key = column_names[i].lower()
                        agent_dict[key] = value

                # 格式化Agent数据
                formatted_agent = {
                    'name': agent_dict.get('name', ''),
                    'project': agent_dict.get('project', ''),
                    'model_name': agent_dict.get('model_name', ''),
                    'skills': agent_dict.get('skills', []),
                    'params': agent_dict.get('params', ''),
                    'status': 'active',  # MindsDB中的Agent默认为active状态
                    'created_at': None,  # MindsDB暂不提供创建时间
                    'updated_at': None   # MindsDB暂不提供更新时间
                }

                # 尝试解析params中的详细信息
                if formatted_agent['params']:
                    try:
                        params_dict = unicode_handler.format_agent_params(formatted_agent['params'])
                        formatted_agent['description'] = params_dict.get('description', '')
                        formatted_agent['prompt_template'] = params_dict.get('prompt_template', '')
                    except Exception as e:
                        logger.warning(f"解析Agent参数失败: {e}")
                        formatted_agent['description'] = ''
                        formatted_agent['prompt_template'] = ''

                agents.append(formatted_agent)
        
        # 应用筛选条件
        filtered_agents = agents.copy()

        # 搜索过滤
        if search:
            search_lower = search.lower()
            filtered_agents = [
                agent for agent in filtered_agents
                if (search_lower in str(agent.get('name', '')).lower() or
                    search_lower in str(agent.get('model_name', '')).lower() or
                    search_lower in str(agent.get('description', '')).lower() or
                    search_lower in str(agent.get('project', '')).lower())
            ]

        # 状态过滤
        if status:
            filtered_agents = [
                agent for agent in filtered_agents
                if agent.get('status', '').lower() == status.lower()
            ]

        # 模型过滤
        if model:
            filtered_agents = [
                agent for agent in filtered_agents
                if model.lower() in str(agent.get('model_name', '')).lower()
            ]

        # 项目过滤
        if project:
            filtered_agents = [
                agent for agent in filtered_agents
                if project.lower() in str(agent.get('project', '')).lower()
            ]

        # 计算分页
        total_count = len(filtered_agents)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_agents = filtered_agents[start_index:end_index]

        # 计算分页信息
        total_pages = (total_count + limit - 1) // limit
        has_next = page < total_pages
        has_prev = page > 1

        return {
            "success": True,
            "data": paginated_agents,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev
            },
            "filters": {
                "search": search,
                "status": status,
                "model": model,
                "project": project
            },
            "message": f"成功获取第 {page} 页，共 {total_count} 个Agent"
        }
        
    except Exception as e:
        logger.error(f"获取Agent列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取Agent列表失败: {str(e)}")

@router.get("/{agent_name}", response_model=Dict[str, Any])
async def get_agent_details(agent_name: str):
    """
    获取Agent详细信息
    
    Args:
        agent_name: Agent名称
        
    Returns:
        Agent详细信息
    """
    try:
        logger.info(f"获取Agent详情: {agent_name}")

        # 查询所有Agent，然后筛选指定的Agent
        query = "SHOW AGENTS"
        client = get_mindsdb_client()
        if not client:
            raise HTTPException(status_code=500, detail="MindsDB客户端未初始化")
        result = client.execute_query(query)

        if not result or 'data' not in result:
            raise HTTPException(status_code=500, detail="无法获取Agent信息")

        # 查找指定的Agent
        agent_found = None
        column_names = result.get('column_names', [])

        for agent_row in result['data']:
            decoded_row = unicode_handler.decode_response_data(agent_row)

            # 将数组转换为字典格式
            agent_dict = {}
            for i, value in enumerate(decoded_row):
                if i < len(column_names):
                    key = column_names[i].lower()
                    agent_dict[key] = value

            if agent_dict.get('name') == agent_name:
                agent_found = agent_dict
                break

        if not agent_found:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")

        # 格式化Agent详细信息
        formatted_agent = {
            'name': agent_found.get('name', ''),
            'project': agent_found.get('project', ''),
            'model_name': agent_found.get('model_name', ''),
            'skills': agent_found.get('skills', []),
            'params': agent_found.get('params', ''),
            'status': 'active',
            'created_at': None,
            'updated_at': None
        }

        # 解析params中的详细信息
        if formatted_agent['params']:
            try:
                params_dict = unicode_handler.format_agent_params(formatted_agent['params'])
                formatted_agent['description'] = params_dict.get('description', '')
                formatted_agent['prompt_template'] = params_dict.get('prompt_template', '')
                formatted_agent['max_tokens'] = params_dict.get('max_tokens', None)
                formatted_agent['temperature'] = params_dict.get('temperature', None)
                formatted_agent['provider'] = params_dict.get('provider', '')

                # 添加其他可能的参数
                for key, value in params_dict.items():
                    if key not in ['description', 'prompt_template', 'max_tokens', 'temperature', 'provider']:
                        formatted_agent[f'param_{key}'] = value

            except Exception as e:
                logger.warning(f"解析Agent参数失败: {e}")
                formatted_agent['description'] = ''
                formatted_agent['prompt_template'] = ''

        # 获取Agent的技能信息
        try:
            skills_query = f"SHOW SKILLS FROM {agent_name}"
            client = get_mindsdb_client()
            if not client:
                logger.error("MindsDB客户端未初始化")
                return []
            skills_result = client.execute_query(skills_query)

            detailed_skills = []
            if skills_result and 'data' in skills_result:
                skills_column_names = skills_result.get('column_names', [])
                for skill_row in skills_result['data']:
                    decoded_skill = unicode_handler.decode_response_data(skill_row)

                    # 将技能数据转换为字典格式
                    skill_dict = {}
                    for i, value in enumerate(decoded_skill):
                        if i < len(skills_column_names):
                            key = skills_column_names[i].lower()
                            skill_dict[key] = value

                    detailed_skills.append(skill_dict)

            formatted_agent['detailed_skills'] = detailed_skills

        except Exception as e:
            logger.warning(f"获取Agent技能详情失败: {e}")
            formatted_agent['detailed_skills'] = []
        
        return {
            "success": True,
            "data": formatted_agent,
            "message": "获取Agent详情成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Agent详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取Agent详情失败: {str(e)}")

@router.post("/", response_model=Dict[str, Any])
async def create_agent(agent_data: Dict[str, Any]):
    """
    创建新Agent，支持完整的Agent配置

    Args:
        agent_data: Agent创建数据，包含以下字段：
            - name (必需): Agent名称
            - model (必需): 使用的AI模型
            - description (可选): Agent描述
            - prompt_template (可选): 提示模板
            - skills (可选): 技能列表
            - temperature (可选): 温度参数
            - max_tokens (可选): 最大token数
            - provider (可选): 模型提供商

    Returns:
        创建结果和Agent信息
    """
    try:
        logger.info(f"创建Agent: {agent_data.get('name')}")

        # 验证必需字段
        required_fields = ['name', 'model']
        for field in required_fields:
            if field not in agent_data or not agent_data[field]:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        name = agent_data['name']
        model = agent_data['model']

        # 验证Agent名称格式（只允许字母、数字、下划线）
        import re
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', name):
            raise HTTPException(
                status_code=400,
                detail="Agent名称只能包含字母、数字和下划线，且必须以字母开头"
            )

        # 检查Agent名称是否已存在
        try:
            existing_agents_query = "SHOW AGENTS"
            client = get_mindsdb_client()
            if not client:
                raise HTTPException(status_code=500, detail="MindsDB客户端未初始化")
            existing_result = client.execute_query(existing_agents_query)

            if existing_result and 'data' in existing_result:
                existing_names = []
                for agent_row in existing_result['data']:
                    decoded_row = unicode_handler.decode_response_data(agent_row)
                    if isinstance(decoded_row, list) and len(decoded_row) > 0:
                        existing_names.append(str(decoded_row[0]))

                if name in existing_names:
                    raise HTTPException(
                        status_code=409,
                        detail=f"Agent名称 '{name}' 已存在，请使用其他名称"
                    )
        except HTTPException:
            raise
        except Exception as e:
            logger.warning(f"检查Agent名称唯一性失败: {e}")

        # 构建Agent参数
        agent_params = {
            'model': model
        }

        # 添加描述信息
        if 'description' in agent_data and agent_data['description']:
            agent_params['description'] = agent_data['description']

        # 添加提示模板
        if 'prompt_template' in agent_data and agent_data['prompt_template']:
            agent_params['prompt_template'] = agent_data['prompt_template']

        # 添加温度参数
        if 'temperature' in agent_data:
            try:
                temp = float(agent_data['temperature'])
                if 0.0 <= temp <= 2.0:
                    agent_params['temperature'] = temp
                else:
                    raise HTTPException(status_code=400, detail="temperature参数必须在0.0-2.0之间")
            except (ValueError, TypeError):
                raise HTTPException(status_code=400, detail="temperature参数必须是有效的数字")

        # 添加最大token数
        if 'max_tokens' in agent_data:
            try:
                max_tokens = int(agent_data['max_tokens'])
                if max_tokens > 0:
                    agent_params['max_tokens'] = max_tokens
                else:
                    raise HTTPException(status_code=400, detail="max_tokens参数必须大于0")
            except (ValueError, TypeError):
                raise HTTPException(status_code=400, detail="max_tokens参数必须是有效的整数")

        # 添加提供商信息
        if 'provider' in agent_data and agent_data['provider']:
            agent_params['provider'] = agent_data['provider']

        # 构建CREATE AGENT SQL语句
        params_list = []
        for key, value in agent_params.items():
            if isinstance(value, str):
                # 转义单引号
                escaped_value = value.replace("'", "''")
                params_list.append(f"{key} = '{escaped_value}'")
            else:
                params_list.append(f"{key} = {value}")

        query = f"CREATE AGENT {name} USING {', '.join(params_list)}"

        # 添加技能配置
        if 'skills' in agent_data and agent_data['skills']:
            skills = agent_data['skills']
            if isinstance(skills, list) and skills:
                skills_str = ','.join([f"'{skill}'" for skill in skills])
                query += f", skills = [{skills_str}]"

        logger.info(f"执行创建Agent SQL: {query}")
        client = get_mindsdb_client()
        if not client:
            raise HTTPException(status_code=500, detail="MindsDB客户端未初始化")
        result = client.execute_query(query)

        # 构建基本的Agent信息（不依赖立即查询）
        agent_info = {
            "name": name,
            "model": model,
            "description": agent_data.get('description', ''),
            "prompt_template": agent_data.get('prompt_template', ''),
            "status": "created"
        }

        # 添加可选参数到返回信息
        if 'temperature' in agent_data:
            agent_info['temperature'] = agent_data['temperature']
        if 'max_tokens' in agent_data:
            agent_info['max_tokens'] = agent_data['max_tokens']
        if 'provider' in agent_data:
            agent_info['provider'] = agent_data['provider']

        # 尝试获取创建后的Agent详细信息（带重试机制）
        detailed_info = None
        for attempt in range(3):  # 最多重试3次
            try:
                import asyncio
                await asyncio.sleep(0.5 * (attempt + 1))  # 递增等待时间
                created_agent = await get_agent_details(name)
                detailed_info = created_agent.get('data', {})
                logger.info(f"第{attempt + 1}次尝试获取Agent详情成功")
                break
            except Exception as e:
                logger.warning(f"第{attempt + 1}次尝试获取Agent详情失败: {e}")
                if attempt == 2:  # 最后一次尝试
                    logger.warning(f"所有尝试都失败，使用基本信息返回")

        # 如果获取到详细信息，则合并到基本信息中
        if detailed_info:
            agent_info.update(detailed_info)

        return {
            "success": True,
            "data": agent_info,
            "message": f"Agent '{name}' 创建成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建Agent失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建Agent失败: {str(e)}")

@router.post("/intelligent-text2sql", response_model=Dict[str, Any])
async def create_intelligent_text2sql_agent(agent_data: Dict[str, Any]):
    """
    创建带有intelligent_text2sql技能的Agent

    Args:
        agent_data: Agent创建数据，包含以下字段：
            - name (必需): Agent名称
            - model (必需): 使用的AI模型
            - description (可选): Agent描述
            - database (必需): 目标数据库
            - tables (可选): 指定表列表，为空则使用所有表
            - schema_analysis (可选): 是否启用架构分析，默认true
            - query_optimization (可选): 是否启用查询优化，默认true
            - temperature (可选): 温度参数
            - max_tokens (可选): 最大token数

    Returns:
        创建结果和Agent信息
    """
    try:
        logger.info(f"创建intelligent_text2sql Agent: {agent_data.get('name')}")

        # 验证必需字段
        required_fields = ['name', 'model', 'database']
        for field in required_fields:
            if field not in agent_data or not agent_data[field]:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        # 初始化技能管理服务
        skill_service = AgentSkillManagementService(mindsdb_client)

        # 构建技能配置
        skill_config = SkillConfiguration(
            skill_type=SkillType.INTELLIGENT_TEXT2SQL.value,
            name="intelligent_text2sql",
            description="智能Text2SQL技能，支持自然语言到SQL的智能转换",
            database=agent_data['database'],
            tables=agent_data.get('tables', []),
            parameters={
                "schema_analysis": agent_data.get('schema_analysis', True),
                "query_optimization": agent_data.get('query_optimization', True),
                "result_formatting": True,
                "error_handling": True
            }
        )

        # 构建Agent配置
        agent_config = AgentConfiguration(
            name=agent_data['name'],
            model=agent_data['model'],
            description=agent_data.get('description', f"智能Text2SQL助手，专门处理{agent_data['database']}数据库的查询"),
            skills=[skill_config],
            parameters={
                "temperature": agent_data.get('temperature', 0.1),
                "max_tokens": agent_data.get('max_tokens', 2000)
            }
        )

        # 创建Agent
        result = await skill_service.create_agent_with_intelligent_text2sql(agent_config)

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "Agent创建失败"))

        # 构建返回信息
        agent_info = {
            "name": agent_config.name,
            "model": agent_config.model,
            "description": agent_config.description,
            "database": agent_data['database'],
            "tables": agent_data.get('tables', []),
            "skills": [skill_config.name],
            "skill_type": "intelligent_text2sql",
            "parameters": agent_config.parameters,
            "status": "created",
            "sql": result.get("sql", "")
        }

        logger.info(f"intelligent_text2sql Agent创建成功: {agent_config.name}")
        return {
            "success": True,
            "data": agent_info,
            "message": f"intelligent_text2sql Agent '{agent_config.name}' 创建成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建intelligent_text2sql Agent失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建intelligent_text2sql Agent失败: {str(e)}")

@router.get("/skills/types", response_model=Dict[str, Any])
async def get_supported_skill_types():
    """
    获取支持的技能类型列表

    Returns:
        支持的技能类型和相关信息
    """
    try:
        skill_service = AgentSkillManagementService()

        skill_types = skill_service.get_supported_skill_types()
        skill_templates = {}

        for skill_type in skill_types:
            template = skill_service.get_skill_template(skill_type)
            if template:
                skill_templates[skill_type] = template

        return {
            "success": True,
            "data": {
                "skill_types": skill_types,
                "templates": skill_templates
            }
        }

    except Exception as e:
        logger.error(f"获取技能类型失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取技能类型失败: {str(e)}")

@router.get("/{agent_name}/skills", response_model=Dict[str, Any])
async def get_agent_skills(agent_name: str):
    """
    获取指定Agent的技能信息

    Args:
        agent_name: Agent名称

    Returns:
        Agent的技能列表和详细信息
    """
    try:
        logger.info(f"获取Agent技能: {agent_name}")

        skill_service = AgentSkillManagementService(mindsdb_client)
        skills = await skill_service.get_agent_skills(agent_name)

        return {
            "success": True,
            "data": {
                "agent_name": agent_name,
                "skills": [skill_service.to_dict(skill) for skill in skills],
                "skill_count": len(skills)
            }
        }

    except Exception as e:
        logger.error(f"获取Agent技能失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取Agent技能失败: {str(e)}")

@router.post("/{agent_name}/respond", response_model=Dict[str, Any])
async def agent_respond(
    agent_name: str,
    message_data: Dict[str, Any]
):
    """
    与Agent进行对话交互

    Args:
        agent_name: Agent名称
        message_data: 消息数据，包含以下字段：
            - question (必需): 用户问题
            - context (可选): 对话上下文
            - session_id (可选): 会话ID
            - stream (可选): 是否流式响应

    Returns:
        Agent的响应结果
    """
    try:
        logger.info(f"Agent {agent_name} 收到消息: {message_data.get('question', '')[:100]}")

        # 验证必需字段
        if 'question' not in message_data or not message_data['question']:
            raise HTTPException(status_code=400, detail="缺少必需字段: question")

        question = message_data['question']
        context = message_data.get('context', [])
        session_id = message_data.get('session_id', '')
        stream = message_data.get('stream', False)

        # 检查Agent是否存在
        try:
            agent_details = await get_agent_details(agent_name)
            if not agent_details.get('success'):
                raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")
        except HTTPException:
            raise
        except Exception as e:
            logger.warning(f"检查Agent存在性失败: {e}")

        # 构建查询语句
        # MindsDB Agent响应的标准格式
        escaped_question = question.replace("'", "''")
        query = f"SELECT * FROM {agent_name} WHERE question = '{escaped_question}'"

        # 添加上下文信息（如果有）
        if context and isinstance(context, list):
            context_str = '; '.join([str(ctx) for ctx in context])
            escaped_context = context_str.replace("'", "''")
            query += f" AND context = '{escaped_context}'"

        logger.info(f"执行Agent查询: {query}")

        # 执行查询
        client = get_mindsdb_client()
        if not client:
            raise HTTPException(status_code=500, detail="MindsDB客户端未初始化")
        result = client.execute_query(query)

        if not result or 'data' not in result or not result['data']:
            return {
                "success": False,
                "error": "Agent没有返回响应",
                "agent_name": agent_name,
                "question": question,
                "message": "Agent可能配置有误或模型不可用"
            }

        # 解析响应数据
        response_data = result['data'][0]
        column_names = result.get('column_names', [])

        # 将响应数据转换为字典格式
        response_dict = {}
        for i, value in enumerate(response_data):
            if i < len(column_names):
                key = column_names[i].lower()
                response_dict[key] = unicode_handler.decode_response_data(value)

        # 提取主要响应内容
        answer = response_dict.get('answer', '')
        response_context = response_dict.get('context', [])
        trace_id = response_dict.get('trace_id', '')

        # 检查是否有错误
        if answer and ('Error code:' in str(answer) or 'error' in str(answer).lower()):
            error_message = str(answer)

            # 解析常见错误
            if 'invalid_api_key' in error_message:
                error_type = "API密钥无效"
                suggestion = "请检查Agent配置的API密钥是否正确"
            elif 'rate_limit' in error_message:
                error_type = "请求频率限制"
                suggestion = "请稍后再试"
            elif 'model_not_found' in error_message:
                error_type = "模型不存在"
                suggestion = "请检查Agent配置的模型名称"
            else:
                error_type = "未知错误"
                suggestion = "请检查Agent配置和网络连接"

            return {
                "success": False,
                "error": error_type,
                "error_details": error_message,
                "suggestion": suggestion,
                "agent_name": agent_name,
                "question": question,
                "trace_id": trace_id
            }

        # 成功响应
        # 安全地获取模型名称
        model_name = ''
        if agent_details and agent_details.get('success') and agent_details.get('data'):
            model_name = agent_details['data'].get('model_name', '')

        response_result = {
            "success": True,
            "agent_name": agent_name,
            "question": question,
            "answer": answer,
            "context": response_context,
            "trace_id": trace_id,
            "session_id": session_id,
            "timestamp": result.get('timestamp') or unicode_handler.decode_response_data(str(int(time.time()))),
            "metadata": {
                "model": model_name,
                "response_time": None,  # 可以在后续版本中添加
                "token_usage": None     # 可以在后续版本中添加
            }
        }

        return response_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Agent响应失败: {e}")
        raise HTTPException(status_code=500, detail=f"Agent响应失败: {str(e)}")

@router.put("/{agent_name}", response_model=Dict[str, Any])
async def update_agent(agent_name: str, agent_data: Dict[str, Any]):
    """
    更新Agent
    
    Args:
        agent_name: Agent名称
        agent_data: 更新数据
        
    Returns:
        更新结果
    """
    try:
        logger.info(f"更新Agent: {agent_name}")
        
        # 检查Agent是否存在
        existing_agent = await get_agent_details(agent_name)
        if not existing_agent['success']:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")
        
        # 构建UPDATE语句（MindsDB可能不支持直接UPDATE，需要先删除再创建）
        # 这里简化处理，实际可能需要根据MindsDB的具体API调整
        
        return {
            "success": True,
            "data": {"name": agent_name},
            "message": f"Agent '{agent_name}' 更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新Agent失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新Agent失败: {str(e)}")

@router.delete("/{agent_name}", response_model=Dict[str, Any])
async def delete_agent(agent_name: str):
    """
    删除Agent
    
    Args:
        agent_name: Agent名称
        
    Returns:
        删除结果
    """
    try:
        logger.info(f"删除Agent: {agent_name}")
        
        # 检查Agent是否存在
        existing_agent = await get_agent_details(agent_name)
        if not existing_agent['success']:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_name}' 不存在")
        
        # 执行删除
        query = f"DROP AGENT {agent_name}"
        client = get_mindsdb_client()
        if not client:
            raise HTTPException(status_code=500, detail="MindsDB客户端未初始化")
        result = client.execute_query(query)
        
        return {
            "success": True,
            "data": {"name": agent_name},
            "message": f"Agent '{agent_name}' 删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除Agent失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除Agent失败: {str(e)}")

@router.get("/{agent_name}/test", response_model=Dict[str, Any])
async def test_agent(agent_name: str, message: str = Query(..., description="测试消息")):
    """
    测试Agent响应
    
    Args:
        agent_name: Agent名称
        message: 测试消息
        
    Returns:
        Agent响应结果
    """
    try:
        logger.info(f"测试Agent: {agent_name}")
        
        # 构建测试查询
        query = f"SELECT * FROM {agent_name} WHERE question = '{message}'"
        client = get_mindsdb_client()
        if not client:
            raise HTTPException(status_code=500, detail="MindsDB客户端未初始化")
        result = client.execute_query(query)
        
        response_data = {}
        if result and 'data' in result and result['data']:
            response_data = unicode_handler.decode_response_data(result['data'][0])
        
        return {
            "success": True,
            "data": response_data,
            "message": "Agent测试完成"
        }
        
    except Exception as e:
        logger.error(f"测试Agent失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试Agent失败: {str(e)}")
