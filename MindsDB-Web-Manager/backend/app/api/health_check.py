"""
健康检查API
提供系统健康状态查询和管理接口
"""
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging

from core.health_check_system import (
    health_status_manager, 
    health_check_scheduler,
    auto_reconnect_manager,
    HealthStatus,
    ComponentType
)
from core.api_response import APIResponseBuilder
from core.exception_handlers import BusinessException

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/health", tags=["health-check"])

class HealthCheckRequest(BaseModel):
    """健康检查请求"""
    component_name: Optional[str] = Field(None, description="组件名称，为空则检查所有组件")

class ReconnectRequest(BaseModel):
    """重连请求"""
    component_name: str = Field(..., description="要重连的组件名称")

@router.get("/status")
async def get_health_status():
    """获取系统健康状态"""
    try:
        # 获取整体健康状态摘要
        summary = health_status_manager.get_health_summary()
        
        # 获取各组件详细状态
        component_statuses = health_status_manager.get_health_status()
        
        # 转换为API响应格式
        components = {}
        for name, result in component_statuses.items():
            components[name] = result.to_dict()
        
        data = {
            "summary": summary,
            "components": components,
            "scheduler_running": health_check_scheduler.running
        }
        
        return APIResponseBuilder.success(
            data=data,
            message="健康状态获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取健康状态失败: {str(e)}")
        raise BusinessException(f"获取健康状态失败: {str(e)}")

@router.get("/status/{component_name}")
async def get_component_health_status(component_name: str):
    """获取指定组件的健康状态"""
    try:
        result = health_status_manager.get_health_status(component_name)
        
        if not result:
            raise HTTPException(status_code=404, detail=f"组件 {component_name} 不存在或未进行健康检查")
        
        return APIResponseBuilder.success(
            data=result.to_dict(),
            message=f"组件 {component_name} 健康状态获取成功"
        ).dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取组件健康状态失败: {str(e)}")
        raise BusinessException(f"获取组件健康状态失败: {str(e)}")

@router.post("/check")
async def run_health_check(request: HealthCheckRequest):
    """立即执行健康检查"""
    try:
        results = await health_check_scheduler.run_immediate_check(request.component_name)
        
        # 转换结果格式
        check_results = {}
        for name, result in results.items():
            check_results[name] = result.to_dict()
        
        return APIResponseBuilder.success(
            data={
                "results": check_results,
                "total_checked": len(results),
                "healthy_count": len([r for r in results.values() if r.status == HealthStatus.HEALTHY]),
                "unhealthy_count": len([r for r in results.values() if r.status == HealthStatus.UNHEALTHY])
            },
            message="健康检查执行完成"
        ).dict()
        
    except Exception as e:
        logger.error(f"执行健康检查失败: {str(e)}")
        raise BusinessException(f"执行健康检查失败: {str(e)}")

@router.post("/reconnect")
async def trigger_reconnect(request: ReconnectRequest):
    """触发组件重连"""
    try:
        # 检查组件是否存在
        component_status = health_status_manager.get_health_status(request.component_name)
        if not component_status:
            raise HTTPException(status_code=404, detail=f"组件 {request.component_name} 不存在")
        
        # 触发重连
        success = await auto_reconnect_manager.handle_connection_failure(
            request.component_name,
            Exception("手动触发重连")
        )
        
        if success:
            return APIResponseBuilder.success(
                data={"component_name": request.component_name, "reconnect_success": True},
                message=f"组件 {request.component_name} 重连成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message=f"组件 {request.component_name} 重连失败",
                error_code="RECONNECT_FAILED"
            ).dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"触发重连失败: {str(e)}")
        raise BusinessException(f"触发重连失败: {str(e)}")

@router.get("/scheduler/status")
async def get_scheduler_status():
    """获取健康检查调度器状态"""
    try:
        data = {
            "running": health_check_scheduler.running,
            "registered_checkers": list(health_check_scheduler.health_checkers.keys()),
            "check_intervals": health_check_scheduler.check_intervals.copy(),
            "active_tasks": len(health_check_scheduler.tasks)
        }
        
        return APIResponseBuilder.success(
            data=data,
            message="调度器状态获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取调度器状态失败: {str(e)}")
        raise BusinessException(f"获取调度器状态失败: {str(e)}")

@router.post("/scheduler/start")
async def start_scheduler():
    """启动健康检查调度器"""
    try:
        if health_check_scheduler.running:
            return APIResponseBuilder.success(
                data={"already_running": True},
                message="健康检查调度器已经在运行"
            ).dict()
        
        await health_check_scheduler.start()
        
        return APIResponseBuilder.success(
            data={"started": True},
            message="健康检查调度器启动成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"启动调度器失败: {str(e)}")
        raise BusinessException(f"启动调度器失败: {str(e)}")

@router.post("/scheduler/stop")
async def stop_scheduler():
    """停止健康检查调度器"""
    try:
        if not health_check_scheduler.running:
            return APIResponseBuilder.success(
                data={"already_stopped": True},
                message="健康检查调度器已经停止"
            ).dict()
        
        await health_check_scheduler.stop()
        
        return APIResponseBuilder.success(
            data={"stopped": True},
            message="健康检查调度器停止成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"停止调度器失败: {str(e)}")
        raise BusinessException(f"停止调度器失败: {str(e)}")

@router.get("/metrics")
async def get_health_metrics():
    """获取健康检查指标"""
    try:
        # 获取所有组件状态
        component_statuses = health_status_manager.get_health_status()
        
        # 计算指标
        total_components = len(component_statuses)
        healthy_components = len([r for r in component_statuses.values() if r.status == HealthStatus.HEALTHY])
        unhealthy_components = len([r for r in component_statuses.values() if r.status == HealthStatus.UNHEALTHY])
        degraded_components = len([r for r in component_statuses.values() if r.status == HealthStatus.DEGRADED])
        
        # 计算平均响应时间
        response_times = [r.response_time for r in component_statuses.values()]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # 计算健康率
        health_rate = (healthy_components / total_components * 100) if total_components > 0 else 0
        
        data = {
            "total_components": total_components,
            "healthy_components": healthy_components,
            "unhealthy_components": unhealthy_components,
            "degraded_components": degraded_components,
            "health_rate": round(health_rate, 2),
            "average_response_time": round(avg_response_time, 3),
            "component_types": {}
        }
        
        # 按组件类型分组统计
        type_stats = {}
        for result in component_statuses.values():
            component_type = result.component_type.value
            if component_type not in type_stats:
                type_stats[component_type] = {
                    "total": 0,
                    "healthy": 0,
                    "unhealthy": 0,
                    "degraded": 0
                }
            
            type_stats[component_type]["total"] += 1
            if result.status == HealthStatus.HEALTHY:
                type_stats[component_type]["healthy"] += 1
            elif result.status == HealthStatus.UNHEALTHY:
                type_stats[component_type]["unhealthy"] += 1
            elif result.status == HealthStatus.DEGRADED:
                type_stats[component_type]["degraded"] += 1
        
        data["component_types"] = type_stats
        
        return APIResponseBuilder.success(
            data=data,
            message="健康检查指标获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取健康检查指标失败: {str(e)}")
        raise BusinessException(f"获取健康检查指标失败: {str(e)}")

@router.get("/test")
async def test_health_check_system():
    """测试健康检查系统"""
    try:
        test_results = []
        
        # 测试1: 检查调度器状态
        scheduler_running = health_check_scheduler.running
        test_results.append({
            "test": "调度器状态检查",
            "result": "通过" if scheduler_running else "失败",
            "details": f"调度器运行状态: {scheduler_running}"
        })
        
        # 测试2: 检查注册的健康检查器数量
        checker_count = len(health_check_scheduler.health_checkers)
        test_results.append({
            "test": "健康检查器注册",
            "result": "通过" if checker_count > 0 else "警告",
            "details": f"已注册 {checker_count} 个健康检查器"
        })
        
        # 测试3: 执行一次立即健康检查
        try:
            immediate_results = await health_check_scheduler.run_immediate_check()
            test_results.append({
                "test": "立即健康检查",
                "result": "通过",
                "details": f"成功检查 {len(immediate_results)} 个组件"
            })
        except Exception as e:
            test_results.append({
                "test": "立即健康检查",
                "result": "失败",
                "details": f"检查失败: {str(e)}"
            })
        
        # 测试4: 检查健康状态管理器
        summary = health_status_manager.get_health_summary()
        test_results.append({
            "test": "健康状态管理器",
            "result": "通过",
            "details": f"管理 {summary['total_components']} 个组件状态"
        })
        
        # 计算测试通过率
        passed_tests = len([t for t in test_results if t["result"] == "通过"])
        total_tests = len(test_results)
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        return APIResponseBuilder.success(
            data={
                "test_results": test_results,
                "summary": {
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "pass_rate": round(pass_rate, 2)
                }
            },
            message=f"健康检查系统测试完成，通过率: {pass_rate:.1f}%"
        ).dict()
        
    except Exception as e:
        logger.error(f"健康检查系统测试失败: {str(e)}")
        raise BusinessException(f"健康检查系统测试失败: {str(e)}")
