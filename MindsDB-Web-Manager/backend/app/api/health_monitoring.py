"""
健康监控API
提供系统健康检查、监控管理和状态查询的REST接口
"""
from fastapi import APIRouter, Query, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging

from core.system_health_monitor import get_system_health_monitor
from core.mindsdb_health_checker import get_mindsdb_monitor
from core.ai_engine_health_checker import get_ai_engine_monitor
from core.api_response import APIResponseBuilder
from core.exception_handlers import BusinessException

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/health", tags=["health-monitoring"])

class MonitoringConfigRequest(BaseModel):
    """监控配置请求"""
    mindsdb_check_interval: Optional[int] = Field(None, ge=10, le=300, description="MindsDB检查间隔（秒）")
    ai_engine_check_interval: Optional[int] = Field(None, ge=30, le=600, description="AI引擎检查间隔（秒）")
    system_check_interval: Optional[int] = Field(None, ge=15, le=180, description="系统检查间隔（秒）")

@router.get("/status")
async def get_system_health_status():
    """获取系统整体健康状态"""
    try:
        system_monitor = get_system_health_monitor()
        health_summary = system_monitor.get_system_health_summary()
        
        return APIResponseBuilder.success(
            data=health_summary,
            message="系统健康状态获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {str(e)}")
        raise BusinessException(f"获取系统健康状态失败: {str(e)}")

@router.get("/detailed")
async def get_detailed_health_report(
    hours: int = Query(24, ge=1, le=168, description="报告时间范围（小时）")
):
    """获取详细健康报告"""
    try:
        system_monitor = get_system_health_monitor()
        detailed_report = system_monitor.get_detailed_health_report(hours=hours)
        
        return APIResponseBuilder.success(
            data=detailed_report,
            message=f"详细健康报告获取成功（{hours}小时）"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取详细健康报告失败: {str(e)}")
        raise BusinessException(f"获取详细健康报告失败: {str(e)}")

@router.get("/components/mindsdb")
async def get_mindsdb_health():
    """获取MindsDB健康状态"""
    try:
        mindsdb_monitor = get_mindsdb_monitor()
        status = mindsdb_monitor.get_monitoring_status()
        
        return APIResponseBuilder.success(
            data=status,
            message="MindsDB健康状态获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取MindsDB健康状态失败: {str(e)}")
        raise BusinessException(f"获取MindsDB健康状态失败: {str(e)}")

@router.get("/components/ai-engines")
async def get_ai_engines_health():
    """获取AI引擎健康状态"""
    try:
        ai_monitor = get_ai_engine_monitor()
        status = ai_monitor.get_monitoring_status()
        
        return APIResponseBuilder.success(
            data=status,
            message="AI引擎健康状态获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取AI引擎健康状态失败: {str(e)}")
        raise BusinessException(f"获取AI引擎健康状态失败: {str(e)}")

@router.post("/check/mindsdb")
async def trigger_mindsdb_health_check():
    """手动触发MindsDB健康检查"""
    try:
        mindsdb_monitor = get_mindsdb_monitor()
        
        # 获取健康检查器并执行检查
        health_checker = mindsdb_monitor.health_checker
        result = await health_checker.check_with_retry()
        
        return APIResponseBuilder.success(
            data=result.to_dict(),
            message="MindsDB健康检查完成"
        ).dict()
        
    except Exception as e:
        logger.error(f"MindsDB健康检查失败: {str(e)}")
        raise BusinessException(f"MindsDB健康检查失败: {str(e)}")

@router.post("/check/ai-engine/{engine_name}")
async def trigger_ai_engine_health_check(engine_name: str):
    """手动触发AI引擎健康检查"""
    try:
        ai_monitor = get_ai_engine_monitor()
        
        # 检查引擎是否存在
        if engine_name not in ai_monitor.health_checkers:
            return APIResponseBuilder.not_found(
                message=f"AI引擎 {engine_name} 不存在"
            ).dict()
        
        # 执行健康检查
        health_checker = ai_monitor.health_checkers[engine_name]
        result = await health_checker.check_with_retry()
        
        return APIResponseBuilder.success(
            data=result.to_dict(),
            message=f"AI引擎 {engine_name} 健康检查完成"
        ).dict()
        
    except Exception as e:
        logger.error(f"AI引擎 {engine_name} 健康检查失败: {str(e)}")
        raise BusinessException(f"AI引擎 {engine_name} 健康检查失败: {str(e)}")

@router.post("/recover/mindsdb")
async def trigger_mindsdb_recovery():
    """手动触发MindsDB恢复"""
    try:
        mindsdb_monitor = get_mindsdb_monitor()
        
        # 执行恢复操作
        health_checker = mindsdb_monitor.health_checker
        recovery_success = await health_checker.recover()
        
        if recovery_success:
            # 恢复成功后执行健康检查
            result = await health_checker.check_health()
            
            return APIResponseBuilder.success(
                data={
                    "recovery_success": True,
                    "health_check_result": result.to_dict()
                },
                message="MindsDB恢复成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message="MindsDB恢复失败",
                details={"recovery_success": False}
            ).dict()
        
    except Exception as e:
        logger.error(f"MindsDB恢复操作失败: {str(e)}")
        raise BusinessException(f"MindsDB恢复操作失败: {str(e)}")

@router.post("/recover/ai-engine/{engine_name}")
async def trigger_ai_engine_recovery(engine_name: str):
    """手动触发AI引擎恢复"""
    try:
        ai_monitor = get_ai_engine_monitor()
        
        # 检查引擎是否存在
        if engine_name not in ai_monitor.health_checkers:
            return APIResponseBuilder.not_found(
                message=f"AI引擎 {engine_name} 不存在"
            ).dict()
        
        # 执行恢复操作
        health_checker = ai_monitor.health_checkers[engine_name]
        recovery_success = await health_checker.recover()
        
        if recovery_success:
            # 恢复成功后执行健康检查
            result = await health_checker.check_health()
            
            return APIResponseBuilder.success(
                data={
                    "recovery_success": True,
                    "health_check_result": result.to_dict()
                },
                message=f"AI引擎 {engine_name} 恢复成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message=f"AI引擎 {engine_name} 恢复失败",
                details={"recovery_success": False}
            ).dict()
        
    except Exception as e:
        logger.error(f"AI引擎 {engine_name} 恢复操作失败: {str(e)}")
        raise BusinessException(f"AI引擎 {engine_name} 恢复操作失败: {str(e)}")

@router.post("/monitoring/start")
async def start_monitoring():
    """启动系统监控"""
    try:
        system_monitor = get_system_health_monitor()
        
        if system_monitor.is_monitoring:
            return APIResponseBuilder.success(
                data={"already_running": True},
                message="系统监控已在运行"
            ).dict()
        
        await system_monitor.start_monitoring()
        
        return APIResponseBuilder.success(
            data={"monitoring_started": True},
            message="系统监控启动成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"启动系统监控失败: {str(e)}")
        raise BusinessException(f"启动系统监控失败: {str(e)}")

@router.post("/monitoring/stop")
async def stop_monitoring():
    """停止系统监控"""
    try:
        system_monitor = get_system_health_monitor()
        
        if not system_monitor.is_monitoring:
            return APIResponseBuilder.success(
                data={"already_stopped": True},
                message="系统监控未在运行"
            ).dict()
        
        await system_monitor.stop_monitoring()
        
        return APIResponseBuilder.success(
            data={"monitoring_stopped": True},
            message="系统监控停止成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"停止系统监控失败: {str(e)}")
        raise BusinessException(f"停止系统监控失败: {str(e)}")

@router.get("/monitoring/status")
async def get_monitoring_status():
    """获取监控运行状态"""
    try:
        system_monitor = get_system_health_monitor()
        mindsdb_monitor = get_mindsdb_monitor()
        ai_monitor = get_ai_engine_monitor()
        
        status = {
            "system_monitoring": system_monitor.is_monitoring,
            "mindsdb_monitoring": mindsdb_monitor.is_monitoring,
            "ai_engine_monitoring": ai_monitor.is_monitoring,
            "monitoring_intervals": {
                "mindsdb": mindsdb_monitor.check_interval,
                "ai_engines": ai_monitor.check_interval,
                "system": system_monitor.check_interval
            }
        }
        
        return APIResponseBuilder.success(
            data=status,
            message="监控状态获取成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取监控状态失败: {str(e)}")
        raise BusinessException(f"获取监控状态失败: {str(e)}")

@router.get("/trends")
async def get_health_trends(
    component: Optional[str] = Query(None, description="组件名称（mindsdb, ai_engines, system）"),
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）")
):
    """获取健康趋势数据"""
    try:
        if component == "mindsdb":
            mindsdb_monitor = get_mindsdb_monitor()
            health_checker = mindsdb_monitor.health_checker
            trend_data = health_checker.get_health_trend(minutes=hours * 60)
            
        elif component == "ai_engines":
            ai_monitor = get_ai_engine_monitor()
            trend_data = {}
            for engine_name, checker in ai_monitor.health_checkers.items():
                trend_data[engine_name] = checker.get_health_trend(minutes=hours * 60)
                
        elif component == "system":
            system_monitor = get_system_health_monitor()
            trend_data = system_monitor._calculate_trends(
                system_monitor.health_history[-hours*2:]  # 简化的趋势计算
            )
            
        else:
            # 返回所有组件的趋势
            mindsdb_monitor = get_mindsdb_monitor()
            ai_monitor = get_ai_engine_monitor()
            system_monitor = get_system_health_monitor()
            
            trend_data = {
                "mindsdb": mindsdb_monitor.health_checker.get_health_trend(minutes=hours * 60),
                "ai_engines": {
                    engine_name: checker.get_health_trend(minutes=hours * 60)
                    for engine_name, checker in ai_monitor.health_checkers.items()
                },
                "system": system_monitor._calculate_trends(
                    system_monitor.health_history[-hours*2:]
                )
            }
        
        return APIResponseBuilder.success(
            data=trend_data,
            message=f"健康趋势数据获取成功（{hours}小时）"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取健康趋势失败: {str(e)}")
        raise BusinessException(f"获取健康趋势失败: {str(e)}")

@router.get("/alerts")
async def get_recent_alerts(
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）")
):
    """获取最近的告警信息"""
    try:
        # 这里可以从日志或专门的告警存储中获取告警信息
        # 目前返回模拟数据
        alerts = [
            {
                "timestamp": "2025-01-09T14:30:00Z",
                "component": "mindsdb",
                "severity": "warning",
                "message": "连接响应时间较慢",
                "resolved": True
            },
            {
                "timestamp": "2025-01-09T13:15:00Z",
                "component": "ai_engine_default",
                "severity": "error",
                "message": "AI引擎调用失败",
                "resolved": True
            }
        ]
        
        return APIResponseBuilder.success(
            data={"alerts": alerts, "time_range_hours": hours},
            message=f"最近告警信息获取成功（{hours}小时）"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取告警信息失败: {str(e)}")
        raise BusinessException(f"获取告警信息失败: {str(e)}")
