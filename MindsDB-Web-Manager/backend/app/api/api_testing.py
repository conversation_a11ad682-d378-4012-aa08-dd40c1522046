"""
API测试模块
提供API请求测试、代理和模拟功能
"""
from fastapi import APIRouter, HTTPException, Query, Request, Body
from typing import List, Dict, Any, Optional
import logging
import time
import json
import requests
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 尝试导入核心模块
try:
    from core.models import APIResponse
    from core.api_utils import create_success_response, create_error_response
    from core.exceptions import ValidationException
    CORE_MODULES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"核心模块导入失败: {e}")
    CORE_MODULES_AVAILABLE = False

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/testing", tags=["api-testing"])

# API测试历史记录
test_history = []

@router.post("/request", response_model=Dict[str, Any])
async def test_api_request(request: Request, test_data: Dict[str, Any]):
    """
    执行API请求测试
    
    Args:
        test_data: 包含请求配置的数据
        
    Returns:
        API请求结果
    """
    try:
        logger.info("执行API请求测试")
        
        # 验证必需字段
        if 'url' not in test_data:
            raise ValidationException("缺少URL参数")
        
        url = test_data['url']
        method = test_data.get('method', 'GET').upper()
        headers = test_data.get('headers', {})
        params = test_data.get('params', {})
        body = test_data.get('body', None)
        timeout = test_data.get('timeout', 30)
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行请求
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=timeout)
            elif method == 'POST':
                if isinstance(body, str):
                    response = requests.post(url, headers=headers, params=params, data=body, timeout=timeout)
                else:
                    response = requests.post(url, headers=headers, params=params, json=body, timeout=timeout)
            elif method == 'PUT':
                if isinstance(body, str):
                    response = requests.put(url, headers=headers, params=params, data=body, timeout=timeout)
                else:
                    response = requests.put(url, headers=headers, params=params, json=body, timeout=timeout)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers, params=params, timeout=timeout)
            elif method == 'PATCH':
                if isinstance(body, str):
                    response = requests.patch(url, headers=headers, params=params, data=body, timeout=timeout)
                else:
                    response = requests.patch(url, headers=headers, params=params, json=body, timeout=timeout)
            else:
                raise ValidationException(f"不支持的HTTP方法: {method}")
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            # 解析响应内容
            try:
                response_json = response.json()
            except:
                response_json = None
            
            # 构建结果
            result = {
                "request": {
                    "url": url,
                    "method": method,
                    "headers": headers,
                    "params": params,
                    "body": body
                },
                "response": {
                    "status_code": response.status_code,
                    "status_text": response.reason,
                    "headers": dict(response.headers),
                    "content": response.text,
                    "json": response_json,
                    "size": len(response.content),
                    "response_time": round(response_time, 3)
                },
                "timestamp": datetime.now().isoformat(),
                "success": 200 <= response.status_code < 400
            }
            
            # 保存到历史记录
            save_test_history(result)
            
            if CORE_MODULES_AVAILABLE:
                response_obj = create_success_response(
                    result,
                    "API请求测试完成",
                    request
                )
                return response_obj.model_dump()
            else:
                return {
                    "success": True,
                    "message": "API请求测试完成",
                    "data": result
                }
            
        except requests.exceptions.Timeout:
            error_result = {
                "request": {
                    "url": url,
                    "method": method,
                    "headers": headers,
                    "params": params,
                    "body": body
                },
                "error": "请求超时",
                "timestamp": datetime.now().isoformat(),
                "success": False
            }
            save_test_history(error_result)
            raise ValidationException("请求超时")
            
        except requests.exceptions.ConnectionError:
            error_result = {
                "request": {
                    "url": url,
                    "method": method,
                    "headers": headers,
                    "params": params,
                    "body": body
                },
                "error": "连接失败",
                "timestamp": datetime.now().isoformat(),
                "success": False
            }
            save_test_history(error_result)
            raise ValidationException("连接失败")
            
    except ValidationException as e:
        logger.warning(f"API测试验证失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(str(e), None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": str(e),
                "data": None
            }
    except Exception as e:
        logger.error(f"API测试失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"API测试失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"API测试失败: {str(e)}",
                "data": None
            }

@router.get("/history", response_model=Dict[str, Any])
async def get_test_history(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量")
):
    """
    获取API测试历史记录
    
    Args:
        page: 页码
        limit: 每页数量
        
    Returns:
        测试历史列表
    """
    try:
        logger.info(f"获取API测试历史 - 页码: {page}, 每页: {limit}")
        
        # 计算分页
        start_index = (page - 1) * limit
        end_index = start_index + limit
        
        # 获取历史记录（按时间倒序）
        sorted_history = sorted(test_history, key=lambda x: x['timestamp'], reverse=True)
        page_history = sorted_history[start_index:end_index]
        
        result = {
            "history": page_history,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": len(test_history),
                "total_pages": (len(test_history) + limit - 1) // limit
            }
        }
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                result,
                "获取测试历史成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "获取测试历史成功",
                "data": result
            }
        
    except Exception as e:
        logger.error(f"获取测试历史失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"获取测试历史失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"获取测试历史失败: {str(e)}",
                "data": None
            }

@router.delete("/history", response_model=Dict[str, Any])
async def clear_test_history(request: Request):
    """
    清空测试历史记录
    
    Returns:
        操作结果
    """
    try:
        logger.info("清空API测试历史")
        
        global test_history
        cleared_count = len(test_history)
        test_history.clear()
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                {"cleared_count": cleared_count},
                "测试历史已清空",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "测试历史已清空",
                "data": {"cleared_count": cleared_count}
            }
        
    except Exception as e:
        logger.error(f"清空测试历史失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"清空测试历史失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"清空测试历史失败: {str(e)}",
                "data": None
            }

@router.get("/templates", response_model=Dict[str, Any])
async def get_request_templates(request: Request):
    """
    获取API请求模板
    
    Returns:
        请求模板列表
    """
    try:
        templates = [
            {
                "id": "get_health",
                "name": "健康检查",
                "description": "检查API服务健康状态",
                "method": "GET",
                "url": "http://localhost:8081/health",
                "headers": {},
                "params": {},
                "body": None,
                "category": "系统"
            },
            {
                "id": "get_agents",
                "name": "获取Agent列表",
                "description": "获取所有AI Agent",
                "method": "GET",
                "url": "http://localhost:8081/api/agents/",
                "headers": {},
                "params": {"page": 1, "limit": 10},
                "body": None,
                "category": "Agent"
            },
            {
                "id": "sql_query",
                "name": "SQL查询",
                "description": "执行SQL查询",
                "method": "POST",
                "url": "http://localhost:8081/api/sql/execute",
                "headers": {"Content-Type": "application/json"},
                "params": {},
                "body": {"query": "SHOW DATABASES;"},
                "category": "数据库"
            },
            {
                "id": "get_datasources",
                "name": "获取数据源",
                "description": "获取数据源列表",
                "method": "GET",
                "url": "http://localhost:8081/api/datasources/",
                "headers": {},
                "params": {"page": 1, "limit": 10},
                "body": None,
                "category": "数据源"
            },
            {
                "id": "mindsdb_status",
                "name": "MindsDB状态",
                "description": "检查MindsDB服务状态",
                "method": "GET",
                "url": "http://localhost:8081/mindsdb/status",
                "headers": {},
                "params": {},
                "body": None,
                "category": "MindsDB"
            },
            {
                "id": "dashboard_overview",
                "name": "仪表板概览",
                "description": "获取系统概览信息",
                "method": "GET",
                "url": "http://localhost:8081/api/dashboard/overview",
                "headers": {},
                "params": {},
                "body": None,
                "category": "仪表板"
            }
        ]
        
        if CORE_MODULES_AVAILABLE:
            response = create_success_response(
                {"templates": templates},
                "获取请求模板成功",
                request
            )
            return response.model_dump()
        else:
            return {
                "success": True,
                "message": "获取请求模板成功",
                "data": {"templates": templates}
            }
        
    except Exception as e:
        logger.error(f"获取请求模板失败: {str(e)}")
        if CORE_MODULES_AVAILABLE:
            response = create_error_response(f"获取请求模板失败: {str(e)}", None, request)
            return response.model_dump()
        else:
            return {
                "success": False,
                "message": f"获取请求模板失败: {str(e)}",
                "data": None
            }

def save_test_history(test_result: Dict[str, Any]):
    """
    保存测试历史记录
    
    Args:
        test_result: 测试结果
    """
    global test_history
    
    # 添加ID
    test_result['id'] = len(test_history) + 1
    
    test_history.append(test_result)
    
    # 限制历史记录数量（保留最近100条）
    if len(test_history) > 100:
        test_history = test_history[-100:]
