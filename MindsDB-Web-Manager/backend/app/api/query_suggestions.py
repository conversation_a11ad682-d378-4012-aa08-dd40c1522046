"""
动态查询建议生成API - 任务#122
基于表结构分析和Agent技能生成智能查询建议
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
import json
import logging
from datetime import datetime
import re

from ..core.mindsdb_client import get_mindsdb_client
from ..core.config import get_settings
from ..core.feature_flags import check_feature_enabled

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/query-suggestions", tags=["查询建议"])

# 请求模型
class QuerySuggestionRequest(BaseModel):
    """查询建议请求"""
    database: str = Field(..., description="数据库名称")
    tables: Optional[List[str]] = Field(None, description="选定的表列表")
    agent_name: Optional[str] = Field(None, description="Agent名称")
    context: Optional[str] = Field(None, description="查询上下文")
    suggestion_type: str = Field("comprehensive", description="建议类型: basic, comprehensive, advanced")
    max_suggestions: int = Field(10, description="最大建议数量")
    include_sql: bool = Field(True, description="是否包含SQL示例")
    user_level: str = Field("intermediate", description="用户水平: beginner, intermediate, advanced")

class TableAnalysisRequest(BaseModel):
    """表分析请求"""
    database: str = Field(..., description="数据库名称")
    table_name: str = Field(..., description="表名")
    analysis_depth: str = Field("standard", description="分析深度: basic, standard, deep")

class SuggestionResponse(BaseModel):
    """建议响应"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    error: Optional[str] = None

# 依赖函数
def check_query_suggestion_features():
    """检查查询建议功能是否启用"""
    if not check_feature_enabled("FEATURE_QUERY_SUGGESTIONS"):
        raise HTTPException(
            status_code=503,
            detail="查询建议功能未启用，请联系管理员"
        )

@router.post("/generate", response_model=SuggestionResponse)
async def generate_query_suggestions(
    request: QuerySuggestionRequest,
    _: None = Depends(check_query_suggestion_features)
):
    """
    生成动态查询建议 - 任务#122核心功能
    """
    try:
        logger.info(f"开始生成查询建议 - 数据库: {request.database}")
        
        # 获取表结构信息
        table_structures = await get_table_structures(request.database, request.tables)
        
        # 获取Agent技能信息
        agent_skills = await get_agent_skills(request.agent_name) if request.agent_name else None
        
        # 分析查询上下文
        context_analysis = await analyze_query_context(request.context, table_structures)
        
        # 生成查询建议
        suggestions = await generate_intelligent_suggestions(
            table_structures, 
            agent_skills, 
            context_analysis, 
            request
        )
        
        # 排序和过滤建议
        filtered_suggestions = await filter_and_rank_suggestions(
            suggestions, 
            request.max_suggestions,
            request.user_level
        )
        
        # 构建响应
        response_data = {
            "suggestions": filtered_suggestions,
            "metadata": {
                "database": request.database,
                "tables_analyzed": len(table_structures),
                "agent_name": request.agent_name,
                "suggestion_count": len(filtered_suggestions),
                "generated_at": datetime.now().isoformat(),
                "suggestion_type": request.suggestion_type
            },
            "context_analysis": context_analysis,
            "table_summary": await summarize_table_structures(table_structures)
        }
        
        logger.info(f"查询建议生成成功 - 生成{len(filtered_suggestions)}个建议")
        return SuggestionResponse(success=True, data=response_data)
        
    except Exception as e:
        logger.error(f"生成查询建议失败: {str(e)}")
        return SuggestionResponse(
            success=False,
            error=f"生成查询建议失败: {str(e)}"
        )

@router.post("/analyze-table", response_model=SuggestionResponse)
async def analyze_table_for_suggestions(
    request: TableAnalysisRequest,
    _: None = Depends(check_query_suggestion_features)
):
    """
    分析单个表以生成建议
    """
    try:
        logger.info(f"分析表结构 - {request.database}.{request.table_name}")
        
        # 获取表结构详细信息
        table_info = await get_detailed_table_info(request.database, request.table_name)
        
        # 生成表特定的查询建议
        table_suggestions = await generate_table_specific_suggestions(
            table_info, 
            request.analysis_depth
        )
        
        response_data = {
            "table_info": table_info,
            "suggestions": table_suggestions,
            "analysis_depth": request.analysis_depth,
            "analyzed_at": datetime.now().isoformat()
        }
        
        return SuggestionResponse(success=True, data=response_data)
        
    except Exception as e:
        logger.error(f"表分析失败: {str(e)}")
        return SuggestionResponse(
            success=False,
            error=f"表分析失败: {str(e)}"
        )

@router.get("/patterns/{database}", response_model=SuggestionResponse)
async def get_query_patterns(
    database: str,
    pattern_type: str = "common",
    _: None = Depends(check_query_suggestion_features)
):
    """
    获取常见查询模式
    """
    try:
        logger.info(f"获取查询模式 - 数据库: {database}, 类型: {pattern_type}")
        
        # 获取数据库表结构
        tables = await get_database_tables(database)
        
        # 生成查询模式
        patterns = await generate_query_patterns(tables, pattern_type)
        
        response_data = {
            "database": database,
            "pattern_type": pattern_type,
            "patterns": patterns,
            "table_count": len(tables),
            "generated_at": datetime.now().isoformat()
        }
        
        return SuggestionResponse(success=True, data=response_data)
        
    except Exception as e:
        logger.error(f"获取查询模式失败: {str(e)}")
        return SuggestionResponse(
            success=False,
            error=f"获取查询模式失败: {str(e)}"
        )

# 核心处理函数
async def get_table_structures(database: str, tables: Optional[List[str]] = None) -> Dict[str, Any]:
    """获取表结构信息"""
    try:
        client = get_mindsdb_client()
        
        # 如果没有指定表，获取所有表
        if not tables:
            tables = await get_database_tables(database)
        
        table_structures = {}
        
        for table in tables:
            try:
                # 获取表结构
                structure_query = f"DESCRIBE {database}.{table}"
                result = await client.query(structure_query)
                
                # 获取样本数据
                sample_query = f"SELECT * FROM {database}.{table} LIMIT 5"
                sample_result = await client.query(sample_query)
                
                table_structures[table] = {
                    "structure": result,
                    "sample_data": sample_result,
                    "column_count": len(result) if result else 0,
                    "analyzed_at": datetime.now().isoformat()
                }
                
            except Exception as e:
                logger.warning(f"获取表{table}结构失败: {str(e)}")
                table_structures[table] = {
                    "structure": None,
                    "sample_data": None,
                    "error": str(e)
                }
        
        return table_structures
        
    except Exception as e:
        logger.error(f"获取表结构失败: {str(e)}")
        return {}

async def get_agent_skills(agent_name: str) -> Optional[Dict[str, Any]]:
    """获取Agent技能信息"""
    try:
        if not agent_name:
            return None
            
        client = get_mindsdb_client()
        
        # 获取Agent详细信息
        agent_query = f"SELECT * FROM mindsdb.agents WHERE name = '{agent_name}'"
        agent_result = await client.query(agent_query)
        
        if not agent_result:
            return None
        
        # 获取Agent的技能
        skills_query = f"SELECT * FROM mindsdb.skills WHERE agent_name = '{agent_name}'"
        skills_result = await client.query(skills_query)
        
        return {
            "agent_info": agent_result[0] if agent_result else None,
            "skills": skills_result or [],
            "skill_count": len(skills_result) if skills_result else 0,
            "text2sql_skills": [skill for skill in (skills_result or []) if 'text2sql' in skill.get('type', '').lower()]
        }
        
    except Exception as e:
        logger.error(f"获取Agent技能失败: {str(e)}")
        return None

async def analyze_query_context(context: Optional[str], table_structures: Dict[str, Any]) -> Dict[str, Any]:
    """分析查询上下文"""
    try:
        analysis = {
            "has_context": bool(context),
            "context_length": len(context) if context else 0,
            "keywords": [],
            "intent": "general",
            "complexity": "basic",
            "suggested_tables": [],
            "query_type": "unknown"
        }
        
        if not context:
            return analysis
        
        context_lower = context.lower()
        
        # 关键词提取
        keywords = extract_keywords_from_context(context_lower)
        analysis["keywords"] = keywords
        
        # 查询意图分析
        analysis["intent"] = analyze_query_intent(context_lower)
        
        # 查询类型识别
        analysis["query_type"] = identify_query_type(context_lower)
        
        # 复杂度评估
        analysis["complexity"] = assess_query_complexity(context_lower, keywords)
        
        # 建议相关表
        analysis["suggested_tables"] = suggest_relevant_tables(keywords, table_structures)
        
        return analysis
        
    except Exception as e:
        logger.error(f"分析查询上下文失败: {str(e)}")
        return {"error": str(e)}

def extract_keywords_from_context(context: str) -> List[str]:
    """从上下文中提取关键词"""
    # 常见的查询关键词
    query_keywords = [
        "查询", "查找", "搜索", "统计", "计算", "分析", "对比", "排序",
        "select", "count", "sum", "avg", "max", "min", "group", "order",
        "where", "join", "union", "distinct", "having"
    ]
    
    # 数据相关关键词
    data_keywords = [
        "用户", "订单", "产品", "销售", "客户", "价格", "数量", "时间",
        "日期", "月份", "年份", "地区", "类别", "状态", "金额"
    ]
    
    found_keywords = []
    all_keywords = query_keywords + data_keywords
    
    for keyword in all_keywords:
        if keyword in context:
            found_keywords.append(keyword)
    
    return found_keywords

def analyze_query_intent(context: str) -> str:
    """分析查询意图"""
    if any(word in context for word in ["统计", "计算", "总计", "平均", "count", "sum", "avg"]):
        return "statistical"
    elif any(word in context for word in ["查询", "查找", "搜索", "显示", "select", "show"]):
        return "search"
    elif any(word in context for word in ["分析", "对比", "比较", "趋势", "analyze", "compare"]):
        return "analysis"
    elif any(word in context for word in ["排序", "排名", "最大", "最小", "order", "rank", "top"]):
        return "ranking"
    else:
        return "general"

def identify_query_type(context: str) -> str:
    """识别查询类型"""
    if any(word in context for word in ["join", "关联", "连接", "合并"]):
        return "join"
    elif any(word in context for word in ["group", "分组", "按照", "分类"]):
        return "aggregation"
    elif any(word in context for word in ["where", "条件", "筛选", "过滤"]):
        return "filter"
    elif any(word in context for word in ["order", "排序", "排列"]):
        return "sort"
    else:
        return "basic"

def assess_query_complexity(context: str, keywords: List[str]) -> str:
    """评估查询复杂度"""
    complexity_score = 0
    
    # 基于关键词数量
    complexity_score += len(keywords)
    
    # 基于特定复杂操作
    complex_operations = ["join", "subquery", "union", "having", "window", "case"]
    for op in complex_operations:
        if op in context:
            complexity_score += 3
    
    # 基于聚合函数
    aggregate_functions = ["count", "sum", "avg", "max", "min", "group_concat"]
    for func in aggregate_functions:
        if func in context:
            complexity_score += 2
    
    if complexity_score <= 3:
        return "basic"
    elif complexity_score <= 8:
        return "intermediate"
    else:
        return "advanced"

def suggest_relevant_tables(keywords: List[str], table_structures: Dict[str, Any]) -> List[str]:
    """基于关键词建议相关表"""
    relevant_tables = []
    
    for table_name, table_info in table_structures.items():
        relevance_score = 0
        
        # 表名匹配
        for keyword in keywords:
            if keyword in table_name.lower():
                relevance_score += 3
        
        # 列名匹配
        if table_info.get("structure"):
            for column in table_info["structure"]:
                column_name = column.get("Field", "").lower()
                for keyword in keywords:
                    if keyword in column_name:
                        relevance_score += 2
        
        if relevance_score > 0:
            relevant_tables.append({
                "table": table_name,
                "relevance_score": relevance_score
            })
    
    # 按相关性排序
    relevant_tables.sort(key=lambda x: x["relevance_score"], reverse=True)
    
    return [table["table"] for table in relevant_tables[:5]]

async def generate_intelligent_suggestions(
    table_structures: Dict[str, Any],
    agent_skills: Optional[Dict[str, Any]],
    context_analysis: Dict[str, Any],
    request: QuerySuggestionRequest
) -> List[Dict[str, Any]]:
    """生成智能查询建议 - 任务#122核心算法"""
    try:
        suggestions = []

        # 1. 基于表结构的基础建议
        basic_suggestions = await generate_basic_table_suggestions(table_structures, request)
        suggestions.extend(basic_suggestions)

        # 2. 基于Agent技能的建议
        if agent_skills:
            skill_suggestions = await generate_skill_based_suggestions(
                table_structures, agent_skills, request
            )
            suggestions.extend(skill_suggestions)

        # 3. 基于上下文的智能建议
        if context_analysis.get("has_context"):
            context_suggestions = await generate_context_based_suggestions(
                table_structures, context_analysis, request
            )
            suggestions.extend(context_suggestions)

        # 4. 基于查询模式的建议
        pattern_suggestions = await generate_pattern_based_suggestions(
            table_structures, context_analysis, request
        )
        suggestions.extend(pattern_suggestions)

        # 5. 基于关系的高级建议
        if request.suggestion_type in ["comprehensive", "advanced"]:
            relationship_suggestions = await generate_relationship_suggestions(
                table_structures, request
            )
            suggestions.extend(relationship_suggestions)

        return suggestions

    except Exception as e:
        logger.error(f"生成智能建议失败: {str(e)}")
        return []

async def generate_basic_table_suggestions(
    table_structures: Dict[str, Any],
    request: QuerySuggestionRequest
) -> List[Dict[str, Any]]:
    """生成基于表结构的基础建议"""
    suggestions = []

    for table_name, table_info in table_structures.items():
        if not table_info.get("structure"):
            continue

        structure = table_info["structure"]

        # 基础查询建议
        suggestions.append({
            "type": "basic_select",
            "priority": 8,
            "category": "基础查询",
            "title": f"查看{table_name}表的所有数据",
            "description": f"获取{table_name}表中的所有记录",
            "natural_language": f"显示{table_name}表的所有数据",
            "sql": f"SELECT * FROM {request.database}.{table_name} LIMIT 100;",
            "complexity": "beginner",
            "estimated_time": "快速",
            "table": table_name
        })

        # 计数查询
        suggestions.append({
            "type": "count",
            "priority": 7,
            "category": "统计查询",
            "title": f"统计{table_name}表的记录数",
            "description": f"计算{table_name}表中的总记录数",
            "natural_language": f"统计{table_name}表有多少条记录",
            "sql": f"SELECT COUNT(*) as total_records FROM {request.database}.{table_name};",
            "complexity": "beginner",
            "estimated_time": "快速",
            "table": table_name
        })

        # 基于列类型的建议
        for column in structure:
            column_name = column.get("Field", "")
            column_type = column.get("Type", "").lower()

            # 数值列的统计建议
            if any(num_type in column_type for num_type in ["int", "decimal", "float", "double"]):
                suggestions.append({
                    "type": "numeric_stats",
                    "priority": 6,
                    "category": "数值统计",
                    "title": f"分析{table_name}表的{column_name}字段统计",
                    "description": f"计算{column_name}字段的最大值、最小值、平均值等统计信息",
                    "natural_language": f"分析{table_name}表中{column_name}字段的统计信息",
                    "sql": f"SELECT MIN({column_name}) as min_value, MAX({column_name}) as max_value, AVG({column_name}) as avg_value, COUNT({column_name}) as count FROM {request.database}.{table_name};",
                    "complexity": "intermediate",
                    "estimated_time": "中等",
                    "table": table_name,
                    "column": column_name
                })

            # 文本列的分组建议
            elif any(text_type in column_type for text_type in ["varchar", "text", "char"]):
                suggestions.append({
                    "type": "group_by",
                    "priority": 5,
                    "category": "分组统计",
                    "title": f"按{column_name}分组统计{table_name}表",
                    "description": f"按{column_name}字段分组，统计每个分组的记录数",
                    "natural_language": f"按{column_name}分组统计{table_name}表的数据分布",
                    "sql": f"SELECT {column_name}, COUNT(*) as count FROM {request.database}.{table_name} GROUP BY {column_name} ORDER BY count DESC;",
                    "complexity": "intermediate",
                    "estimated_time": "中等",
                    "table": table_name,
                    "column": column_name
                })

            # 日期列的时间分析建议
            elif any(date_type in column_type for date_type in ["date", "datetime", "timestamp"]):
                suggestions.append({
                    "type": "time_analysis",
                    "priority": 6,
                    "category": "时间分析",
                    "title": f"分析{table_name}表的{column_name}时间分布",
                    "description": f"按时间维度分析{column_name}字段的数据分布",
                    "natural_language": f"分析{table_name}表中{column_name}字段的时间分布趋势",
                    "sql": f"SELECT DATE({column_name}) as date, COUNT(*) as count FROM {request.database}.{table_name} GROUP BY DATE({column_name}) ORDER BY date DESC LIMIT 30;",
                    "complexity": "intermediate",
                    "estimated_time": "中等",
                    "table": table_name,
                    "column": column_name
                })

    return suggestions

async def generate_skill_based_suggestions(
    table_structures: Dict[str, Any],
    agent_skills: Dict[str, Any],
    request: QuerySuggestionRequest
) -> List[Dict[str, Any]]:
    """基于Agent技能生成建议"""
    suggestions = []

    if not agent_skills or not agent_skills.get("text2sql_skills"):
        return suggestions

    text2sql_skills = agent_skills["text2sql_skills"]

    for skill in text2sql_skills:
        skill_name = skill.get("name", "")
        skill_description = skill.get("description", "")

        # 基于技能生成相关查询建议
        if "user" in skill_name.lower() or "用户" in skill_description:
            suggestions.append({
                "type": "skill_based",
                "priority": 9,
                "category": "Agent技能",
                "title": f"使用{skill_name}技能查询用户信息",
                "description": f"利用Agent的{skill_name}技能进行用户相关查询",
                "natural_language": "查询用户相关信息",
                "sql": "-- 此查询将使用Agent的专业技能执行",
                "complexity": "intermediate",
                "estimated_time": "中等",
                "skill_name": skill_name,
                "agent_name": request.agent_name
            })

        elif "order" in skill_name.lower() or "订单" in skill_description:
            suggestions.append({
                "type": "skill_based",
                "priority": 9,
                "category": "Agent技能",
                "title": f"使用{skill_name}技能分析订单数据",
                "description": f"利用Agent的{skill_name}技能进行订单分析",
                "natural_language": "分析订单相关数据",
                "sql": "-- 此查询将使用Agent的专业技能执行",
                "complexity": "intermediate",
                "estimated_time": "中等",
                "skill_name": skill_name,
                "agent_name": request.agent_name
            })

    return suggestions

async def generate_context_based_suggestions(
    table_structures: Dict[str, Any],
    context_analysis: Dict[str, Any],
    request: QuerySuggestionRequest
) -> List[Dict[str, Any]]:
    """基于上下文生成建议"""
    suggestions = []

    intent = context_analysis.get("intent", "general")
    query_type = context_analysis.get("query_type", "basic")
    keywords = context_analysis.get("keywords", [])
    suggested_tables = context_analysis.get("suggested_tables", [])

    # 基于查询意图的建议
    if intent == "statistical":
        for table in suggested_tables[:3]:
            suggestions.append({
                "type": "context_statistical",
                "priority": 10,
                "category": "上下文建议",
                "title": f"统计分析{table}表数据",
                "description": f"基于您的查询意图，对{table}表进行统计分析",
                "natural_language": f"对{table}表进行统计分析",
                "sql": f"SELECT COUNT(*) as total, AVG(CASE WHEN ISNUMERIC(column_name) THEN CAST(column_name AS DECIMAL) END) as avg_value FROM {request.database}.{table};",
                "complexity": "intermediate",
                "estimated_time": "中等",
                "table": table,
                "context_intent": intent
            })

    elif intent == "search":
        for table in suggested_tables[:3]:
            suggestions.append({
                "type": "context_search",
                "priority": 10,
                "category": "上下文建议",
                "title": f"搜索{table}表中的相关数据",
                "description": f"基于您的搜索意图，在{table}表中查找相关信息",
                "natural_language": f"在{table}表中搜索相关数据",
                "sql": f"SELECT * FROM {request.database}.{table} WHERE 1=1 -- 添加具体搜索条件",
                "complexity": "beginner",
                "estimated_time": "快速",
                "table": table,
                "context_intent": intent
            })

    # 基于关键词的建议
    for keyword in keywords[:5]:
        if keyword in ["时间", "日期", "date", "time"]:
            suggestions.append({
                "type": "context_temporal",
                "priority": 8,
                "category": "时间相关",
                "title": f"基于时间维度的数据分析",
                "description": f"根据关键词'{keyword}'，进行时间相关的数据分析",
                "natural_language": f"按时间分析数据趋势",
                "sql": f"-- 时间相关查询，基于关键词: {keyword}",
                "complexity": "intermediate",
                "estimated_time": "中等",
                "keyword": keyword
            })

    return suggestions

async def generate_pattern_based_suggestions(
    table_structures: Dict[str, Any],
    context_analysis: Dict[str, Any],
    request: QuerySuggestionRequest
) -> List[Dict[str, Any]]:
    """基于查询模式生成建议"""
    suggestions = []

    # 常见查询模式
    patterns = [
        {
            "name": "top_n",
            "title": "Top N 查询",
            "description": "查找排名前N的记录",
            "template": "SELECT * FROM {database}.{table} ORDER BY {column} DESC LIMIT {n};"
        },
        {
            "name": "recent_data",
            "title": "最近数据查询",
            "description": "查询最近的数据记录",
            "template": "SELECT * FROM {database}.{table} WHERE {date_column} >= DATE_SUB(NOW(), INTERVAL 7 DAY);"
        },
        {
            "name": "duplicate_check",
            "title": "重复数据检查",
            "description": "检查表中的重复记录",
            "template": "SELECT {column}, COUNT(*) as count FROM {database}.{table} GROUP BY {column} HAVING COUNT(*) > 1;"
        },
        {
            "name": "null_analysis",
            "title": "空值分析",
            "description": "分析表中的空值情况",
            "template": "SELECT COUNT(*) as total, COUNT({column}) as non_null, COUNT(*) - COUNT({column}) as null_count FROM {database}.{table};"
        }
    ]

    for table_name, table_info in table_structures.items():
        if not table_info.get("structure"):
            continue

        structure = table_info["structure"]

        for pattern in patterns:
            # 为每个模式生成具体建议
            if pattern["name"] == "top_n":
                # 找数值列用于排序
                numeric_columns = [col["Field"] for col in structure
                                 if any(t in col.get("Type", "").lower() for t in ["int", "decimal", "float"])]

                for col in numeric_columns[:2]:  # 限制数量
                    suggestions.append({
                        "type": "pattern_top_n",
                        "priority": 7,
                        "category": "查询模式",
                        "title": f"查询{table_name}表中{col}最高的10条记录",
                        "description": pattern["description"],
                        "natural_language": f"显示{table_name}表中{col}值最大的前10条记录",
                        "sql": pattern["template"].format(
                            database=request.database,
                            table=table_name,
                            column=col,
                            n=10
                        ),
                        "complexity": "intermediate",
                        "estimated_time": "快速",
                        "table": table_name,
                        "pattern": pattern["name"]
                    })

            elif pattern["name"] == "recent_data":
                # 找日期列
                date_columns = [col["Field"] for col in structure
                              if any(t in col.get("Type", "").lower() for t in ["date", "datetime", "timestamp"])]

                for col in date_columns[:1]:  # 只取第一个日期列
                    suggestions.append({
                        "type": "pattern_recent",
                        "priority": 6,
                        "category": "查询模式",
                        "title": f"查询{table_name}表最近7天的数据",
                        "description": pattern["description"],
                        "natural_language": f"显示{table_name}表最近一周的记录",
                        "sql": pattern["template"].format(
                            database=request.database,
                            table=table_name,
                            date_column=col
                        ),
                        "complexity": "intermediate",
                        "estimated_time": "中等",
                        "table": table_name,
                        "pattern": pattern["name"]
                    })

    return suggestions

async def generate_relationship_suggestions(
    table_structures: Dict[str, Any],
    request: QuerySuggestionRequest
) -> List[Dict[str, Any]]:
    """生成基于表关系的高级建议"""
    suggestions = []

    tables = list(table_structures.keys())

    # 如果有多个表，生成JOIN建议
    if len(tables) >= 2:
        for i, table1 in enumerate(tables):
            for table2 in tables[i+1:]:
                # 尝试找到可能的关联字段
                table1_structure = table_structures[table1].get("structure", [])
                table2_structure = table_structures[table2].get("structure", [])

                table1_columns = [col["Field"] for col in table1_structure]
                table2_columns = [col["Field"] for col in table2_structure]

                # 寻找可能的关联字段
                potential_joins = []
                for col1 in table1_columns:
                    for col2 in table2_columns:
                        if (col1 == col2 or
                            col1.endswith("_id") and col2.startswith(col1[:-3]) or
                            col2.endswith("_id") and col1.startswith(col2[:-3])):
                            potential_joins.append((col1, col2))

                # 生成JOIN建议
                for col1, col2 in potential_joins[:2]:  # 限制数量
                    suggestions.append({
                        "type": "relationship_join",
                        "priority": 8,
                        "category": "表关联",
                        "title": f"关联查询{table1}和{table2}表",
                        "description": f"通过{col1}和{col2}字段关联两个表的数据",
                        "natural_language": f"关联{table1}表和{table2}表，显示相关信息",
                        "sql": f"SELECT t1.*, t2.* FROM {request.database}.{table1} t1 JOIN {request.database}.{table2} t2 ON t1.{col1} = t2.{col2} LIMIT 100;",
                        "complexity": "advanced",
                        "estimated_time": "较慢",
                        "tables": [table1, table2],
                        "join_columns": [col1, col2]
                    })

    return suggestions

async def filter_and_rank_suggestions(
    suggestions: List[Dict[str, Any]],
    max_suggestions: int,
    user_level: str
) -> List[Dict[str, Any]]:
    """过滤和排序建议"""
    try:
        # 根据用户水平过滤
        level_filter = {
            "beginner": ["beginner"],
            "intermediate": ["beginner", "intermediate"],
            "advanced": ["beginner", "intermediate", "advanced"]
        }

        allowed_complexities = level_filter.get(user_level, ["beginner", "intermediate"])

        # 过滤复杂度
        filtered_suggestions = [
            s for s in suggestions
            if s.get("complexity", "beginner") in allowed_complexities
        ]

        # 按优先级排序
        filtered_suggestions.sort(key=lambda x: x.get("priority", 0), reverse=True)

        # 去重（基于SQL或标题）
        seen_sql = set()
        unique_suggestions = []

        for suggestion in filtered_suggestions:
            sql = suggestion.get("sql", "")
            title = suggestion.get("title", "")
            key = sql or title

            if key not in seen_sql:
                seen_sql.add(key)
                unique_suggestions.append(suggestion)

        # 限制数量
        return unique_suggestions[:max_suggestions]

    except Exception as e:
        logger.error(f"过滤和排序建议失败: {str(e)}")
        return suggestions[:max_suggestions]

async def summarize_table_structures(table_structures: Dict[str, Any]) -> Dict[str, Any]:
    """总结表结构信息"""
    try:
        summary = {
            "total_tables": len(table_structures),
            "tables_with_structure": 0,
            "total_columns": 0,
            "column_types": {},
            "tables": []
        }

        for table_name, table_info in table_structures.items():
            table_summary = {
                "name": table_name,
                "column_count": 0,
                "has_structure": False,
                "column_types": []
            }

            if table_info.get("structure"):
                summary["tables_with_structure"] += 1
                table_summary["has_structure"] = True

                structure = table_info["structure"]
                table_summary["column_count"] = len(structure)
                summary["total_columns"] += len(structure)

                for column in structure:
                    col_type = column.get("Type", "").lower()
                    table_summary["column_types"].append(col_type)

                    # 统计列类型
                    base_type = col_type.split("(")[0]  # 去掉长度限制
                    summary["column_types"][base_type] = summary["column_types"].get(base_type, 0) + 1

            summary["tables"].append(table_summary)

        return summary

    except Exception as e:
        logger.error(f"总结表结构失败: {str(e)}")
        return {"error": str(e)}

# 辅助函数
async def get_database_tables(database: str) -> List[str]:
    """获取数据库中的所有表"""
    try:
        client = get_mindsdb_client()
        query = f"SHOW TABLES FROM {database}"
        result = await client.query(query)

        if result:
            return [row.get("Tables_in_" + database, "") for row in result if row]
        return []

    except Exception as e:
        logger.error(f"获取数据库表列表失败: {str(e)}")
        return []

async def get_detailed_table_info(database: str, table_name: str) -> Dict[str, Any]:
    """获取表的详细信息"""
    try:
        client = get_mindsdb_client()

        # 获取表结构
        structure_query = f"DESCRIBE {database}.{table_name}"
        structure = await client.query(structure_query)

        # 获取表统计信息
        stats_query = f"SELECT COUNT(*) as row_count FROM {database}.{table_name}"
        stats = await client.query(stats_query)

        # 获取样本数据
        sample_query = f"SELECT * FROM {database}.{table_name} LIMIT 10"
        sample_data = await client.query(sample_query)

        return {
            "table_name": table_name,
            "database": database,
            "structure": structure,
            "row_count": stats[0]["row_count"] if stats else 0,
            "sample_data": sample_data,
            "column_count": len(structure) if structure else 0,
            "analyzed_at": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取表详细信息失败: {str(e)}")
        return {"error": str(e)}

async def generate_table_specific_suggestions(
    table_info: Dict[str, Any],
    analysis_depth: str
) -> List[Dict[str, Any]]:
    """生成表特定的查询建议"""
    suggestions = []

    if not table_info.get("structure"):
        return suggestions

    table_name = table_info["table_name"]
    database = table_info["database"]
    structure = table_info["structure"]
    row_count = table_info.get("row_count", 0)

    # 基础建议
    suggestions.extend([
        {
            "type": "table_overview",
            "title": f"查看{table_name}表概览",
            "sql": f"SELECT * FROM {database}.{table_name} LIMIT 20;",
            "description": "查看表的基本数据结构和内容"
        },
        {
            "type": "table_stats",
            "title": f"获取{table_name}表统计信息",
            "sql": f"SELECT COUNT(*) as total_rows FROM {database}.{table_name};",
            "description": f"当前表共有{row_count}行数据"
        }
    ])

    # 深度分析建议
    if analysis_depth in ["standard", "deep"]:
        for column in structure:
            col_name = column.get("Field", "")
            col_type = column.get("Type", "").lower()

            if "int" in col_type or "decimal" in col_type:
                suggestions.append({
                    "type": "column_analysis",
                    "title": f"分析{col_name}字段的数值分布",
                    "sql": f"SELECT MIN({col_name}), MAX({col_name}), AVG({col_name}), COUNT(DISTINCT {col_name}) FROM {database}.{table_name};",
                    "description": f"分析{col_name}字段的统计特征"
                })

    return suggestions

async def generate_query_patterns(tables: List[str], pattern_type: str) -> List[Dict[str, Any]]:
    """生成查询模式"""
    patterns = []

    if pattern_type == "common":
        patterns = [
            {
                "name": "基础查询",
                "description": "查看表的基本数据",
                "example": "SELECT * FROM table_name LIMIT 10;"
            },
            {
                "name": "统计查询",
                "description": "统计表中的记录数量",
                "example": "SELECT COUNT(*) FROM table_name;"
            },
            {
                "name": "分组统计",
                "description": "按字段分组统计",
                "example": "SELECT column_name, COUNT(*) FROM table_name GROUP BY column_name;"
            }
        ]

    return patterns
