"""
Agent测试和监控API
提供Text2SQL Agent测试和性能监控的REST接口
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
import logging
from datetime import timedelta

try:
    from ..core.agent_test_service import AgentTestService, TestCase
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.agent_test_service import AgentTestService, TestCase
try:
    from ..core.agent_performance_monitor import get_performance_monitor, AgentTestStatus
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.agent_performance_monitor import get_performance_monitor, AgentTestStatus
try:
    from ..core.mindsdb_client import MindsDBClient
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.mindsdb_client import MindsDBClient
try:
    from ..core.api_utils import create_success_response, create_error_response
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.api_utils import create_success_response, create_error_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/agent-testing", tags=["Agent测试和监控"])

# Pydantic模型定义
class TestAgentRequest(BaseModel):
    """测试Agent请求"""
    agent_name: str = Field(..., description="Agent名称")
    test_query: str = Field(..., description="测试查询")
    test_case_id: Optional[str] = Field(None, description="测试用例ID")
    timeout: Optional[float] = Field(30.0, description="超时时间（秒）")

class RunTestSuiteRequest(BaseModel):
    """运行测试套件请求"""
    agent_name: str = Field(..., description="Agent名称")
    test_case_ids: Optional[List[str]] = Field(None, description="测试用例ID列表")
    tags: Optional[List[str]] = Field(None, description="按标签过滤")

class CreateTestCaseRequest(BaseModel):
    """创建测试用例请求"""
    name: str = Field(..., description="测试用例名称")
    description: str = Field(..., description="描述")
    test_query: str = Field(..., description="测试查询")
    expected_sql_pattern: Optional[str] = Field(None, description="期望的SQL模式")
    expected_result_count: Optional[int] = Field(None, description="期望的结果数量")
    tags: Optional[List[str]] = Field(default=[], description="标签")
    created_by: Optional[str] = Field(None, description="创建者")

class UpdateTestCaseRequest(BaseModel):
    """更新测试用例请求"""
    name: Optional[str] = Field(None, description="测试用例名称")
    description: Optional[str] = Field(None, description="描述")
    test_query: Optional[str] = Field(None, description="测试查询")
    expected_sql_pattern: Optional[str] = Field(None, description="期望的SQL模式")
    expected_result_count: Optional[int] = Field(None, description="期望的结果数量")
    tags: Optional[List[str]] = Field(None, description="标签")

class PerformanceQueryRequest(BaseModel):
    """性能查询请求"""
    metric_name: Optional[str] = Field(None, description="指标名称")
    time_range_hours: Optional[int] = Field(24, description="时间范围（小时）")
    agent_name: Optional[str] = Field(None, description="Agent名称")

# 依赖注入
def get_test_service() -> AgentTestService:
    """获取测试服务实例"""
    mindsdb_client = MindsDBClient()
    return AgentTestService(mindsdb_client)

@router.post("/test")
async def test_agent(
    request: TestAgentRequest,
    test_service: AgentTestService = Depends(get_test_service)
):
    """测试单个Agent"""
    try:
        logger.info(f"测试Agent: {request.agent_name}")
        
        result = await test_service.test_agent(
            agent_name=request.agent_name,
            test_query=request.test_query,
            test_case_id=request.test_case_id,
            timeout=request.timeout
        )
        
        return create_success_response({
            "test_result": result.to_dict()
        })
        
    except Exception as e:
        logger.error(f"测试Agent失败: {e}")
        return create_error_response(f"测试Agent失败: {str(e)}", 500)

@router.post("/test-suite")
async def run_test_suite(
    request: RunTestSuiteRequest,
    background_tasks: BackgroundTasks,
    test_service: AgentTestService = Depends(get_test_service)
):
    """运行测试套件"""
    try:
        logger.info(f"运行测试套件: {request.agent_name}")
        
        # 在后台运行测试套件
        async def run_suite():
            try:
                results = await test_service.run_test_suite(
                    agent_name=request.agent_name,
                    test_case_ids=request.test_case_ids,
                    tags=request.tags
                )
                logger.info(f"测试套件完成: {request.agent_name}, {len(results)}个测试")
            except Exception as e:
                logger.error(f"测试套件执行失败: {e}")
        
        background_tasks.add_task(run_suite)
        
        return create_success_response({
            "message": f"测试套件已启动: {request.agent_name}",
            "status": "running"
        })
        
    except Exception as e:
        logger.error(f"启动测试套件失败: {e}")
        return create_error_response(f"启动测试套件失败: {str(e)}", 500)

@router.get("/results/{agent_name}")
async def get_test_results(
    agent_name: str,
    limit: int = 100,
    test_service: AgentTestService = Depends(get_test_service)
):
    """获取Agent测试结果"""
    try:
        monitor = get_performance_monitor()
        results = monitor.get_recent_test_results(agent_name=agent_name, limit=limit)
        
        return create_success_response({
            "agent_name": agent_name,
            "test_results": results,
            "total_results": len(results)
        })
        
    except Exception as e:
        logger.error(f"获取测试结果失败: {e}")
        return create_error_response(f"获取测试结果失败: {str(e)}", 500)

@router.get("/statistics/{agent_name}")
async def get_agent_statistics(
    agent_name: str,
    test_service: AgentTestService = Depends(get_test_service)
):
    """获取Agent统计信息"""
    try:
        monitor = get_performance_monitor()
        stats = monitor.get_agent_statistics(agent_name)
        
        if not stats:
            return create_error_response(f"Agent统计信息不存在: {agent_name}", 404)
        
        return create_success_response({
            "agent_name": agent_name,
            "statistics": stats
        })
        
    except Exception as e:
        logger.error(f"获取Agent统计信息失败: {e}")
        return create_error_response(f"获取Agent统计信息失败: {str(e)}", 500)

@router.get("/performance/summary")
async def get_performance_summary(
    time_range_hours: int = 24,
    test_service: AgentTestService = Depends(get_test_service)
):
    """获取性能摘要"""
    try:
        monitor = get_performance_monitor()
        time_range = timedelta(hours=time_range_hours)
        summary = monitor.get_agent_performance_summary(time_range)
        
        return create_success_response({
            "performance_summary": summary
        })
        
    except Exception as e:
        logger.error(f"获取性能摘要失败: {e}")
        return create_error_response(f"获取性能摘要失败: {str(e)}", 500)

@router.get("/performance/metrics")
async def get_performance_metrics(
    metric_name: Optional[str] = None,
    time_range_hours: int = 24,
    test_service: AgentTestService = Depends(get_test_service)
):
    """获取性能指标"""
    try:
        monitor = get_performance_monitor()
        time_range = timedelta(hours=time_range_hours)
        metrics = monitor.get_performance_metrics(metric_name, time_range)
        
        return create_success_response({
            "metrics": metrics,
            "time_range_hours": time_range_hours
        })
        
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        return create_error_response(f"获取性能指标失败: {str(e)}", 500)

@router.get("/performance/prometheus")
async def get_prometheus_metrics(
    test_service: AgentTestService = Depends(get_test_service)
):
    """获取Prometheus格式的指标"""
    try:
        monitor = get_performance_monitor()
        prometheus_data = monitor.export_metrics_prometheus_format()
        
        return create_success_response({
            "prometheus_metrics": prometheus_data
        })
        
    except Exception as e:
        logger.error(f"获取Prometheus指标失败: {e}")
        return create_error_response(f"获取Prometheus指标失败: {str(e)}", 500)

@router.post("/test-cases")
async def create_test_case(
    request: CreateTestCaseRequest,
    test_service: AgentTestService = Depends(get_test_service)
):
    """创建测试用例"""
    try:
        test_case = test_service.create_test_case(
            name=request.name,
            description=request.description,
            test_query=request.test_query,
            expected_sql_pattern=request.expected_sql_pattern,
            expected_result_count=request.expected_result_count,
            tags=request.tags,
            created_by=request.created_by
        )
        
        return create_success_response({
            "test_case": test_case.to_dict(),
            "message": f"测试用例创建成功: {test_case.name}"
        })
        
    except Exception as e:
        logger.error(f"创建测试用例失败: {e}")
        return create_error_response(f"创建测试用例失败: {str(e)}", 500)

@router.get("/test-cases")
async def list_test_cases(
    tags: Optional[str] = None,
    test_service: AgentTestService = Depends(get_test_service)
):
    """列出测试用例"""
    try:
        tag_list = tags.split(',') if tags else None
        test_cases = test_service.list_test_cases(tags=tag_list)
        
        return create_success_response({
            "test_cases": [case.to_dict() for case in test_cases],
            "total_cases": len(test_cases)
        })
        
    except Exception as e:
        logger.error(f"列出测试用例失败: {e}")
        return create_error_response(f"列出测试用例失败: {str(e)}", 500)

@router.get("/test-cases/{test_case_id}")
async def get_test_case(
    test_case_id: str,
    test_service: AgentTestService = Depends(get_test_service)
):
    """获取测试用例详情"""
    try:
        test_case = test_service.get_test_case(test_case_id)
        
        if not test_case:
            return create_error_response(f"测试用例不存在: {test_case_id}", 404)
        
        return create_success_response({
            "test_case": test_case.to_dict()
        })
        
    except Exception as e:
        logger.error(f"获取测试用例失败: {e}")
        return create_error_response(f"获取测试用例失败: {str(e)}", 500)

@router.put("/test-cases/{test_case_id}")
async def update_test_case(
    test_case_id: str,
    request: UpdateTestCaseRequest,
    test_service: AgentTestService = Depends(get_test_service)
):
    """更新测试用例"""
    try:
        updates = {}
        if request.name is not None:
            updates["name"] = request.name
        if request.description is not None:
            updates["description"] = request.description
        if request.test_query is not None:
            updates["test_query"] = request.test_query
        if request.expected_sql_pattern is not None:
            updates["expected_sql_pattern"] = request.expected_sql_pattern
        if request.expected_result_count is not None:
            updates["expected_result_count"] = request.expected_result_count
        if request.tags is not None:
            updates["tags"] = request.tags
        
        test_case = test_service.update_test_case(test_case_id, updates)
        
        if not test_case:
            return create_error_response(f"测试用例不存在: {test_case_id}", 404)
        
        return create_success_response({
            "test_case": test_case.to_dict(),
            "message": f"测试用例更新成功: {test_case.name}"
        })
        
    except Exception as e:
        logger.error(f"更新测试用例失败: {e}")
        return create_error_response(f"更新测试用例失败: {str(e)}", 500)

@router.delete("/test-cases/{test_case_id}")
async def delete_test_case(
    test_case_id: str,
    test_service: AgentTestService = Depends(get_test_service)
):
    """删除测试用例"""
    try:
        success = test_service.delete_test_case(test_case_id)
        
        if success:
            return create_success_response({
                "message": f"测试用例删除成功: {test_case_id}"
            })
        else:
            return create_error_response(f"测试用例不存在: {test_case_id}", 404)
        
    except Exception as e:
        logger.error(f"删除测试用例失败: {e}")
        return create_error_response(f"删除测试用例失败: {str(e)}", 500)

@router.get("/health")
async def get_testing_system_health():
    """获取测试系统健康状态"""
    try:
        monitor = get_performance_monitor()
        
        # 获取最近1小时的统计
        summary = monitor.get_agent_performance_summary(timedelta(hours=1))
        
        # 计算健康评分
        health_score = 100
        if summary["total_tests"] > 0:
            if summary["overall_success_rate"] < 0.9:
                health_score -= 30
            if summary["avg_response_time"] > 10:
                health_score -= 20
        
        status = "healthy" if health_score >= 80 else "degraded" if health_score >= 60 else "unhealthy"
        
        return create_success_response({
            "status": status,
            "health_score": health_score,
            "recent_summary": summary,
            "timestamp": monitor.performance_monitor._get_current_time().isoformat() if hasattr(monitor, 'performance_monitor') else None
        })
        
    except Exception as e:
        logger.error(f"获取测试系统健康状态失败: {e}")
        return create_error_response(f"获取测试系统健康状态失败: {str(e)}", 500)
