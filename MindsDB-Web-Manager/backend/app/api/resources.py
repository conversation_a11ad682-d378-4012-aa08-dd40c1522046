"""
资源管理API端点
提供资源的查询、注册、权限检查等功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import logging

from core.auth_middleware import get_current_active_user, require_admin_permission
from core.auth_models import User
from core.database import get_db
from core.api_response import APIResponseBuilder
from core.resource_manager import ResourceType, ActionType, get_resource_manager
from core.resource_registry import get_resource_registry
from core.authorization_middleware import get_authorization_checker

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/resources", tags=["resources"])

# 请求模型
class CreateResourceRequest(BaseModel):
    """创建资源请求"""
    resource_type: str = Field(..., description="资源类型")
    resource_id: str = Field(..., min_length=1, max_length=100, description="资源ID")
    name: str = Field(..., min_length=1, max_length=200, description="资源名称")
    description: Optional[str] = Field(None, max_length=500, description="资源描述")
    allowed_actions: List[str] = Field(default_factory=list, description="允许的操作列表")
    parent_resource_id: Optional[str] = Field(None, description="父资源ID")

class CheckPermissionRequest(BaseModel):
    """权限检查请求"""
    resource_id: str = Field(..., description="资源ID")
    action: str = Field(..., description="操作类型")
    additional_context: Optional[Dict[str, Any]] = Field(None, description="附加上下文")

# 响应模型
class ResourceResponse(BaseModel):
    """资源响应"""
    id: str
    type: str
    name: str
    description: Optional[str]
    allowed_actions: List[str]
    has_parent: bool
    parent_id: Optional[str]

@router.get("/summary", response_model=dict)
async def get_resource_summary(
    current_user: User = Depends(require_admin_permission())
):
    """获取资源摘要"""
    try:
        registry = get_resource_registry()
        summary = registry.get_resource_summary()
        
        return APIResponseBuilder.success(
            data=summary,
            message="获取资源摘要成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取资源摘要失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取资源摘要失败",
            error_code="GET_RESOURCE_SUMMARY_ERROR"
        ).dict()

@router.get("/types", response_model=dict)
async def list_resource_types(
    current_user: User = Depends(get_current_active_user)
):
    """获取资源类型列表"""
    try:
        resource_types = []
        for resource_type in ResourceType:
            resource_types.append({
                "value": resource_type.value,
                "name": resource_type.name,
                "description": f"{resource_type.value}类型的资源"
            })
        
        return APIResponseBuilder.success(
            data={
                "resource_types": resource_types,
                "count": len(resource_types)
            },
            message="获取资源类型列表成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取资源类型列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取资源类型列表失败",
            error_code="LIST_RESOURCE_TYPES_ERROR"
        ).dict()

@router.get("/actions", response_model=dict)
async def list_action_types(
    current_user: User = Depends(get_current_active_user)
):
    """获取操作类型列表"""
    try:
        action_types = []
        for action_type in ActionType:
            action_types.append({
                "value": action_type.value,
                "name": action_type.name,
                "description": f"{action_type.value}操作"
            })
        
        return APIResponseBuilder.success(
            data={
                "action_types": action_types,
                "count": len(action_types)
            },
            message="获取操作类型列表成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取操作类型列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取操作类型列表失败",
            error_code="LIST_ACTION_TYPES_ERROR"
        ).dict()

@router.get("/", response_model=dict)
async def list_resources(
    resource_type: Optional[str] = Query(None, description="资源类型过滤"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: User = Depends(get_current_active_user)
):
    """获取资源列表"""
    try:
        resource_manager = get_resource_manager()
        
        if resource_type:
            # 验证资源类型
            try:
                rt = ResourceType(resource_type)
                resources = resource_manager.get_resources_by_type(rt)
            except ValueError:
                return APIResponseBuilder.error(
                    message=f"无效的资源类型: {resource_type}",
                    error_code="INVALID_RESOURCE_TYPE"
                ).dict()
        else:
            resources = resource_manager.get_all_resources()
        
        # 分页
        total = len(resources)
        paginated_resources = resources[skip:skip + limit]
        
        resources_data = []
        for resource in paginated_resources:
            resources_data.append({
                "id": resource.full_resource_id,
                "type": resource.resource_type.value,
                "resource_id": resource.resource_id,
                "name": resource.name,
                "description": resource.description,
                "allowed_actions": [action.value for action in resource.allowed_actions],
                "has_parent": resource.parent_resource is not None,
                "parent_id": resource.parent_resource.full_resource_id if resource.parent_resource else None
            })
        
        return APIResponseBuilder.success(
            data={
                "resources": resources_data,
                "total": total,
                "skip": skip,
                "limit": limit,
                "resource_type": resource_type
            },
            message="获取资源列表成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取资源列表失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取资源列表失败",
            error_code="LIST_RESOURCES_ERROR"
        ).dict()

@router.get("/{resource_id}", response_model=dict)
async def get_resource(
    resource_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """获取资源详情"""
    try:
        resource_manager = get_resource_manager()
        resource = resource_manager.get_resource(resource_id)
        
        if not resource:
            return APIResponseBuilder.error(
                message="资源不存在",
                error_code="RESOURCE_NOT_FOUND"
            ).dict()
        
        return APIResponseBuilder.success(
            data={
                "id": resource.full_resource_id,
                "type": resource.resource_type.value,
                "resource_id": resource.resource_id,
                "name": resource.name,
                "description": resource.description,
                "allowed_actions": [action.value for action in resource.allowed_actions],
                "has_parent": resource.parent_resource is not None,
                "parent_id": resource.parent_resource.full_resource_id if resource.parent_resource else None,
                "metadata": resource.metadata
            },
            message="获取资源详情成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取资源详情失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取资源详情失败",
            error_code="GET_RESOURCE_ERROR"
        ).dict()

@router.post("/", response_model=dict)
async def create_resource(
    resource_data: CreateResourceRequest,
    current_user: User = Depends(require_admin_permission())
):
    """创建资源"""
    try:
        # 验证资源类型
        try:
            resource_type = ResourceType(resource_data.resource_type)
        except ValueError:
            return APIResponseBuilder.error(
                message=f"无效的资源类型: {resource_data.resource_type}",
                error_code="INVALID_RESOURCE_TYPE"
            ).dict()
        
        # 验证操作类型
        allowed_actions = set()
        for action_str in resource_data.allowed_actions:
            try:
                action = ActionType(action_str)
                allowed_actions.add(action)
            except ValueError:
                return APIResponseBuilder.error(
                    message=f"无效的操作类型: {action_str}",
                    error_code="INVALID_ACTION_TYPE"
                ).dict()
        
        # 创建资源
        registry = get_resource_registry()
        success = registry.register_dynamic_resource(
            resource_type=resource_type,
            resource_id=resource_data.resource_id,
            name=resource_data.name,
            description=resource_data.description,
            allowed_actions=allowed_actions,
            parent_resource_id=resource_data.parent_resource_id
        )
        
        if success:
            return APIResponseBuilder.success(
                data={
                    "resource_id": f"{resource_type.value}:{resource_data.resource_id}",
                    "type": resource_type.value,
                    "name": resource_data.name
                },
                message="资源创建成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message="资源创建失败",
                error_code="CREATE_RESOURCE_FAILED"
            ).dict()
        
    except Exception as e:
        logger.error(f"创建资源失败: {str(e)}")
        return APIResponseBuilder.error(
            message="创建资源失败",
            error_code="CREATE_RESOURCE_ERROR"
        ).dict()

@router.delete("/{resource_id}", response_model=dict)
async def delete_resource(
    resource_id: str,
    current_user: User = Depends(require_admin_permission())
):
    """删除资源"""
    try:
        resource_manager = get_resource_manager()
        
        # 检查资源是否存在
        resource = resource_manager.get_resource(resource_id)
        if not resource:
            return APIResponseBuilder.error(
                message="资源不存在",
                error_code="RESOURCE_NOT_FOUND"
            ).dict()
        
        # 删除资源
        success = resource_manager.unregister_resource(resource_id)
        
        if success:
            return APIResponseBuilder.success(
                message="资源删除成功"
            ).dict()
        else:
            return APIResponseBuilder.error(
                message="资源删除失败",
                error_code="DELETE_RESOURCE_FAILED"
            ).dict()
        
    except Exception as e:
        logger.error(f"删除资源失败: {str(e)}")
        return APIResponseBuilder.error(
            message="删除资源失败",
            error_code="DELETE_RESOURCE_ERROR"
        ).dict()

@router.post("/check-permission", response_model=dict)
async def check_permission(
    permission_data: CheckPermissionRequest,
    current_user: User = Depends(get_current_active_user)
):
    """检查权限"""
    try:
        # 验证操作类型
        try:
            action = ActionType(permission_data.action)
        except ValueError:
            return APIResponseBuilder.error(
                message=f"无效的操作类型: {permission_data.action}",
                error_code="INVALID_ACTION_TYPE"
            ).dict()
        
        # 检查权限
        checker = get_authorization_checker()
        has_permission = checker.check_resource_access(
            user=current_user,
            resource_id=permission_data.resource_id,
            action=action,
            additional_context=permission_data.additional_context
        )
        
        return APIResponseBuilder.success(
            data={
                "has_permission": has_permission,
                "user_id": current_user.id,
                "username": current_user.username,
                "resource_id": permission_data.resource_id,
                "action": permission_data.action,
                "is_superuser": current_user.is_superuser
            },
            message="权限检查完成"
        ).dict()
        
    except Exception as e:
        logger.error(f"权限检查失败: {str(e)}")
        return APIResponseBuilder.error(
            message="权限检查失败",
            error_code="CHECK_PERMISSION_ERROR"
        ).dict()

@router.get("/user/accessible", response_model=dict)
async def get_user_accessible_resources(
    resource_type: str = Query(..., description="资源类型"),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户可访问的资源"""
    try:
        # 验证资源类型
        try:
            rt = ResourceType(resource_type)
        except ValueError:
            return APIResponseBuilder.error(
                message=f"无效的资源类型: {resource_type}",
                error_code="INVALID_RESOURCE_TYPE"
            ).dict()
        
        # 获取可访问的资源
        checker = get_authorization_checker()
        accessible_resources = checker.get_user_accessible_resources(current_user, rt)
        
        return APIResponseBuilder.success(
            data={
                "user_id": current_user.id,
                "username": current_user.username,
                "resource_type": resource_type,
                "accessible_resources": accessible_resources,
                "count": len(accessible_resources),
                "is_superuser": current_user.is_superuser
            },
            message="获取用户可访问资源成功"
        ).dict()
        
    except Exception as e:
        logger.error(f"获取用户可访问资源失败: {str(e)}")
        return APIResponseBuilder.error(
            message="获取用户可访问资源失败",
            error_code="GET_USER_ACCESSIBLE_RESOURCES_ERROR"
        ).dict()
