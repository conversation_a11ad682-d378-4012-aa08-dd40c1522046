"""
智能分析API
提供数据分析和Agent智能生成功能
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.mindsdb_client import mindsdb_client
from core.ai_generator import ai_generator, DataAnalysis, AgentRecommendation
from core.unicode_handler import UnicodeHandler

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/analytics", tags=["analytics"])

@router.post("/analyze-table", response_model=Dict[str, Any])
async def analyze_table_structure(table_info: Dict[str, Any]):
    """
    分析表结构并生成数据洞察
    
    Args:
        table_info: 表信息，包含表名、列信息、样本数据等
        
    Returns:
        数据分析结果
    """
    try:
        logger.info(f"分析表结构: {table_info.get('name', 'unknown')}")
        
        # 执行数据分析
        analysis = ai_generator.analyze_table_structure(table_info)
        
        return {
            "success": True,
            "data": {
                "table_name": analysis.table_name,
                "column_count": analysis.column_count,
                "row_count": analysis.row_count,
                "data_types": analysis.data_types,
                "business_domain": analysis.business_domain,
                "complexity_score": analysis.complexity_score,
                "recommended_prompts": analysis.recommended_prompts
            },
            "message": "表结构分析完成"
        }
        
    except Exception as e:
        logger.error(f"分析表结构失败: {e}")
        raise HTTPException(status_code=500, detail=f"分析表结构失败: {str(e)}")

@router.post("/generate-agents", response_model=Dict[str, Any])
async def generate_agent_recommendations(
    database_name: str,
    table_name: str,
    analysis_data: Optional[Dict[str, Any]] = None
):
    """
    基于数据结构生成Agent推荐
    
    Args:
        database_name: 数据库名称
        table_name: 表名称
        analysis_data: 可选的预分析数据
        
    Returns:
        Agent推荐列表
    """
    try:
        logger.info(f"生成Agent推荐: {database_name}.{table_name}")
        
        # 如果没有提供分析数据，先获取表信息
        if not analysis_data:
            # 获取表结构
            columns_query = f"DESCRIBE {database_name}.{table_name}"
            columns_result = mindsdb_client.execute_query(columns_query)
            
            # 获取样本数据
            preview_query = f"SELECT * FROM {database_name}.{table_name} LIMIT 5"
            preview_result = mindsdb_client.execute_query(preview_query)
            
            table_info = {
                "name": table_name,
                "columns": columns_result.get('data', []) if columns_result else [],
                "sample_data": preview_result.get('data', []) if preview_result else []
            }
            
            # 执行数据分析
            analysis = ai_generator.analyze_table_structure(table_info)
        else:
            # 使用提供的分析数据
            analysis = DataAnalysis(
                table_name=analysis_data.get('table_name', table_name),
                column_count=analysis_data.get('column_count', 0),
                row_count=analysis_data.get('row_count', 0),
                data_types=analysis_data.get('data_types', {}),
                business_domain=analysis_data.get('business_domain', 'analytics'),
                complexity_score=analysis_data.get('complexity_score', 0.5),
                recommended_prompts=analysis_data.get('recommended_prompts', [])
            )
        
        # 生成Agent推荐
        recommendations = ai_generator.generate_agent_recommendations(analysis)
        
        # 转换为字典格式
        recommendations_data = []
        for rec in recommendations:
            recommendations_data.append({
                "name": rec.name,
                "description": rec.description,
                "prompt_template": rec.prompt_template,
                "model_suggestion": rec.model_suggestion,
                "skills": rec.skills,
                "confidence_score": rec.confidence_score
            })
        
        return {
            "success": True,
            "data": {
                "analysis": {
                    "table_name": analysis.table_name,
                    "business_domain": analysis.business_domain,
                    "complexity_score": analysis.complexity_score
                },
                "recommendations": recommendations_data
            },
            "message": f"为表 '{table_name}' 生成了 {len(recommendations)} 个Agent推荐"
        }
        
    except Exception as e:
        logger.error(f"生成Agent推荐失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成Agent推荐失败: {str(e)}")

@router.get("/business-domains", response_model=Dict[str, Any])
async def get_business_domains():
    """
    获取支持的业务领域列表
    
    Returns:
        业务领域信息
    """
    try:
        domains = {
            "sales": {
                "name": "销售管理",
                "description": "销售数据分析、客户管理、业绩预测",
                "keywords": ["销售", "订单", "客户", "产品", "价格", "收入"]
            },
            "finance": {
                "name": "财务管理",
                "description": "财务分析、成本控制、投资评估",
                "keywords": ["财务", "账户", "交易", "支付", "预算", "成本"]
            },
            "hr": {
                "name": "人力资源",
                "description": "员工管理、绩效评估、人才分析",
                "keywords": ["员工", "薪资", "部门", "职位", "考勤", "绩效"]
            },
            "inventory": {
                "name": "库存管理",
                "description": "库存优化、供应链管理、采购计划",
                "keywords": ["库存", "商品", "仓库", "供应商", "采购", "配送"]
            },
            "marketing": {
                "name": "营销管理",
                "description": "营销分析、客户洞察、ROI优化",
                "keywords": ["营销", "广告", "推广", "活动", "渠道", "转化"]
            },
            "analytics": {
                "name": "数据分析",
                "description": "数据挖掘、统计分析、机器学习",
                "keywords": ["分析", "统计", "报告", "指标", "趋势", "预测"]
            }
        }
        
        return {
            "success": True,
            "data": domains,
            "message": "获取业务领域列表成功"
        }
        
    except Exception as e:
        logger.error(f"获取业务领域失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取业务领域失败: {str(e)}")

@router.post("/batch-generate", response_model=Dict[str, Any])
async def batch_generate_agents(
    database_name: str,
    table_names: List[str],
    max_agents_per_table: int = Query(2, ge=1, le=5, description="每个表最大Agent数量")
):
    """
    批量生成多个表的Agent推荐
    
    Args:
        database_name: 数据库名称
        table_names: 表名称列表
        max_agents_per_table: 每个表最大Agent数量
        
    Returns:
        批量生成结果
    """
    try:
        logger.info(f"批量生成Agent: {database_name}, 表数量: {len(table_names)}")
        
        batch_results = []
        
        for table_name in table_names:
            try:
                # 获取表信息
                columns_query = f"DESCRIBE {database_name}.{table_name}"
                columns_result = mindsdb_client.execute_query(columns_query)
                
                preview_query = f"SELECT * FROM {database_name}.{table_name} LIMIT 3"
                preview_result = mindsdb_client.execute_query(preview_query)
                
                table_info = {
                    "name": table_name,
                    "columns": columns_result.get('data', []) if columns_result else [],
                    "sample_data": preview_result.get('data', []) if preview_result else []
                }
                
                # 分析表结构
                analysis = ai_generator.analyze_table_structure(table_info)
                
                # 生成Agent推荐
                recommendations = ai_generator.generate_agent_recommendations(analysis)
                
                # 限制数量
                limited_recommendations = recommendations[:max_agents_per_table]
                
                # 转换格式
                recommendations_data = []
                for rec in limited_recommendations:
                    recommendations_data.append({
                        "name": rec.name,
                        "description": rec.description,
                        "prompt_template": rec.prompt_template,
                        "model_suggestion": rec.model_suggestion,
                        "skills": rec.skills,
                        "confidence_score": rec.confidence_score
                    })
                
                batch_results.append({
                    "table_name": table_name,
                    "business_domain": analysis.business_domain,
                    "complexity_score": analysis.complexity_score,
                    "recommendations": recommendations_data,
                    "success": True
                })
                
            except Exception as table_error:
                logger.warning(f"处理表 {table_name} 失败: {table_error}")
                batch_results.append({
                    "table_name": table_name,
                    "recommendations": [],
                    "success": False,
                    "error": str(table_error)
                })
        
        successful_count = sum(1 for result in batch_results if result["success"])
        total_agents = sum(len(result["recommendations"]) for result in batch_results)
        
        return {
            "success": True,
            "data": {
                "results": batch_results,
                "summary": {
                    "total_tables": len(table_names),
                    "successful_tables": successful_count,
                    "total_agents_generated": total_agents
                }
            },
            "message": f"批量生成完成，成功处理 {successful_count}/{len(table_names)} 个表，生成 {total_agents} 个Agent推荐"
        }
        
    except Exception as e:
        logger.error(f"批量生成Agent失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量生成Agent失败: {str(e)}")

@router.get("/prompt-templates/{domain}", response_model=Dict[str, Any])
async def get_prompt_templates(domain: str):
    """
    获取指定业务领域的Prompt模板
    
    Args:
        domain: 业务领域
        
    Returns:
        Prompt模板列表
    """
    try:
        templates = ai_generator.prompt_templates.get(domain)
        
        if not templates:
            raise HTTPException(status_code=404, detail=f"业务领域 '{domain}' 不存在")
        
        return {
            "success": True,
            "data": {
                "domain": domain,
                "templates": templates
            },
            "message": f"获取 '{domain}' 领域Prompt模板成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Prompt模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取Prompt模板失败: {str(e)}")
