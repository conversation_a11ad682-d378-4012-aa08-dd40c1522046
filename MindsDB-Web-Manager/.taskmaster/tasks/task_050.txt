# Task ID: 50
# Title: 悬浮窗口控制功能实现
# Status: pending
# Dependencies: 49
# Priority: high
# Description: 为任务管理悬浮窗口实现拖拽移动、调整大小、最小化、最大化和关闭（包括ESC键）等核心窗口控制功能。
# Details:
拖拽: 在窗口顶部添加一个可拖拽区域（例如标题栏），使用JavaScript监听`mousedown`, `mousemove`, `mouseup`事件来计算并更新窗口的`left`和`top`样式属性。调整大小: 在窗口边缘和角落添加可拖拽手柄，类似拖拽，监听鼠标事件来调整窗口的`width`和`height`。最小化/最大化/关闭: 在窗口标题栏添加对应的按钮。最小化可隐藏窗口或将其缩小到任务栏/屏幕边缘；最大化将窗口尺寸设置为视口大小；关闭隐藏窗口。ESC键关闭: 监听`keydown`事件，当`event.key`为'Escape'时触发关闭操作。

# Test Strategy:
手动拖拽窗口到屏幕不同位置，验证其移动是否流畅。尝试从不同边缘和角落调整窗口大小，验证尺寸变化是否正确。点击最小化、最大化、关闭按钮，验证功能是否按预期执行。在窗口打开时按下ESC键，验证窗口是否关闭。
