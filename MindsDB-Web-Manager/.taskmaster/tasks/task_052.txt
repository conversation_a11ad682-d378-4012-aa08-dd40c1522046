# Task ID: 52
# Title: iframe内容加载与基本交互
# Status: pending
# Dependencies: 49, 50
# Priority: high
# Description: 在悬浮窗口内部使用iframe加载`task-manager.html`的完整内容，确保其无边框显示并保持原有功能和样式。
# Details:
HTML: 在悬浮窗口的主内容区域内添加一个`<iframe src="task-manager.html"></iframe>`元素。CSS: 为iframe设置`border: none; width: 100%; height: 100%;`以确保无边框并完美适配父容器。JavaScript: 确保iframe加载完成后，`task-manager.html`内部的所有交互（如按钮点击、表单提交、数据加载显示）都能正常工作。

# Test Strategy:
验证`task-manager.html`是否成功加载到iframe中。检查iframe是否有边框，并验证其尺寸是否与悬浮窗口内容区域完美匹配。在iframe内部执行`task-manager.html`的所有核心功能（例如，创建新任务、查看任务列表、刷新任务状态），确保它们都能正常工作。检查`task-manager.html`的样式和布局在iframe中是否保持不变。
