# Task ID: 53
# Title: 窗口状态持久化与恢复
# Status: pending
# Dependencies: 50
# Priority: medium
# Description: 实现悬浮窗口的状态管理，利用`localStorage`记住用户的窗口大小和位置偏好，并在页面刷新后恢复，同时提供窗口重置功能。
# Details:
保存状态: 在窗口关闭、调整大小或移动时，将当前窗口的`width`, `height`, `left`, `top`等属性保存到`localStorage`中，例如：`localStorage.setItem('taskManagerWindowConfig', JSON.stringify({ width, height, left, top }));`。恢复状态: 在页面加载时，检查`localStorage`中是否存在保存的配置。如果存在，则读取并应用这些配置来设置悬浮窗口的初始大小和位置。重置功能: 提供一个按钮或选项，点击后清除`localStorage`中保存的配置，并将窗口恢复到默认的居中1200x800px状态。

# Test Strategy:
打开窗口，调整其大小和位置，然后刷新页面，验证窗口是否恢复到上次的状态。关闭窗口，刷新页面，再次打开窗口，验证其是否恢复到上次的状态。测试重置功能，验证窗口是否恢复到默认大小和位置。在不同浏览器中测试`localStorage`的兼容性。
