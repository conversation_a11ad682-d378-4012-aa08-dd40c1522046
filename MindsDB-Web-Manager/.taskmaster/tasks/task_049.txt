# Task ID: 49
# Title: 悬浮窗口基础结构与样式开发
# Status: pending
# Dependencies: None
# Priority: high
# Description: 实现MindsDB Web Manager中任务管理悬浮窗口的基础HTML结构和CSS样式，包括模态背景、毛玻璃效果、固定尺寸（1200x800px）和居中显示，并支持基本的响应式调整。
# Details:
HTML: 创建一个主容器`div`作为模态背景，内部包含一个`div`作为悬浮窗口。CSS: 模态背景应使用`position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); backdrop-filter: blur(5px); display: flex; justify-content: center; align-items: center; z-index: 1000;`。悬浮窗口应设置`width: 1200px; height: 800px; background-color: rgba(255,255,255,0.1); border-radius: 8px; box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); display: flex; flex-direction: column; overflow: hidden;`。使用媒体查询调整窗口最大宽度和高度，确保在小屏幕上也能良好显示。

# Test Strategy:
在不同分辨率的桌面浏览器中测试窗口是否居中显示，尺寸是否为1200x800px。验证毛玻璃背景效果是否正确应用。检查窗口是否覆盖整个页面且为模态。
