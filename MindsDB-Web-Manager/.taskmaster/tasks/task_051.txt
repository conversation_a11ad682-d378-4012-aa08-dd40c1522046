# Task ID: 51
# Title: 任务管理标签页集成
# Status: pending
# Dependencies: 49
# Priority: high
# Description: 在`unified-web-manager.html`的主导航标签页中添加一个名为“🔧 任务管理”的入口，并确保点击时能正确打开悬浮窗口。
# Details:
HTML: 在现有导航结构中，找到“项目目录”和“权限管理”标签页的DOM位置，插入新的`<li>`或`<a>`元素，包含“🔧 任务管理”文本和图标。CSS: 确保新标签页的样式与现有标签页（如字体、颜色、hover效果）保持一致。JavaScript: 为新标签页元素添加事件监听器。当点击时，调用一个函数来显示（或创建并显示）任务管理悬浮窗口。考虑使用`accesskey`属性或JavaScript事件监听器实现键盘快捷键访问。

# Test Strategy:
验证“🔧 任务管理”标签页是否出现在正确的位置，且样式与周围标签页一致。点击标签页，验证悬浮窗口是否成功打开。测试键盘快捷键是否能打开窗口。
