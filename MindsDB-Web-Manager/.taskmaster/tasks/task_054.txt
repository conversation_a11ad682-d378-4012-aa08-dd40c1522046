# Task ID: 54
# Title: 跨框架安全通信机制
# Status: pending
# Dependencies: 52
# Priority: medium
# Description: 实施基于`postMessage` API的跨框架通信机制，确保`unified-web-manager.html`与`task-manager.html`之间安全地交换信息，并包含消息验证和错误边界处理。
# Details:
发送消息: 父页面向iframe发送：`iframeElement.contentWindow.postMessage(message, targetOrigin);`；iframe向父页面发送：`window.parent.postMessage(message, targetOrigin);`。接收消息: 监听`message`事件：`window.addEventListener('message', event => { /* handle message */ });`。安全验证: 始终检查`event.origin`以确保消息来自预期的源，例如：`if (event.origin !== "http://expected-domain.com") return;`。消息结构: 定义清晰的消息结构（例如，包含`type`和`payload`字段），以便于解析和处理不同类型的消息。错误边界: 在消息处理逻辑中加入`try-catch`块，防止单个消息处理失败影响整个应用。

# Test Strategy:
模拟父页面向iframe发送消息（例如，通知iframe刷新数据），验证iframe是否正确响应。模拟iframe向父页面发送消息（例如，任务完成通知），验证父页面是否正确接收和处理。尝试从非预期源发送消息，验证消息是否被安全策略拒绝。在通信过程中引入错误数据或格式，验证错误处理机制是否正常工作。
