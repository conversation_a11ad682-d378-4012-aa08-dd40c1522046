{"master": {"tasks": [{"id": 49, "title": "悬浮窗口基础结构与样式开发", "description": "实现MindsDB Web Manager中任务管理悬浮窗口的基础HTML结构和CSS样式，包括模态背景、毛玻璃效果、固定尺寸（1200x800px）和居中显示，并支持基本的响应式调整。", "details": "HTML: 创建一个主容器`div`作为模态背景，内部包含一个`div`作为悬浮窗口。CSS: 模态背景应使用`position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); backdrop-filter: blur(5px); display: flex; justify-content: center; align-items: center; z-index: 1000;`。悬浮窗口应设置`width: 1200px; height: 800px; background-color: rgba(255,255,255,0.1); border-radius: 8px; box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); display: flex; flex-direction: column; overflow: hidden;`。使用媒体查询调整窗口最大宽度和高度，确保在小屏幕上也能良好显示。", "testStrategy": "在不同分辨率的桌面浏览器中测试窗口是否居中显示，尺寸是否为1200x800px。验证毛玻璃背景效果是否正确应用。检查窗口是否覆盖整个页面且为模态。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 50, "title": "悬浮窗口控制功能实现", "description": "为任务管理悬浮窗口实现拖拽移动、调整大小、最小化、最大化和关闭（包括ESC键）等核心窗口控制功能。", "details": "拖拽: 在窗口顶部添加一个可拖拽区域（例如标题栏），使用JavaScript监听`mousedown`, `mousemove`, `mouseup`事件来计算并更新窗口的`left`和`top`样式属性。调整大小: 在窗口边缘和角落添加可拖拽手柄，类似拖拽，监听鼠标事件来调整窗口的`width`和`height`。最小化/最大化/关闭: 在窗口标题栏添加对应的按钮。最小化可隐藏窗口或将其缩小到任务栏/屏幕边缘；最大化将窗口尺寸设置为视口大小；关闭隐藏窗口。ESC键关闭: 监听`keydown`事件，当`event.key`为'Escape'时触发关闭操作。", "testStrategy": "手动拖拽窗口到屏幕不同位置，验证其移动是否流畅。尝试从不同边缘和角落调整窗口大小，验证尺寸变化是否正确。点击最小化、最大化、关闭按钮，验证功能是否按预期执行。在窗口打开时按下ESC键，验证窗口是否关闭。", "priority": "high", "dependencies": [49], "status": "done", "subtasks": []}, {"id": 51, "title": "任务管理标签页集成", "description": "在`unified-web-manager.html`的主导航标签页中添加一个名为“🔧 任务管理”的入口，并确保点击时能正确打开悬浮窗口。", "details": "HTML: 在现有导航结构中，找到“项目目录”和“权限管理”标签页的DOM位置，插入新的`<li>`或`<a>`元素，包含“🔧 任务管理”文本和图标。CSS: 确保新标签页的样式与现有标签页（如字体、颜色、hover效果）保持一致。JavaScript: 为新标签页元素添加事件监听器。当点击时，调用一个函数来显示（或创建并显示）任务管理悬浮窗口。考虑使用`accesskey`属性或JavaScript事件监听器实现键盘快捷键访问。", "testStrategy": "验证“🔧 任务管理”标签页是否出现在正确的位置，且样式与周围标签页一致。点击标签页，验证悬浮窗口是否成功打开。测试键盘快捷键是否能打开窗口。", "priority": "high", "dependencies": [49], "status": "done", "subtasks": []}, {"id": 52, "title": "iframe内容加载与基本交互", "description": "在悬浮窗口内部使用iframe加载`task-manager.html`的完整内容，确保其无边框显示并保持原有功能和样式。", "details": "HTML: 在悬浮窗口的主内容区域内添加一个`<iframe src=\"task-manager.html\"></iframe>`元素。CSS: 为iframe设置`border: none; width: 100%; height: 100%;`以确保无边框并完美适配父容器。JavaScript: 确保iframe加载完成后，`task-manager.html`内部的所有交互（如按钮点击、表单提交、数据加载显示）都能正常工作。", "testStrategy": "验证`task-manager.html`是否成功加载到iframe中。检查iframe是否有边框，并验证其尺寸是否与悬浮窗口内容区域完美匹配。在iframe内部执行`task-manager.html`的所有核心功能（例如，创建新任务、查看任务列表、刷新任务状态），确保它们都能正常工作。检查`task-manager.html`的样式和布局在iframe中是否保持不变。", "priority": "high", "dependencies": [49, 50], "status": "done", "subtasks": []}, {"id": 53, "title": "窗口状态持久化与恢复", "description": "实现悬浮窗口的状态管理，利用`localStorage`记住用户的窗口大小和位置偏好，并在页面刷新后恢复，同时提供窗口重置功能。", "details": "保存状态: 在窗口关闭、调整大小或移动时，将当前窗口的`width`, `height`, `left`, `top`等属性保存到`localStorage`中，例如：`localStorage.setItem('taskManagerWindowConfig', JSON.stringify({ width, height, left, top }));`。恢复状态: 在页面加载时，检查`localStorage`中是否存在保存的配置。如果存在，则读取并应用这些配置来设置悬浮窗口的初始大小和位置。重置功能: 提供一个按钮或选项，点击后清除`localStorage`中保存的配置，并将窗口恢复到默认的居中1200x800px状态。", "testStrategy": "打开窗口，调整其大小和位置，然后刷新页面，验证窗口是否恢复到上次的状态。关闭窗口，刷新页面，再次打开窗口，验证其是否恢复到上次的状态。测试重置功能，验证窗口是否恢复到默认大小和位置。在不同浏览器中测试`localStorage`的兼容性。", "priority": "medium", "dependencies": [50], "status": "done", "subtasks": []}, {"id": 54, "title": "跨框架安全通信机制", "description": "实施基于`postMessage` API的跨框架通信机制，确保`unified-web-manager.html`与`task-manager.html`之间安全地交换信息，并包含消息验证和错误边界处理。", "details": "发送消息: 父页面向iframe发送：`iframeElement.contentWindow.postMessage(message, targetOrigin);`；iframe向父页面发送：`window.parent.postMessage(message, targetOrigin);`。接收消息: 监听`message`事件：`window.addEventListener('message', event => { /* handle message */ });`。安全验证: 始终检查`event.origin`以确保消息来自预期的源，例如：`if (event.origin !== \"http://expected-domain.com\") return;`。消息结构: 定义清晰的消息结构（例如，包含`type`和`payload`字段），以便于解析和处理不同类型的消息。错误边界: 在消息处理逻辑中加入`try-catch`块，防止单个消息处理失败影响整个应用。", "testStrategy": "模拟父页面向iframe发送消息（例如，通知iframe刷新数据），验证iframe是否正确响应。模拟iframe向父页面发送消息（例如，任务完成通知），验证父页面是否正确接收和处理。尝试从非预期源发送消息，验证消息是否被安全策略拒绝。在通信过程中引入错误数据或格式，验证错误处理机制是否正常工作。", "priority": "medium", "dependencies": [52], "status": "done", "subtasks": []}, {"id": 55, "title": "用户体验动画与加载指示器", "description": "为悬浮窗口的打开和关闭操作添加平滑的动画效果，并在iframe内容加载时显示一个加载状态指示器。", "details": "打开/关闭动画: 使用CSS `transition`通过改变`opacity`, `transform`（例如`scale`或`translate`）属性来实现动画。通过JavaScript添加/移除CSS类来触发动画。加载指示器: 在iframe容器上方或内部添加一个加载动画（例如，旋转的spinner）。在iframe开始加载时显示指示器（监听`loadstart`或在设置`src`后立即显示），在`iframe.onload`事件触发时隐藏指示器。", "testStrategy": "打开和关闭悬浮窗口，观察动画效果是否流畅自然，没有卡顿。模拟网络延迟（例如，通过浏览器开发者工具），验证加载指示器是否在iframe内容加载期间正确显示，并在加载完成后消失。检查动画和指示器在不同浏览器中的兼容性。", "priority": "medium", "dependencies": [49, 52], "status": "done", "subtasks": []}, {"id": 56, "title": "移动端适配与错误处理", "description": "确保任务管理悬浮窗口及其内部的iframe内容在移动设备上具有良好的响应式表现和触摸友好交互。同时，实现健壮的错误处理和用户反馈机制。", "details": "移动端适配: 针对小屏幕设备使用CSS媒体查询调整悬浮窗口的尺寸、位置和布局，使其占据更多屏幕空间或变为全屏模式。使用Flexbox/Grid确保内部布局在不同屏幕尺寸下都能自适应。确保拖拽、调整大小等操作在触摸屏上也能流畅响应。错误处理: 当`task-manager.html`无法加载时，显示友好的错误消息给用户。在`task-manager.html`内部，确保数据加载或任务操作失败时，有明确的错误提示。将错误信息记录到控制台或后端日志系统。用户反馈: 提供操作成功/失败的Toast通知或消息框，以及表单验证错误提示。", "testStrategy": "在不同尺寸的移动设备模拟器或真实设备上测试悬浮窗口的显示和交互。验证窗口在旋转屏幕方向时是否正确调整布局。模拟`task-manager.html`加载失败，验证错误提示是否正确显示。在`task-manager.html`中模拟任务创建失败或数据加载失败，验证用户反馈是否清晰。", "priority": "low", "dependencies": [49, 52, 55], "status": "done", "subtasks": []}], "metadata": {"created": "2025-07-19T08:30:13.357Z", "updated": "2025-07-26T09:05:22.680Z", "description": "Tasks for master context"}}}