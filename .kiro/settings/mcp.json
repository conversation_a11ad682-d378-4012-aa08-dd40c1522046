{"mcpServers": {"Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": ["resolve-library-id", "get-library-docs"]}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": []}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "disabled": false, "autoApprove": []}, "apple-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@Dhravya/apple-mcp", "--key", "018d0705-7565-40b8-820a-2e4179355ee9", "--profile", "sleepy-whitefish-PAdXPN"], "disabled": false, "autoApprove": []}, "taskmaster-ai": {"command": "node", "args": ["/Users/<USER>/dev/claude-task-master/mcp-server/server.js"], "env": {"OPENROUTER_API_KEY": "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", "DEEPSEEK_API_KEY": "***********************************", "GOOGLE_API_KEY": "88888880", "XAI_API_KEY": "************************************************************************************", "OPENAI_API_KEY": "sk-proj-88888880"}, "disabled": false, "autoApprove": ["rules", "initialize_project", "models", "get_tasks", "add_tag", "parse_prd", "expand_task", "set_task_status", "get_task", "generate", "add_subtask", "next_task", "update_subtask", "research"]}, "jira-mcp": {"command": "npx", "args": ["-y", "mcpo", "--", "python", "-m", "jira_mcp_server"], "env": {"JIRA_API_TOKEN": "YOUR_JIRA_API_TOKEN", "JIRA_USER_EMAIL": "<EMAIL>", "JIRA_URL": "https://your-instance.atlassian.net", "JIRA_PROJECT_KEY": "YOUR_PROJECT"}, "disabled": false, "autoApprove": ["jira_create_issue", "jira_update_issue", "jira_get_issues", "jira_sync_tasks"]}}}