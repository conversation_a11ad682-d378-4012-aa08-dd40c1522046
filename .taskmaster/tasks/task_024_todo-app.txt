# Task ID: 24
# Title: 前端页面：在系统工具区域添加模型管理器入口
# Status: pending
# Dependencies: None
# Priority: medium
# Description: 在`frontend/unified-web-manager.html`的系统工具区域添加一个新的按钮，作为模型管理器的入口。
# Details:
在`frontend/unified-web-manager.html`中找到现有的系统工具栏（例如Agent管理、ML引擎管理器按钮所在区域）。
- 在现有按钮旁边插入一个新的`<a>`或`<button>`元素，文本为“🧠 模型管理器”。
- 使用现有的`.tool-btn`等CSS类，确保样式与现有按钮保持一致。
- 为新按钮添加一个唯一的ID，以便在JavaScript中绑定事件。

# Test Strategy:
在浏览器中访问`unified-web-manager.html`，验证新的“🧠 模型管理器”按钮是否正确显示在系统工具栏中，且样式与现有按钮一致，没有破坏现有布局。
