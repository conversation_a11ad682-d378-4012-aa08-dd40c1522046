# Task ID: 23
# Title: 前端脚本：实现模型删除功能
# Status: pending
# Dependencies: 16, 21
# Priority: medium
# Description: 在`frontend/js/pages/model-manager.js`中实现模型删除功能，包括确认提示和调用后端API。
# Details:
在`model-manager.js`中：
- 为每个模型卡片添加“删除”按钮。
- 点击删除按钮时，弹出确认模态框（复用现有`showModal()`）。
- 用户确认后，调用`DELETE /api/models/:name`（任务16）API。
- 成功后从列表中移除模型卡片并显示成功通知，失败则显示错误通知。

# Test Strategy:
功能测试：
- 点击模型卡片的“删除”按钮，验证确认模态框正确弹出。
- 确认删除后，验证模型从列表中移除，且后端数据被删除。
- 验证删除失败时，能显示正确的错误通知。
