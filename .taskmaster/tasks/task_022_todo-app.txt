# Task ID: 22
# Title: 前端脚本：实现模型创建与编辑功能
# Status: pending
# Dependencies: 16, 21
# Priority: medium
# Description: 在`frontend/js/pages/model-manager.js`中实现模型创建和编辑功能的模态框及相关逻辑，复用现有表单样式和`showModal()`函数。
# Details:
在`model-manager.js`中：
- 实现`showCreateModelModal()`和`showEditModelModal(model)`函数，用于显示模型创建/编辑的模态框。
- 模态框内部使用现有表单样式和组件（如输入框、下拉菜单）。
- 提交表单时，调用`POST /api/models/create`或`PUT /api/models/:name`（任务16）API。
- 成功后刷新模型列表并显示成功通知，失败则显示错误通知。

# Test Strategy:
功能测试：
- 点击“创建模型”按钮，验证模态框正确弹出，表单样式正确。
- 填写表单并提交，验证模型能成功创建并显示在列表中。
- 点击现有模型的“编辑”按钮，验证模态框弹出并预填充数据。
- 修改模型信息并提交，验证模型能成功更新。
- 验证在创建/编辑失败时，能显示正确的错误通知。
