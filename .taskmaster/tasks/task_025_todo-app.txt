# Task ID: 25
# Title: 前端脚本：实现模型管理器页面加载逻辑
# Status: pending
# Dependencies: 20, 24
# Priority: medium
# Description: 在`frontend/js/pages/unified-web-manager.js`中实现`showModelManager()`函数，用于处理模型管理器按钮的点击事件，并加载模型管理器页面。
# Details:
在`frontend/js/pages/unified-web-manager.js`中：
- 找到或创建处理系统工具按钮点击事件的逻辑。
- 为任务24中添加的模型管理器按钮绑定点击事件。
- 在事件处理函数中，实现`showModelManager()`函数，该函数应负责加载`model-manager.html`的内容到主内容区域，并初始化`model-manager.js`中的逻辑（例如调用`loadModels()`）。
- 确保页面切换流畅，并显示加载状态。

# Test Strategy:
在浏览器中访问`unified-web-manager.html`：
- 点击“🧠 模型管理器”按钮，验证页面内容区域切换到模型管理器界面。
- 验证模型列表能正确加载并显示。
- 验证页面切换速度和加载状态显示符合用户体验要求。
