{"todo-app": {"tasks": [{"id": 16, "title": "后端API：实现核心模型管理端点", "description": "在现有`backend/proxy-server.js`中新增模型管理相关的API端点，包括列出、获取详情、创建、更新和删除模型的功能。这些API将作为前端模型管理器的数据来源。", "details": "在`backend/proxy-server.js`中实现以下RESTful API：\n- `GET /api/models/list`: 执行`SHOW MODELS`查询，返回所有模型实例列表。\n- `GET /api/models/:name`: 执行`SHOW MODELS WHERE name = :name`或类似查询，获取特定模型详情。\n- `POST /api/models/create`: 接收模型配置，执行`CREATE MODEL`语句。\n- `PUT /api/models/:name`: 接收模型更新配置，执行`ALTER MODEL`语句。\n- `DELETE /api/models/:name`: 执行`DROP MODEL`语句。\n所有端点应复用现有SQL查询代理模式和错误处理机制。", "testStrategy": "使用Postman或cURL测试每个API端点：\n- 验证`GET /api/models/list`能正确返回模型列表。\n- 验证`POST /api/models/create`能成功创建模型，并能通过`GET`查询到。\n- 验证`PUT /api/models/:name`能成功更新模型配置。\n- 验证`DELETE /api/models/:name`能成功删除模型。\n- 验证错误处理机制在无效请求时能返回正确的错误码和信息。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 17, "title": "后端API：新增ML引擎依赖检查端点", "description": "在`backend/proxy-server.js`中新增一个API端点，用于检查特定ML引擎是否被任何模型实例所依赖，以支持后续的引擎删除依赖检查功能。", "details": "在`backend/proxy-server.js`中实现`GET /api/ml-engines/dependencies/:name`端点。\n该端点应执行MindsDB SQL查询，例如`SELECT name FROM mindsdb.models WHERE engine = ':name'`，以检查是否有模型依赖于指定的ML引擎。返回一个包含依赖模型列表的响应，如果无依赖则返回空列表。", "testStrategy": "使用Postman或cURL测试：\n- 创建一个依赖于某个引擎的模型，然后调用`/api/ml-engines/dependencies/:engine_name`，验证能返回该模型。\n- 删除所有依赖于某个引擎的模型，然后调用`/api/ml-engines/dependencies/:engine_name`，验证返回空列表。\n- 验证对不存在的引擎的查询能正确处理。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 18, "title": "后端API：增强ML引擎删除依赖检查", "description": "修改现有`backend/proxy-server.js`中`DELETE /api/ml-engines/:name`端点的逻辑，在删除ML引擎之前，先调用依赖检查API，如果存在依赖则阻止删除。", "details": "在`backend/proxy-server.js`中找到处理`DELETE /api/ml-engines/:name`的逻辑。\n在执行`DROP ML_ENGINE`之前，调用内部或外部的`/api/ml-engines/dependencies/:name`端点（或直接复用其逻辑）。\n如果依赖检查返回有依赖模型，则返回一个错误响应（例如409 Conflict），并附带提示信息，阻止删除操作。否则，继续执行删除。", "testStrategy": "功能测试：\n- 创建一个ML引擎，并基于它创建一个模型。尝试删除该引擎，验证删除操作被阻止，并收到正确的错误提示。\n- 删除所有依赖模型后，再次尝试删除该引擎，验证删除成功。\n- 验证删除不存在的引擎时，系统能正确响应。", "priority": "high", "dependencies": [17], "status": "done", "subtasks": []}, {"id": 19, "title": "后端API：增强ML引擎列表筛选逻辑", "description": "修改后端处理`SHOW ML_ENGINES`查询的逻辑，使其能够根据前端请求筛选只返回基础ML引擎，而非所有ML相关组件。", "details": "在`backend/proxy-server.js`中，找到处理`SHOW ML_ENGINES`查询的逻辑。根据前端请求中可能携带的参数（例如`type=base`），在执行MindsDB SQL查询后，对结果进行过滤，只返回那些代表基础API连接器的引擎，排除模型实例。这可能需要根据MindsDB的内部元数据来区分引擎和模型。", "testStrategy": "使用Postman或cURL测试：\n- 调用`GET /api/ml-engines/list`（或现有对应端点），验证在不带筛选参数时返回所有引擎和模型。\n- 调用`GET /api/ml-engines/list?type=base`（或约定好的筛选参数），验证只返回基础ML引擎，不包含模型实例。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 20, "title": "前端页面：创建模型管理器HTML骨架", "description": "创建新的`frontend/pages/model-manager.html`文件，复用现有`unified-web-manager.html`的结构、CSS类和布局，为模型管理器提供独立的页面骨架。", "details": "复制`unified-web-manager.html`的通用头部、导航、系统工具区域和内容区域的HTML结构。移除或注释掉与ML引擎管理、Agent管理等不相关的特定内容，只保留通用的布局和样式引用。确保引入必要的CSS文件和JavaScript文件（包括即将创建的`model-manager.js`）。", "testStrategy": "在浏览器中直接打开`model-manager.html`，验证页面结构和基本样式是否正确加载，且与现有Web Manager页面风格一致。检查控制台是否有HTML或CSS相关的错误。", "priority": "medium", "dependencies": [], "status": "done", "subtasks": []}, {"id": 21, "title": "前端脚本：实现模型管理器核心列表逻辑", "description": "创建`frontend/js/pages/model-manager.js`文件，实现模型列表的加载、显示和基本交互逻辑，复用Agent管理器的卡片布局风格。", "details": "在`frontend/js/pages/model-manager.js`中：\n- 编写`loadModels()`函数，调用`GET /api/models/list`（任务16）获取模型数据。\n- 编写`createModelCard(model)`函数，参考Agent管理器的卡片布局和样式，动态生成模型卡片，显示模型名称、引擎、状态等信息。\n- 将生成的卡片渲染到`model-manager.html`的内容区域。\n- 实现基本的筛选和搜索功能（复用现有组件设计）。\n- 集成现有的`showNotification()`和错误处理机制。", "testStrategy": "在浏览器中访问模型管理器页面：\n- 验证页面加载后能正确显示所有模型实例的卡片。\n- 验证模型卡片样式与Agent卡片一致。\n- 验证筛选和搜索功能能正确过滤模型列表。\n- 检查网络请求，确保`GET /api/models/list`被正确调用且数据返回正常。", "priority": "high", "dependencies": [16, 20], "status": "done", "subtasks": []}, {"id": 22, "title": "前端脚本：实现模型创建与编辑功能", "description": "在`frontend/js/pages/model-manager.js`中实现模型创建和编辑功能的模态框及相关逻辑，复用现有表单样式和`showModal()`函数。", "details": "在`model-manager.js`中：\n- 实现`showCreateModelModal()`和`showEditModelModal(model)`函数，用于显示模型创建/编辑的模态框。\n- 模态框内部使用现有表单样式和组件（如输入框、下拉菜单）。\n- 提交表单时，调用`POST /api/models/create`或`PUT /api/models/:name`（任务16）API。\n- 成功后刷新模型列表并显示成功通知，失败则显示错误通知。\n<info added on 2025-07-28T14:41:32.072Z>\n【重要更新】基于任务28的经验，增强错误处理和鲁棒性：\n- 增加对本地文件模式（`window.location.protocol === 'file:'`）的检测。\n- 在进行API调用前，检查网络连接状态。\n- 当后端API不可用时，提供模拟数据和功能演示模式。\n- 所有API调用都应使用`try-catch`块进行错误捕获。\n- 确保在发生错误时，向用户显示清晰友好的提示信息。\n- 即使在API调用失败或网络异常的情况下，也要确保UI界面能正常加载和显示，避免空白或崩溃。\n</info added on 2025-07-28T14:41:32.072Z>", "testStrategy": "功能测试：\n- 点击“创建模型”按钮，验证模态框正确弹出，表单样式正确。\n- 填写表单并提交，验证模型能成功创建并显示在列表中。\n- 点击现有模型的“编辑”按钮，验证模态框弹出并预填充数据。\n- 修改模型信息并提交，验证模型能成功更新。\n- 验证在创建/编辑失败时，能显示正确的错误通知。", "priority": "medium", "dependencies": [16, 21], "status": "done", "subtasks": []}, {"id": 23, "title": "前端脚本：实现模型删除功能", "description": "在`frontend/js/pages/model-manager.js`中实现模型删除功能，包括确认提示和调用后端API。", "details": "在`model-manager.js`中：\n- 为每个模型卡片添加“删除”按钮。\n- 点击删除按钮时，弹出确认模态框（复用现有`showModal()`）。\n- 用户确认后，调用`DELETE /api/models/:name`（任务16）API。\n- 成功后从列表中移除模型卡片并显示成功通知，失败则显示错误通知。\n<info added on 2025-07-28T14:41:50.752Z>\n【重要更新】基于任务28的修复经验，增强错误处理和鲁棒性：\n- 增加对本地文件模式（`window.location.protocol === 'file:'`）的检测，并在进行API调用前检查API的可用性。\n- 当后端API不可用时，提供模拟删除功能演示模式。\n- 所有API调用都应使用`try-catch`块进行错误捕获。\n- 确保在发生错误时，向用户显示清晰友好的提示信息。\n- 即使在API调用失败或网络异常的情况下，也要确保删除确认对话框能正常显示。\n</info added on 2025-07-28T14:41:50.752Z>", "testStrategy": "功能测试：\n- 点击模型卡片的“删除”按钮，验证确认模态框正确弹出。\n- 确认删除后，验证模型从列表中移除，且后端数据被删除。\n- 验证删除失败时，能显示正确的错误通知。", "priority": "medium", "dependencies": [16, 21], "status": "done", "subtasks": []}, {"id": 24, "title": "前端页面：在系统工具区域添加模型管理器入口", "description": "在`frontend/unified-web-manager.html`的系统工具区域添加一个新的按钮，作为模型管理器的入口。", "details": "在`frontend/unified-web-manager.html`中找到现有的系统工具栏（例如Agent管理、ML引擎管理器按钮所在区域）。\n- 在现有按钮旁边插入一个新的`<a>`或`<button>`元素，文本为“🧠 模型管理器”。\n- 使用现有的`.tool-btn`等CSS类，确保样式与现有按钮保持一致。\n- 为新按钮添加一个唯一的ID，以便在JavaScript中绑定事件。", "testStrategy": "在浏览器中访问`unified-web-manager.html`，验证新的“🧠 模型管理器”按钮是否正确显示在系统工具栏中，且样式与现有按钮一致，没有破坏现有布局。", "priority": "medium", "dependencies": [], "status": "done", "subtasks": []}, {"id": 25, "title": "前端脚本：实现模型管理器页面加载逻辑", "description": "在`frontend/js/pages/unified-web-manager.js`中实现`showModelManager()`函数，用于处理模型管理器按钮的点击事件，并加载模型管理器页面。", "details": "在`frontend/js/pages/unified-web-manager.js`中：\n- 找到或创建处理系统工具按钮点击事件的逻辑。\n- 为任务24中添加的模型管理器按钮绑定点击事件。\n- 在事件处理函数中，实现`showModelManager()`函数，该函数应负责加载`model-manager.html`的内容到主内容区域，并初始化`model-manager.js`中的逻辑（例如调用`loadModels()`）。\n- 确保页面切换流畅，并显示加载状态。\n<info added on 2025-07-28T14:42:16.826Z>\n【重要更新】基于任务24和任务28的经验，请重新评估并调整模型管理器页面加载逻辑：\n- 任务24已实现模型管理器入口按钮。\n- **重要：** 当前`openModelManager()`函数已在新标签页中打开模型管理器。这与本任务原计划的“加载`model-manager.html`内容到主内容区域”的目标存在冲突。\n- **请决定并实现以下两种方案之一：**\n    1.  **继续实现内嵌显示：** 如果选择此方案，`showModelManager()`函数需负责将`model-manager.html`内容加载到主内容区域。在此基础上，**必须**添加全面的错误处理和对本地文件模式（`window.location.protocol === 'file:'`）的支持，确保页面加载逻辑在任何环境下（包括网络受限或离线情况）都能正常工作，参考任务28的鲁棒性要求。\n    2.  **保留在新标签页中打开的行为：** 如果选择此方案，则本任务原定的内嵌加载目标不再适用。请将本任务的范围调整为优化新标签页打开的体验，或将其标记为不适用/完成，并将精力转移到其他模型管理器相关功能上。\n- 在开始具体实现前，请与相关负责人确认最终的页面加载方式。\n</info added on 2025-07-28T14:42:16.826Z>", "testStrategy": "在浏览器中访问`unified-web-manager.html`：\n- 点击“🧠 模型管理器”按钮，验证页面内容区域切换到模型管理器界面。\n- 验证模型列表能正确加载并显示。\n- 验证页面切换速度和加载状态显示符合用户体验要求。", "priority": "medium", "dependencies": [20, 24], "status": "done", "subtasks": []}, {"id": 26, "title": "前端重构：ML引擎管理器功能增强", "description": "对`frontend/js/pages/unified-web-manager.js`中的ML引擎管理功能进行渐进式重构，包括筛选基础引擎、增强删除逻辑和显示模型依赖数量。", "details": "在`frontend/js/pages/unified-web-manager.js`中：\n- **修改`loadAIEngines()`函数**：调用后端API时，添加参数以筛选只显示基础ML引擎（利用任务19的后端增强）。\n- **增强`deleteEngine()`函数**：在调用后端删除API之前，先调用后端依赖检查API（任务18），如果存在依赖则阻止删除并提示用户。\n- **扩展`createEngineCard()`函数**：在引擎卡片上显示该引擎所依赖的模型数量（通过调用任务16的模型列表API进行统计），并添加一个“查看模型”链接，点击后可跳转到模型管理器并筛选出相关模型。", "testStrategy": "功能测试：\n- 访问ML引擎管理器页面，验证只显示基础ML引擎，不显示模型实例。\n- 创建一个模型依赖于某个引擎，尝试删除该引擎，验证删除被阻止并提示依赖。\n- 验证引擎卡片上能正确显示依赖的模型数量，并点击“查看模型”链接能跳转到模型管理器并显示相关模型。", "priority": "high", "dependencies": [18, 19, 16], "status": "done", "subtasks": []}, {"id": 27, "title": "前端优化：Agent创建流程模型选择", "description": "优化Agent创建流程，将现有Agent创建表单中的ML引擎选择下拉菜单改为模型选择下拉菜单，数据源从ML引擎列表切换为模型列表。", "details": "在`unified-web-manager.js`中找到Agent创建相关的代码。\n- 定位Agent创建表单中的引擎选择下拉菜单。\n- 修改该下拉菜单的数据填充逻辑，不再调用`SHOW ML_ENGINES`，而是调用`GET /api/models/list`（任务16）获取模型列表。\n- 将获取到的模型名称填充到下拉菜单中，确保用户可以选择已存在的模型来创建Agent。\n- 保持现有表单结构和Agent创建API调用不变，仅修改数据源。", "testStrategy": "功能测试：\n- 访问Agent创建界面，验证模型选择下拉菜单中显示的是模型名称，而不是引擎名称。\n- 验证下拉菜单中的模型列表与模型管理器中显示的模型列表一致。\n- 选择一个模型并成功创建一个Agent，验证Agent功能正常。\n- 验证现有Agent功能不受影响。", "priority": "high", "dependencies": [16], "status": "done", "subtasks": []}, {"id": 28, "title": "紧急修复：主页功能回归问题", "description": "修复unified-web-manager.html主页面上仪表板、Agent列表和数据源管理功能因近期模型管理器集成导致的加载和显示问题。", "details": "1. 问题排查: 深入检查frontend/unified-web-manager.html和frontend/js/pages/unified-web-manager.js中与模型管理器入口（任务24）和页面加载逻辑（任务25）相关的最新修改。特别关注这些修改是否意外地影响了现有初始化函数、API调用或DOM操作。2. 仪表板数据加载: 验证仪表板（Dashboard）的数据加载API调用是否正常，数据渲染逻辑是否被破坏。检查相关JavaScript函数，确保它们在页面加载时正确执行。3. Agent列表加载: 检查Agent列表的API调用（例如GET /api/agents/list）和前端渲染逻辑。特别注意任务27对Agent相关代码的修改是否引入了副作用，影响了Agent列表的加载和显示。4. 数据源管理: 验证数据源管理功能的API调用和前端交互是否正常。5. API调用和错误处理: 使用浏览器开发者工具（Network Tab）监控所有API请求，检查是否有请求失败、响应格式错误或未被正确处理的错误。确保所有API调用都返回预期数据，并且前端能正确处理成功和失败情况。6. 代码回溯与隔离: 如果发现是新功能代码导致的问题，考虑暂时回溯或隔离相关代码，以恢复核心功能的正常运行。然后，重新审视新功能的集成方式，确保其不会干扰现有逻辑。7. 日志分析: 检查前端控制台和后端服务器日志，查找任何与加载失败或功能异常相关的错误信息。", "testStrategy": "1. 访问主页: 在浏览器中访问unified-web-manager.html。2. 仪表板验证: 验证仪表板区域是否正确加载并显示数据，所有图表和统计信息是否正常。3. Agent列表验证: 导航到Agent管理区域，验证Agent列表是否完整加载并正确渲染，可以进行排序、筛选等操作。尝试创建、编辑、删除Agent，确保功能正常。4. 数据源管理验证: 导航到数据源管理区域，验证数据源列表是否加载，可以进行添加、编辑、删除数据源等操作。5. 模型管理器入口验证: 验证模型管理器入口按钮（任务24）仍然存在且样式正常，但点击后不应影响主页面的其他功能。6. 控制台和网络监控: 打开浏览器开发者工具，检查控制台是否有JavaScript错误，网络请求是否有失败或异常响应。7. 回归测试: 确保在修复过程中没有引入新的问题，所有原有功能（包括ML引擎管理、系统工具等）均正常工作。", "status": "done", "dependencies": [24, 25, 26, 27], "priority": "high", "subtasks": []}, {"id": 29, "title": "模型管理器：8084端口统一服务集成", "description": "将本地模型管理器功能完全集成到8084端口的Node.js统一服务中，实现页面路由、功能适配、API调用更新及本地模式移除，完成从原型到生产环境的迁移。", "details": "此任务旨在将当前运行在本地文件模式下的模型管理器(frontend/pages/model-manager.html)及其配套脚本(model-manager.js)完全集成到8084端口的Node.js代理服务器中。\n\n1.  **Node.js路由集成：** 在8084端口的Node.js服务（例如`server.js`或相关路由文件）中，添加一个新的路由，例如`/model-manager`，使其能够正确地服务`frontend/pages/model-manager.html`文件及其所有相关静态资源（CSS, JS）。\n2.  **前端脚本适配：** 确保`frontend/js/pages/model-manager.js`中的所有功能在8084服务环境下正常工作。这包括但不限于模型列表加载、创建、编辑和删除操作。\n3.  **移除本地文件模式检测：** 识别并移除`model-manager.js`以及其内部函数（如`showCreateModelModal()`、`showEditModelModal()`、`deleteModel()`）中所有针对`window.location.protocol === 'file:'`的检测逻辑和相关的模拟数据/功能演示模式。所有API调用都应直接指向真实的后端服务。\n4.  **更新API配置：** 确保`model-manager.js`中所有模型管理相关的API调用（例如`/api/models/list`, `/api/models/create`, `/api/models/:name`等）都通过8084端口的Node.js代理服务器进行访问。这意味着前端请求应发送到`/api/...`，由Node.js服务转发到实际的后端API。\n5.  **入口按钮集成：** 验证`unified-web-manager.html`中的模型管理器入口按钮（由任务24和25实现）能够正确地导航到由8084服务提供的`/model-manager`页面。\n6.  **错误处理与鲁棒性：** 确保在API调用失败或网络异常时，前端能提供清晰的用户反馈，而不是依赖于本地模拟模式。", "testStrategy": "1.  **直接访问测试：** 在浏览器中直接访问`http://localhost:8084/model-manager`，验证页面是否正确加载，模型列表是否显示，且无任何本地文件模式相关的警告或错误。\n2.  **入口按钮测试：** 访问`http://localhost:8084/unified-web-manager.html`，点击“🧠 模型管理器”按钮，验证是否能正确跳转到`http://localhost:8084/model-manager`页面，且功能正常。\n3.  **CRUD功能测试：**\n    *   **创建模型：** 尝试创建新模型，验证模态框弹出、数据提交、模型在列表中显示，并通过浏览器开发者工具的网络面板确认API请求（POST /api/models/create）是通过8084端口发出的。\n    *   **读取模型：** 验证模型列表加载完整且正确，筛选和搜索功能正常工作，确认GET /api/models/list请求通过8084端口发出。\n    *   **更新模型：** 选择一个现有模型进行编辑，修改信息后提交，验证模型数据更新，并通过网络面板确认PUT /api/models/:name请求通过8084端口发出。\n    *   **删除模型：** 选择一个模型进行删除，确认删除提示，验证模型从列表中移除，并通过网络面板确认DELETE /api/models/:name请求通过8084端口发出。\n4.  **本地模式移除验证：** 检查`model-manager.js`及其相关函数，确保所有`if (window.location.protocol === 'file:')`的条件分支和模拟数据逻辑已被移除。\n5.  **稳定性与性能：** 在8084环境下，重复执行上述操作，观察页面加载速度、响应时间以及整体用户体验，确保没有明显的性能下降或不稳定现象。", "status": "done", "dependencies": [20, 21, 22, 23, 24, 25, 28], "priority": "high", "subtasks": []}], "metadata": {"created": "2025-07-28T12:41:04.193Z", "updated": "2025-07-28T15:58:27.450Z", "description": "Tasks for todo-app context"}}}