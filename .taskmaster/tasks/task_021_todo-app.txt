# Task ID: 21
# Title: 前端脚本：实现模型管理器核心列表逻辑
# Status: pending
# Dependencies: 16, 20
# Priority: high
# Description: 创建`frontend/js/pages/model-manager.js`文件，实现模型列表的加载、显示和基本交互逻辑，复用Agent管理器的卡片布局风格。
# Details:
在`frontend/js/pages/model-manager.js`中：
- 编写`loadModels()`函数，调用`GET /api/models/list`（任务16）获取模型数据。
- 编写`createModelCard(model)`函数，参考Agent管理器的卡片布局和样式，动态生成模型卡片，显示模型名称、引擎、状态等信息。
- 将生成的卡片渲染到`model-manager.html`的内容区域。
- 实现基本的筛选和搜索功能（复用现有组件设计）。
- 集成现有的`showNotification()`和错误处理机制。

# Test Strategy:
在浏览器中访问模型管理器页面：
- 验证页面加载后能正确显示所有模型实例的卡片。
- 验证模型卡片样式与Agent卡片一致。
- 验证筛选和搜索功能能正确过滤模型列表。
- 检查网络请求，确保`GET /api/models/list`被正确调用且数据返回正常。
