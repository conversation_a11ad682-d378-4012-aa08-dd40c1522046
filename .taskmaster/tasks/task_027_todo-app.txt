# Task ID: 27
# Title: 前端优化：Agent创建流程模型选择
# Status: pending
# Dependencies: 16
# Priority: high
# Description: 优化Agent创建流程，将现有Agent创建表单中的ML引擎选择下拉菜单改为模型选择下拉菜单，数据源从ML引擎列表切换为模型列表。
# Details:
在`unified-web-manager.js`中找到Agent创建相关的代码。
- 定位Agent创建表单中的引擎选择下拉菜单。
- 修改该下拉菜单的数据填充逻辑，不再调用`SHOW ML_ENGINES`，而是调用`GET /api/models/list`（任务16）获取模型列表。
- 将获取到的模型名称填充到下拉菜单中，确保用户可以选择已存在的模型来创建Agent。
- 保持现有表单结构和Agent创建API调用不变，仅修改数据源。

# Test Strategy:
功能测试：
- 访问Agent创建界面，验证模型选择下拉菜单中显示的是模型名称，而不是引擎名称。
- 验证下拉菜单中的模型列表与模型管理器中显示的模型列表一致。
- 选择一个模型并成功创建一个Agent，验证Agent功能正常。
- 验证现有Agent功能不受影响。
