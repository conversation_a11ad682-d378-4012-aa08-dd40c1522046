# Task ID: 18
# Title: 后端API：增强ML引擎删除依赖检查
# Status: pending
# Dependencies: 17
# Priority: high
# Description: 修改现有`backend/proxy-server.js`中`DELETE /api/ml-engines/:name`端点的逻辑，在删除ML引擎之前，先调用依赖检查API，如果存在依赖则阻止删除。
# Details:
在`backend/proxy-server.js`中找到处理`DELETE /api/ml-engines/:name`的逻辑。
在执行`DROP ML_ENGINE`之前，调用内部或外部的`/api/ml-engines/dependencies/:name`端点（或直接复用其逻辑）。
如果依赖检查返回有依赖模型，则返回一个错误响应（例如409 Conflict），并附带提示信息，阻止删除操作。否则，继续执行删除。

# Test Strategy:
功能测试：
- 创建一个ML引擎，并基于它创建一个模型。尝试删除该引擎，验证删除操作被阻止，并收到正确的错误提示。
- 删除所有依赖模型后，再次尝试删除该引擎，验证删除成功。
- 验证删除不存在的引擎时，系统能正确响应。
