# Task ID: 26
# Title: 前端重构：ML引擎管理器功能增强
# Status: pending
# Dependencies: 18, 19, 16
# Priority: high
# Description: 对`frontend/js/pages/unified-web-manager.js`中的ML引擎管理功能进行渐进式重构，包括筛选基础引擎、增强删除逻辑和显示模型依赖数量。
# Details:
在`frontend/js/pages/unified-web-manager.js`中：
- **修改`loadAIEngines()`函数**：调用后端API时，添加参数以筛选只显示基础ML引擎（利用任务19的后端增强）。
- **增强`deleteEngine()`函数**：在调用后端删除API之前，先调用后端依赖检查API（任务18），如果存在依赖则阻止删除并提示用户。
- **扩展`createEngineCard()`函数**：在引擎卡片上显示该引擎所依赖的模型数量（通过调用任务16的模型列表API进行统计），并添加一个“查看模型”链接，点击后可跳转到模型管理器并筛选出相关模型。

# Test Strategy:
功能测试：
- 访问ML引擎管理器页面，验证只显示基础ML引擎，不显示模型实例。
- 创建一个模型依赖于某个引擎，尝试删除该引擎，验证删除被阻止并提示依赖。
- 验证引擎卡片上能正确显示依赖的模型数量，并点击“查看模型”链接能跳转到模型管理器并显示相关模型。
