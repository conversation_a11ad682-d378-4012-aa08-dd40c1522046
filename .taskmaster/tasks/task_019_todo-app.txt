# Task ID: 19
# Title: 后端API：增强ML引擎列表筛选逻辑
# Status: pending
# Dependencies: None
# Priority: high
# Description: 修改后端处理`SHOW ML_ENGINES`查询的逻辑，使其能够根据前端请求筛选只返回基础ML引擎，而非所有ML相关组件。
# Details:
在`backend/proxy-server.js`中，找到处理`SHOW ML_ENGINES`查询的逻辑。根据前端请求中可能携带的参数（例如`type=base`），在执行MindsDB SQL查询后，对结果进行过滤，只返回那些代表基础API连接器的引擎，排除模型实例。这可能需要根据MindsDB的内部元数据来区分引擎和模型。

# Test Strategy:
使用Postman或cURL测试：
- 调用`GET /api/ml-engines/list`（或现有对应端点），验证在不带筛选参数时返回所有引擎和模型。
- 调用`GET /api/ml-engines/list?type=base`（或约定好的筛选参数），验证只返回基础ML引擎，不包含模型实例。
