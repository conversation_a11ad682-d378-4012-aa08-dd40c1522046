# Task ID: 17
# Title: 后端API：新增ML引擎依赖检查端点
# Status: pending
# Dependencies: None
# Priority: high
# Description: 在`backend/proxy-server.js`中新增一个API端点，用于检查特定ML引擎是否被任何模型实例所依赖，以支持后续的引擎删除依赖检查功能。
# Details:
在`backend/proxy-server.js`中实现`GET /api/ml-engines/dependencies/:name`端点。
该端点应执行MindsDB SQL查询，例如`SELECT name FROM mindsdb.models WHERE engine = ':name'`，以检查是否有模型依赖于指定的ML引擎。返回一个包含依赖模型列表的响应，如果无依赖则返回空列表。

# Test Strategy:
使用Postman或cURL测试：
- 创建一个依赖于某个引擎的模型，然后调用`/api/ml-engines/dependencies/:engine_name`，验证能返回该模型。
- 删除所有依赖于某个引擎的模型，然后调用`/api/ml-engines/dependencies/:engine_name`，验证返回空列表。
- 验证对不存在的引擎的查询能正确处理。
