# Task ID: 16
# Title: 后端API：实现核心模型管理端点
# Status: pending
# Dependencies: None
# Priority: high
# Description: 在现有`backend/proxy-server.js`中新增模型管理相关的API端点，包括列出、获取详情、创建、更新和删除模型的功能。这些API将作为前端模型管理器的数据来源。
# Details:
在`backend/proxy-server.js`中实现以下RESTful API：
- `GET /api/models/list`: 执行`SHOW MODELS`查询，返回所有模型实例列表。
- `GET /api/models/:name`: 执行`SHOW MODELS WHERE name = :name`或类似查询，获取特定模型详情。
- `POST /api/models/create`: 接收模型配置，执行`CREATE MODEL`语句。
- `PUT /api/models/:name`: 接收模型更新配置，执行`ALTER MODEL`语句。
- `DELETE /api/models/:name`: 执行`DROP MODEL`语句。
所有端点应复用现有SQL查询代理模式和错误处理机制。

# Test Strategy:
使用Postman或cURL测试每个API端点：
- 验证`GET /api/models/list`能正确返回模型列表。
- 验证`POST /api/models/create`能成功创建模型，并能通过`GET`查询到。
- 验证`PUT /api/models/:name`能成功更新模型配置。
- 验证`DELETE /api/models/:name`能成功删除模型。
- 验证错误处理机制在无效请求时能返回正确的错误码和信息。
